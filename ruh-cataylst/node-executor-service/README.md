# Node Executor Service

A highly dynamic and scalable system for processing messages with Kafka. This system allows you to add new components without any configuration or code changes to the core system. It includes robust security features and a modular architecture for easy extension.

## Overview

The Node Executor Service is designed to run multiple components in parallel, each listening on the same Kafka topic and filtering messages based on the `target_component` field. This architecture simplifies the Kafka topic structure and allows for easier management of the message flow in the system.

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Python 3.11+ (for local development)
- Poetry (for local development)

### Installation

#### Using Docker (Recommended for Production)

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/node-executor-service.git
   cd node-executor-service
   ```

2. Create a `.env` file from the example:
   ```bash
   cp .env.example .env
   ```

3. Start the services using Docker Compose:
   ```bash
   docker-compose up -d
   ```

4. Check the logs to ensure everything is running correctly:
   ```bash
   docker-compose logs -f node-executor
   ```

#### Local Development

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/node-executor-service.git
   cd node-executor-service
   ```

2. Install dependencies using Poetry:
   ```bash
   poetry install
   ```

3. Create a `.env` file from the example:
   ```bash
   cp .env.example .env
   ```

4. Start Kafka using Docker Compose:
   ```bash
   docker-compose up -d zookeeper kafka
   ```

5. Run the application:
   ```bash
   # On Windows
   .\run_auto.ps1

   # On Linux/macOS
   ./run_auto.sh
   ```

## Features

- **Zero Configuration**: Add new components without any configuration files or environment variables
- **Auto-Discovery**: Components are automatically discovered at runtime
- **Single Kafka Topic**: All components share a single Kafka topic for requests
- **Unified Interface**: All components implement the same simple interface
- **Scalable**: Can handle 1000+ components without any changes to the core system
- **Secure**: Comprehensive security features including URL validation, rate limiting, and content scanning
- **Auditable**: Built-in audit logging for security events
- **Extensible**: Easy to add new components with specialized functionality
- **Parallel Processing**: Multiple components can run in parallel

## Architecture

The system is built around a few core concepts:

1. **Components**: Self-contained units that process messages
2. **Component Registry**: Automatically discovers and registers components
3. **Component Manager**: Manages the lifecycle of components and their Kafka connections
4. **Shared Kafka Topics**: All components share the same set of topics:
   - `node-execution-request`: For all incoming requests
   - `node_results`: For all responses
   - `node-execution-request_dlq`: Dead letter queue for all failed messages

```mermaid
flowchart TB
    subgraph Client
        A[Client Application]
    end

    subgraph "Shared Messaging"
        B[Kafka Broker]
    end

    subgraph "Component System"
        C[Component Registry]
        D[Component Manager]
        E[Topic Generator]
        S[Security Manager]
        L[Audit Logger]
    end

    subgraph "Components"
        F1[API Component]
        F2[Document Component]
        F3[Split Text Component]
        F4[Update Data Component]
        F5[Select Data Component]
    end

    subgraph "External Systems"
        G[API Endpoints]
        H[Document Files]
        I[Text Processing]
        J[Data Storage]
    end

    subgraph "Security Layer"
        S1[URL Validation]
        S2[Rate Limiting]
        S3[Content Scanning]
        S4[Path Validation]
    end

    A -->|Send Request| B
    B -->|Component Topics| D
    C -->|Register| F1
    C -->|Register| F2
    C -->|Register| F3
    C -->|Register| F4
    C -->|Register| F5
    D -->|Manage| F1
    D -->|Manage| F2
    D -->|Manage| F3
    D -->|Manage| F4
    D -->|Manage| F5
    E -->|Generate| B

    S -->|Secure| F1
    S -->|Secure| F2
    S -->|Secure| F4
    S -->|Secure| F5

    S -->|Configure| S1
    S -->|Configure| S2
    S -->|Configure| S3
    S -->|Configure| S4

    F1 -->|Log Events| L
    F2 -->|Log Events| L
    F3 -->|Log Events| L
    F4 -->|Log Events| L
    F5 -->|Log Events| L

    F1 -->|Process| G
    F2 -->|Process| H
    F3 -->|Process| I
    F4 -->|Process| J
    F5 -->|Process| J

    F1 -->|Results| B
    F2 -->|Results| B
    F3 -->|Results| B
    F4 -->|Results| B
    F5 -->|Results| B
    B -->|Deliver Results| A

    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef tertiary fill:#bfb,stroke:#333,stroke-width:1px;
    classDef security fill:#fbb,stroke:#333,stroke-width:1px;
    class C,D,E,S,L primary;
    class A,B,F1,F2,F3,F4,F5 secondary;
    class G,H,I,J tertiary;
    class S1,S2,S3,S4 security;
```

## Included Components

### API Component

Executes HTTP requests to external APIs. This component handles various HTTP methods, headers, query parameters, and request bodies. Includes security features like URL validation and rate limiting.

### Document Component

Extracts content from document files. This component supports different file formats and can extract both content and metadata. Includes security features like path validation and content scanning.

### Split Text Component

Splits text into a list using a delimiter. This component supports various options like max_splits and include_delimiter.

### Update Data Component (UpdateDataExecutor)

Performs data manipulation operations including:
- Dictionary updates (shallow or deep merges)
- File operations (create, update, delete)
- Secure file handling with path validation

### Select Data Component (SelectDataComExec)

Performs data selection and filtering operations including:
- Extracting elements from lists or dictionaries
- File operations (get_all, get_by_id, filter, sort)
- Secure data access with validation

### Parse JSON Component (ParseJSONDataCompExecutor)

Converts JSON strings to Python objects:
- Parses JSON strings into dictionaries, lists, or primitive values
- Supports reading from files or direct string input
- Provides strict and non-strict parsing modes
- Includes security features for file access

### Combine Text Component (CombineTextCompExecutor)

Joins text inputs with a specified separator:
- Combines strings or lists of strings
- Supports multiple input sources
- Handles escape sequences in separators (\n, \t, \r)
- Automatically converts non-string inputs to strings

### Text Analysis Component

Performs text analysis operations including:
- Counting words, sentences, and characters
- Extracting entities (names, places, etc.)
- Analyzing sentiment
- Extracting keywords

## Adding a New Component

### Component Development Guide

One of the key features of this system is the ability to add new components without modifying the core system. Here's a comprehensive guide to creating new components:

#### Component Lifecycle

1. **Registration**: Component is registered with the system at startup
2. **Initialization**: Component is initialized when first used
3. **Validation**: Component validates incoming messages
4. **Processing**: Component processes messages and produces results
5. **Cleanup**: Component performs any necessary cleanup

#### Component Requirements

- Must inherit from `BaseComponent`
- Must be decorated with `@register_component`
- Must implement the `process` method
- Should implement the `validate` method

#### Adding a new component is as simple as creating a new Python file:

```python
from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component
from pydantic import BaseModel, Field
from typing import Optional, List

# Define request schema using Pydantic (optional)
class MyRequestSchema(BaseModel):
    required_field: str = Field(..., description="A required field")
    optional_field: Optional[str] = Field(None, description="An optional field")
    number_field: int = Field(..., ge=0, le=100, description="A number between 0 and 100")
    list_field: List[str] = Field(default_factory=list, description="A list of strings")

@register_component("my_component")
class MyComponent(BaseComponent):
    def __init__(self):
        super().__init__()
        # Set the request schema for automatic validation
        self.request_schema = MyRequestSchema

    async def validate(self, payload):
        # For manual validation, you can use the validation helpers
        # This is optional if you're using request_schema

        # Validate a single field
        name_result = self.validate_field(
            "name", payload.get("name"),
            required=True, field_type=str,
            min_length=3, max_length=50
        )
        if not name_result.is_valid:
            return name_result

        # Validate a numeric field
        age_result = self.validate_field(
            "age", payload.get("age"),
            required=False, field_type=int,
            min_value=0, max_value=120
        )
        if not age_result.is_valid:
            return age_result

        # Validate against allowed values
        status_result = self.validate_field(
            "status", payload.get("status"),
            required=True, allowed_values=["active", "inactive", "pending"]
        )
        if not status_result.is_valid:
            return status_result

        # All validations passed
        return ValidationResult(is_valid=True)

    async def process(self, payload):
        # Process the payload
        result = do_something_with(payload["required_field"])
        return {"output": result}
```

That's it! No configuration files, no environment variables, no code changes to the core system.

## How It Works

1. When the system starts, it scans for components using the `@register_component` decorator
2. The system creates a single set of Kafka topics for all components
3. When a message is received, it's routed to the appropriate component based on the `target_component` field
4. The component processes the message and sends the result to the response topic
5. The result includes a `component_type` field to identify which component processed it

## Kafka Topics

The system uses a single set of topics for all components:

- `node-execution-request`: For all incoming requests
- `node_results`: For all responses
- `node-execution-request_dlq`: Dead letter queue for all failed messages

Each message includes a `target_component` field to identify which component should process it. This design simplifies the Kafka topic structure and allows for easier management of the message flow in the system.

## Installation

1. Clone the repository:
   ```bash
   git clone https://gitlab.rapidinnovation.tech/ruh-catalyst/node-executor-service
   cd node-executor-service
   ```

2. Install dependencies:
   ```bash
   poetry install
   ```

## Configuration

The system requires minimal configuration for basic functionality, but offers extensive security configuration options. Create a `.env` file based on the provided `.env.example`:

```bash
# Kafka connection settings
KAFKA_BOOTSTRAP_SERVERS="localhost:9092"

# Node Executor Kafka topics
NODE_EXECUTOR_TOPIC="node-executor"
NODE_EXECUTOR_RESPONSE_TOPIC="node-executor-response"
NODE_EXECUTOR_DLQ_TOPIC="node-executor-dlq"

# Logging settings
LOG_LEVEL="INFO"

# Security settings (examples)
SECURITY_API_URL_ALLOWLIST=["example.com", "api.example.com", "api.weatherapi.com"]
SECURITY_API_URL_BLOCKLIST=["localhost", "127.0.0.1", "0.0.0.0", "::1"]
SECURITY_API_MAX_TIMEOUT=60
SECURITY_API_ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]
SECURITY_API_MAX_CONTENT_SIZE=10485760
SECURITY_API_ALLOWED_CONTENT_TYPES=["application/json", "text/plain", "text/html", "application/xml"]

# Document security
SECURITY_DOC_ALLOWED_PATHS=["./data", "./documents"]
SECURITY_DOC_BLOCKED_PATHS=["/etc", "/var", "/usr", "/bin", "/sbin", "C:\\Windows", "C:\\Program Files"]

# Audit logging
SECURITY_ENABLE_AUDIT_LOGGING=true
SECURITY_LOG_SENSITIVE_FIELDS=["password", "token", "secret", "key", "auth", "credential", "apikey", "api_key", "access_token"]

# Content security
SECURITY_SCAN_FOR_MALICIOUS_CONTENT=true

# Kafka security (optional)
KAFKA_SECURITY_PROTOCOL="PLAINTEXT"
# KAFKA_SASL_MECHANISM="PLAIN"
# KAFKA_SASL_USERNAME="your-username"
# KAFKA_SASL_PASSWORD="your-password"
# KAFKA_SSL_CAFILE="/path/to/ca.pem"
# KAFKA_SSL_CERTFILE="/path/to/cert.pem"
# KAFKA_SSL_KEYFILE="/path/to/key.pem"
```

### Docker Environment Variables

When using Docker, you can configure the system using environment variables in the `docker-compose.yml` file. The environment variables follow the same naming convention as the `.env` file.

## Production Deployment

### Deployment Architecture

For production deployments, we recommend the following architecture:

1. **Multiple Node Executor Instances**: Deploy multiple instances of the node-executor service for high availability and scalability
2. **Kafka Cluster**: Use a production-grade Kafka cluster with multiple brokers and proper replication
3. **Load Balancer**: Place a load balancer in front of the node-executor instances
4. **Monitoring**: Implement comprehensive monitoring and alerting
5. **Logging**: Centralize logs for analysis and troubleshooting

### Deployment Steps

1. Build the Docker image:
   ```bash
   docker build -t node-executor:latest .
   ```

2. Push the image to your container registry:
   ```bash
   docker tag node-executor:latest your-registry/node-executor:latest
   docker push your-registry/node-executor:latest
   ```

3. Deploy using Docker Compose or Kubernetes:
   - For Docker Compose, use the provided `docker-compose.yml` file
   - For Kubernetes, use the provided deployment files in the `kubernetes/` directory:
     ```bash
     kubectl apply -f kubernetes/kafka.yaml
     kubectl apply -f kubernetes/deployment.yaml
     ```

### Monitoring and Maintenance

#### Health Checks

The system provides health check endpoints that can be used to monitor the health of the service:

- `/health`: Returns the overall health of the service
- `/metrics`: Returns metrics about the service (requires Prometheus integration)

#### Scaling

To scale the service horizontally, simply add more instances of the node-executor service. The Kafka-based architecture ensures that messages are properly distributed among the instances.

#### Upgrading

To upgrade the service:

1. Build and push a new version of the Docker image
2. Update the image tag in your deployment configuration
3. Deploy the new version
4. Monitor the logs for any issues

The system is designed to handle graceful shutdowns, so in-flight requests will be properly processed before the service shuts down.

## Running the System

To run the system:

```bash
# On Linux/macOS/Git Bash
chmod +x run_auto.sh  # Make the script executable (first time only)
./run_auto.sh
```

```powershell
# On Windows PowerShell
.\run_auto.ps1
```

Or directly with Python:

```bash
poetry run python -m app.main
```

## Running the System

To run the system:

```bash
# On Linux/macOS/Git Bash
chmod +x run_auto.sh  # Make the script executable (first time only)
./run_auto.sh
```

```powershell
# On Windows PowerShell
.\run_auto.ps1
```

Or directly with Python:

```bash
poetry run python -m app.main
```

## Testing

The system includes a comprehensive test suite that verifies the functionality of all components. The test suite is designed to be easy to run and extend.

### Automated Component Testing

The `test_all_components.py` file provides a complete test suite for all components in the system. This test script:

1. Tests each component with multiple test cases
2. Verifies component behavior with different input parameters
3. Validates response formats and content
4. Provides detailed logging of test results
5. Includes error handling and recovery
6. Generates a summary report of test results

To run the automated test suite:

```bash
# Using Poetry
poetry run python test_all_components.py

# Or directly with Python
python test_all_components.py
```

The test suite requires Kafka to be running. Make sure to start Kafka before running the tests:

```bash
# Start Kafka and Zookeeper
docker-compose up -d zookeeper kafka
```

### Test Suite Features

The test suite includes the following features:

- **Comprehensive Testing**: Tests all aspects of each component
- **Isolated Test Environment**: Creates temporary files and directories for testing
- **Detailed Logging**: Provides detailed logs of test execution
- **Validation**: Verifies that component responses match expected values
- **Error Handling**: Gracefully handles errors and continues testing
- **Test Summary**: Provides a summary of test results at the end

### Component Tests

The test suite includes tests for all components:

1. **API Component**: Tests GET, POST, and other HTTP methods with various parameters
2. **Combine Text Component**: Tests text combination with different separators and input types
3. **Doc Component**: Tests document extraction with different file types and options
4. **Parse JSON Component**: Tests JSON parsing from strings and files
5. **Select Data Component**: Tests data selection from dictionaries, lists, and with JSONPath/JMESPath
6. **Split Text Component**: Tests text splitting with various delimiters and options
7. **Text Analysis Component**: Tests text analysis operations like counting, sentiment analysis, and entity extraction
8. **Update Data Component**: Tests dictionary updates and file operations

### Manual Testing

In addition to the automated test suite, you can manually test components by sending messages to the Kafka topics and monitoring the responses.

#### Example Test Message

Here's an example of how to send a test message to the API component:

```python
import asyncio
import json
from aiokafka import AIOKafkaProducer

async def send_test_message():
    # Create a producer
    producer = AIOKafkaProducer(
        bootstrap_servers='localhost:9092',
        value_serializer=lambda v: json.dumps(v).encode('utf-8')
    )

    # Start the producer
    await producer.start()

    try:
        # Create a test message
        message = {
            "request_id": "test-123",
            "tool_name": "api",  # Target the API component
            "tool_parameters": {
                "url": "https://jsonplaceholder.typicode.com/posts/1",
                "method": "GET",
                "headers": {"Content-Type": "application/json"}
            }
        }

        # Send the message
        await producer.send_and_wait('node-execution-request', message)
        print(f"Sent message: {message}")
    finally:
        # Stop the producer
        await producer.stop()

if __name__ == "__main__":
    asyncio.run(send_test_message())
```

#### Monitoring Responses

You can monitor the responses from the components by consuming messages from the response topic:

```python
import asyncio
import json
from aiokafka import AIOKafkaConsumer

async def monitor_responses():
    # Create a consumer
    consumer = AIOKafkaConsumer(
        'node_results',
        bootstrap_servers='localhost:9092',
        group_id='test-consumer',
        auto_offset_reset='latest',
        value_deserializer=lambda m: json.loads(m.decode('utf-8'))
    )

    # Start the consumer
    await consumer.start()

    try:
        # Consume messages
        async for msg in consumer:
            print(f"Received response: {msg.value}")
    finally:
        # Stop the consumer
        await consumer.stop()

if __name__ == "__main__":
    asyncio.run(monitor_responses())
```

## Message Format

Each component accepts specific message formats for requests and produces standardized responses.

### API Component

#### Request

```json
{
  "request_id": "unique-request-id",
  "url": "https://api.example.com/endpoint",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer token123"
  },
  "params": {
    "param1": "value1",
    "param2": "value2"
  },
  "json": {
    "key1": "value1",
    "key2": "value2"
  },
  "timeout": 30
}
```

#### Response

```json
{
  "request_id": "unique-request-id",
  "result": {
    "status_code": 200,
    "headers": {
      "Content-Type": "application/json"
    },
    "content_type": "application/json",
    "data": {
      "key1": "value1",
      "key2": "value2"
    }
  },
  "status": "success",
  "timestamp": 1621234567.89
}
```

### Document Component

#### Request

```json
{
  "request_id": "unique-request-id",
  "file_path": "/path/to/document.pdf",
  "file_type": "auto",
  "max_content_length": 10000,
  "extract_metadata": true
}
```

#### Response

```json
{
  "request_id": "unique-request-id",
  "result": {
    "content": {
      "text": "Document content...",
      "pages": 10
    },
    "metadata": {
      "title": "document.pdf",
      "author": "Author Name",
      "creation_date": "2023-01-01",
      "file_size": 12345
    }
  },
  "status": "success",
  "timestamp": 1621234567.89
}
```

### Split Text Component

#### Request

```json
{
  "request_id": "unique-request-id",
  "input_text": "apple,banana,cherry,date,elderberry",
  "delimiter": ",",
  "max_splits": 2,
  "include_delimiter": true
}
```

#### Response

```json
{
  "request_id": "unique-request-id",
  "result": {
    "output_list": ["apple,", "banana,", "cherry,date,elderberry"]
  },
  "status": "success",
  "timestamp": 1621234567.89
}
```

### Update Data Component

#### Dictionary Update Request

```json
{
  "request_id": "unique-request-id",
  "input_dict": {
    "key1": "value1",
    "key2": "value2"
  },
  "updates": {
    "key2": "new_value2",
    "key3": "value3"
  },
  "deep_update": false
}
```

#### File Operation Request

```json
{
  "request_id": "unique-request-id",
  "operation": "create",  // or "update", "delete"
  "data_file": "./data/my_data.json",
  "data": {
    "id": "123",
    "name": "Example Data",
    "values": [1, 2, 3]
  }
}
```

#### Response

```json
{
  "request_id": "unique-request-id",
  "result": {
    "key1": "value1",
    "key2": "new_value2",
    "key3": "value3"
  },
  "status": "success",
  "timestamp": 1621234567.89
}
```

#### File Operation Response

```json
{
  "request_id": "unique-request-id",
  "result": {
    "success": true,
    "message": "Data file created: ./data/my_data.json"
  },
  "status": "success",
  "timestamp": 1621234567.89
}
```

### Select Data Component

#### Data Selection Request

```json
{
  "request_id": "unique-request-id",
  "input_data": ["apple", "banana", "cherry", "date"],
  "selector": "2",  // Select the third item (index 2)
  "data_type": "List"  // or "Dictionary", "Auto-Detect"
}
```

#### File Operation Request

```json
{
  "request_id": "unique-request-id",
  "operation": "get_all",  // or "get_by_id", "filter", "sort"
  "data_file": "./data/my_data.json",
  "id": "123",  // for get_by_id
  "filter_criteria": {  // for filter
    "category": "fruits"
  },
  "sort_key": "name",  // for sort
  "ascending": true  // for sort
}
```

#### Response

```json
{
  "request_id": "unique-request-id",
  "result": "cherry",
  "status": "success",
  "timestamp": 1621234567.89
}
```

#### File Operation Response

```json
{
  "request_id": "unique-request-id",
  "result": {
    "success": true,
    "data": [
      {
        "id": "123",
        "name": "Apple",
        "category": "fruits"
      },
      {
        "id": "124",
        "name": "Banana",
        "category": "fruits"
      }
    ]
  },
  "status": "success",
  "timestamp": 1621234567.89
}
```

### Error Response

```json
{
  "request_id": "unique-request-id",
  "error": "Error message",
  "status": "error",
  "timestamp": 1621234567.89
}
```

## Project Structure

```
node-executor-service/
├── app/
│   ├── main.py                # Main entry point
│   ├── __init__.py
│   ├── components/                 # Component implementations
│   │   ├── api_component.py        # API component
│   │   ├── combine_text_component.py # Combine Text component
│   │   ├── doc_component.py        # Document component
│   │   ├── parse_json_component.py # Parse JSON component
│   │   ├── select_data_component.py # Select Data component
│   │   ├── split_text_component.py # Split Text component
│   │   ├── text_analysis_component.py # Text Analysis component
│   │   ├── update_data_component.py # Update Data component
│   │   └── __init__.py
│   ├── config/                     # Configuration
│   │   ├── config.py               # Configuration settings
│   │   └── __init__.py
│   └── core_/                      # Core functionality
│       ├── audit_logger.py         # Security audit logging
│       ├── base_component.py       # Base class for all components
│       ├── component_system.py     # Component registry and manager
│       ├── security_config.py      # Security configuration
│       ├── security_middleware.py  # Security middleware
│       └── __init__.py
├── diagrams/                       # Architecture diagrams
│   ├── architecture.md             # Architecture documentation
│   ├── code_flow.md                # Code flow documentation
│   ├── data_flow.md                # Data flow documentation
│   └── README.md                   # Diagrams README
├── .env.example                    # Example environment variables
├── .gitignore                      # Git ignore file
├── Dockerfile                      # Docker configuration
├── poetry.lock                     # Poetry lock file
├── pyproject.toml                  # Poetry project file
├── README.md                       # This file
├── run_auto.ps1                    # Windows run script
└── run_auto.sh                     # Linux/macOS run script
```

## System Architecture and Design

### Core Architecture

The Dynamic Kafka Component System is built on a modular, event-driven architecture that enables high scalability and flexibility. The system consists of the following key components:

1. **Component Registry**: Maintains a registry of all available components
2. **Component Manager**: Manages component lifecycle and message routing
3. **Topic Generator**: Dynamically generates Kafka topics for components
4. **Security Manager**: Enforces security policies and validates requests
5. **Audit Logger**: Records security events and component activities

### Design Principles

1. **Zero Configuration**: Components are automatically discovered and registered
2. **Loose Coupling**: Components communicate via messages, not direct calls
3. **Single Responsibility**: Each component performs a specific task
4. **Fail Fast**: Validation occurs early in the processing pipeline
5. **Defense in Depth**: Multiple layers of security controls

### Scalability Approach

The system achieves scalability through several mechanisms:

1. **Horizontal Scaling**: Multiple instances can process messages in parallel
2. **Asynchronous Processing**: Non-blocking I/O for high throughput
3. **Configurable Concurrency**: Adjustable limits for parallel task execution
4. **Resource Isolation**: Components run independently and don't share state
5. **Efficient Message Routing**: Direct message delivery to target components

## System Features

- **Auto-Discovery**: Components are automatically discovered at runtime
- **Dynamic Topic Generation**: Kafka topics are generated automatically
- **Parallel Processing**: Handles multiple requests concurrently from different workflows
- **Task Monitoring**: Background task monitors execution and cleans up timed-out tasks
- **Error Handling**: Failed executions are sent to a dead letter queue
- **Graceful Shutdown**: Properly handles in-flight requests during shutdown
- **Verified Concurrency**: Comprehensive tests verify that requests from multiple workflows are processed in parallel
- **Hot Reload**: Components can be added or updated without restarting the system
- **Extensible Pipeline**: New processing steps can be added to the workflow
- **Stateless Design**: Components maintain no state between invocations

## Security Features

- **URL Validation**: Validates URLs against allowlists and blocklists
- **Rate Limiting**: Limits the number of requests per time period
- **Content Scanning**: Scans content for malicious patterns
- **Path Validation**: Validates file paths against allowlists and blocklists
- **Audit Logging**: Logs security events for auditing purposes
- **Sensitive Data Handling**: Redacts sensitive data in logs
- **Enhanced Input Validation**: Comprehensive validation framework with:
  - Schema-based validation using Pydantic
  - Field-level validation with detailed error reporting
  - Type checking, range validation, and allowed values validation
  - Custom validation rules
- **Secure Defaults**: Conservative security settings by default
- **Defense in Depth**: Multiple layers of security controls

### Security Recommendations

#### JWT Authentication Implementation

For production environments, we recommend implementing JWT-based authentication. This would involve:

1. Creating a `SecurityMiddleware` that validates JWT tokens
2. Adding token validation to the pre-processing pipeline
3. Implementing role-based access control for components
4. Setting up a token issuer and management system

> **Note**: JWT-based authentication is not currently implemented but is planned for a future release.

## Troubleshooting

### Common Issues

#### Kafka Connection Issues

**Symptom**: The application fails to connect to Kafka with errors like `Connection refused` or `Broker not available`.

**Solution**:
1. Ensure Kafka is running: `docker ps | grep kafka`
2. If Kafka is not running, start it: `docker-compose up -d zookeeper kafka`
3. Check Kafka logs: `docker-compose logs kafka`
4. Verify the `KAFKA_BOOTSTRAP_SERVERS` setting in your `.env` file

#### Component Not Found

**Symptom**: The application logs show `Component type 'xyz' is not registered`.

**Solution**:
1. Ensure the component file is in the correct location (`app/components/`)
2. Check that the component class is decorated with `@register_component`
3. Verify that the component name matches the expected name
4. Restart the application to discover new components

#### Security Validation Failures

**Symptom**: Requests fail with security validation errors.

**Solution**:
1. Check the security settings in your `.env` file
2. Verify that the request meets the security requirements (URL allowlist, content size, etc.)
3. Review the audit logs for detailed information about the security failure

### FAQ

#### How do I add a new component?

See the [Adding a New Component](#adding-a-new-component) section for detailed instructions.

#### How do I configure security settings?

Security settings are configured in the `.env` file. See the [Configuration](#configuration) section for details.

#### Can I use this system without Kafka?

No, Kafka is a core dependency of this system. It's used for message routing, component communication, and ensuring reliable message delivery.

#### How do I monitor the system in production?

See the [Monitoring and Maintenance](#monitoring-and-maintenance) section for details on health checks and metrics.

## Benefits

- **Modularity**: Each component is completely independent
- **Scalability**: Add as many components as needed without changing the core system
- **Maintainability**: Simple, consistent interface for all components
- **Flexibility**: Components can be added, removed, or updated without affecting others

## Prerequisites

- Python 3.11 or higher
- Poetry (Python package manager)
- Kafka broker (for running the system and integration tests)

## License

MIT License

## Recent Updates

### Enhanced Test Suite (April 2025)

The system has been updated with a comprehensive test suite that provides thorough testing of all components. Key improvements include:

1. **Comprehensive Component Testing**: Each component now has multiple test cases that verify its functionality with various inputs and parameters.

2. **Improved Test Reporting**: The test suite now provides detailed logs and a summary report of test results.

3. **Error Handling and Recovery**: Tests are designed to handle errors gracefully and continue testing other components.

4. **Validation of Expected Results**: Tests now verify that component responses match expected values.

5. **Isolated Test Environment**: Tests create temporary files and directories to avoid interfering with the production environment.

The new test suite (`test_all_components.py`) makes it easier to:
- Verify that all components are working correctly
- Detect regressions when making changes
- Understand how components behave with different inputs
- Ensure consistent behavior across all components

To run the updated test suite, make sure Kafka is running and then execute:

```bash
poetry run python test_all_components.py
```

## Conclusion

The Node Executor Service provides a highly dynamic and scalable way to process messages with Kafka. It's designed to be easy to extend with new components, while maintaining a high level of security and reliability.

Key features of this system include:

1. **Single Kafka Topic Architecture**: All components share the same Kafka topics, simplifying the message flow and reducing the number of topics needed.

2. **Component-Based Design**: Each component is a self-contained unit that can be developed and deployed independently.

3. **Parallel Processing**: Multiple components can run in parallel, increasing throughput and efficiency.

4. **Robust Error Handling**: Failed messages are sent to a Dead Letter Queue (DLQ) for later analysis and reprocessing.

5. **Security-First Approach**: Comprehensive security features including URL validation, rate limiting, and content scanning.

6. **Comprehensive Testing**: A complete test suite that verifies the functionality of all components.

This architecture makes the system highly maintainable, scalable, and secure, while also being easy to extend with new functionality.

## Contributors

- Your Name
