#!/usr/bin/env python3
"""
Manual test runner for conditional component multiple transitions support.
This script tests the new functionality without requiring pytest.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from components.conditional_component import ConditionalComponent


async def test_first_match_strategy():
    """Test first_match strategy with multiple conditions."""
    print("🧪 Testing first_match strategy...")
    
    component = ConditionalComponent()
    
    payload = {
        "request_id": "test-001",
        "conditions": [
            {
                "source": "node_output",
                "operator": "equals",
                "expected_value": "test_value",
                "next_transition": "transition_1"
            },
            {
                "source": "node_output",
                "operator": "equals",
                "expected_value": "test_value",  # This would also match but should be ignored
                "next_transition": "transition_2"
            }
        ],
        "global_context": {},
        "node_output": "test_value",
        "default_transition": "default_transition",
        "evaluation_strategy": "first_match"
    }

    result = await component.process(payload)
    
    print(f"   Status: {result['status']}")
    print(f"   Target: {result['routing_decision'].get('target_transition')}")
    print(f"   Matched: {result['routing_decision'].get('matched_condition')}")
    
    # Verify first match wins
    assert result["status"] == "success"
    assert result["routing_decision"]["target_transition"] == "transition_1"
    assert result["routing_decision"]["matched_condition"] == 1
    
    print("   ✅ PASSED: First match strategy works correctly")


async def test_all_matches_strategy():
    """Test all_matches strategy with multiple matching conditions."""
    print("\n🧪 Testing all_matches strategy...")
    
    component = ConditionalComponent()
    
    payload = {
        "request_id": "test-002",
        "conditions": [
            {
                "source": "node_output",
                "operator": "contains",
                "expected_value": "test",
                "next_transition": "transition_1"
            },
            {
                "source": "node_output",
                "operator": "starts_with",
                "expected_value": "test",
                "next_transition": "transition_2"
            },
            {
                "source": "node_output",
                "operator": "equals",
                "expected_value": "different_value",  # This won't match
                "next_transition": "transition_3"
            }
        ],
        "global_context": {},
        "node_output": "test_value",
        "default_transition": "default_transition",
        "evaluation_strategy": "all_matches"
    }

    result = await component.process(payload)
    
    print(f"   Status: {result['status']}")
    print(f"   Targets: {result['routing_decision'].get('target_transitions')}")
    print(f"   Matched conditions: {result['routing_decision'].get('matched_conditions')}")
    print(f"   Total matches: {result['metadata'].get('total_matches')}")
    
    # Verify multiple matches
    assert result["status"] == "success"
    assert result["routing_decision"]["target_transitions"] == ["transition_1", "transition_2"]
    assert result["routing_decision"]["matched_conditions"] == [1, 2]
    assert result["metadata"]["total_matches"] == 2
    
    print("   ✅ PASSED: All matches strategy works correctly")


async def test_all_matches_no_matches():
    """Test all_matches strategy when no conditions match."""
    print("\n🧪 Testing all_matches strategy with no matches...")
    
    component = ConditionalComponent()
    
    payload = {
        "request_id": "test-003",
        "conditions": [
            {
                "source": "node_output",
                "operator": "equals",
                "expected_value": "different_value",
                "next_transition": "transition_1"
            }
        ],
        "global_context": {},
        "node_output": "test_value",
        "default_transition": "default_transition",
        "evaluation_strategy": "all_matches"
    }

    result = await component.process(payload)
    
    print(f"   Status: {result['status']}")
    print(f"   Targets: {result['routing_decision'].get('target_transitions')}")
    print(f"   Matched conditions: {result['routing_decision'].get('matched_conditions')}")
    print(f"   Total matches: {result['metadata'].get('total_matches')}")
    
    # Verify default transition is used
    assert result["status"] == "success"
    assert result["routing_decision"]["target_transitions"] == ["default_transition"]
    assert result["routing_decision"]["matched_conditions"] == []
    assert result["metadata"]["total_matches"] == 0
    
    print("   ✅ PASSED: No matches defaults to default transition")


async def test_backward_compatibility():
    """Test backward compatibility when no strategy is specified."""
    print("\n🧪 Testing backward compatibility...")
    
    component = ConditionalComponent()
    
    payload = {
        "request_id": "test-004",
        "conditions": [
            {
                "source": "node_output",
                "operator": "equals",
                "expected_value": "test_value",
                "next_transition": "transition_1"
            }
        ],
        "global_context": {},
        "node_output": "test_value",
        "default_transition": "default_transition"
        # No evaluation_strategy specified - should default to "first_match"
    }

    result = await component.process(payload)
    
    print(f"   Status: {result['status']}")
    print(f"   Has target_transition: {'target_transition' in result['routing_decision']}")
    print(f"   Target: {result['routing_decision'].get('target_transition')}")
    
    # Should use legacy single transition format
    assert result["status"] == "success"
    assert "target_transition" in result["routing_decision"]
    assert result["routing_decision"]["target_transition"] == "transition_1"
    
    print("   ✅ PASSED: Backward compatibility maintained")


async def test_invalid_strategy():
    """Test error handling for invalid evaluation strategy."""
    print("\n🧪 Testing invalid strategy handling...")
    
    component = ConditionalComponent()
    
    payload = {
        "request_id": "test-005",
        "conditions": [
            {
                "source": "node_output",
                "operator": "equals",
                "expected_value": "test_value",
                "next_transition": "transition_1"
            }
        ],
        "global_context": {},
        "node_output": "test_value",
        "default_transition": "default_transition",
        "evaluation_strategy": "invalid_strategy"
    }

    result = await component.process(payload)
    
    print(f"   Status: {result['status']}")
    print(f"   Error: {result.get('error', 'No error')}")
    
    # Should return error
    assert result["status"] == "error"
    assert "Unknown evaluation strategy" in result["error"]
    
    print("   ✅ PASSED: Invalid strategy properly handled")


async def main():
    """Run all tests."""
    print("🚀 Starting Conditional Component Multiple Transitions Tests\n")
    
    try:
        await test_first_match_strategy()
        await test_all_matches_strategy()
        await test_all_matches_no_matches()
        await test_backward_compatibility()
        await test_invalid_strategy()
        
        print("\n🎉 ALL TESTS PASSED! Multiple transitions support is working correctly.")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
