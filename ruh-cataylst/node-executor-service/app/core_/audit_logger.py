"""
Audit Logger - Provides security audit logging functionality.
"""
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional

from app.core_.security_config import get_security_settings

# Create a dedicated audit logger
audit_logger = logging.getLogger("audit")

# Set up a formatter for audit logs
audit_formatter = logging.Formatter(
    '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "event_type": "%(event_type)s", '
    '"component": "%(component)s", "message": "%(message)s", "details": %(details)s}',
    datefmt="%Y-%m-%d %H:%M:%S"
)


def setup_audit_logging():
    """
    Set up audit logging based on security settings.
    """
    settings = get_security_settings()
    
    if not settings.ENABLE_AUDIT_LOGGING:
        return
    
    # Set the level to INFO
    audit_logger.setLevel(logging.INFO)
    
    # Remove any existing handlers
    for handler in audit_logger.handlers[:]:
        audit_logger.removeHandler(handler)
    
    # Create a file handler if specified
    if settings.AUDIT_LOG_FILE:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(settings.AUDIT_LOG_FILE), exist_ok=True)
        
        # Create a file handler
        file_handler = logging.FileHandler(settings.AUDIT_LOG_FILE)
        file_handler.setFormatter(audit_formatter)
        audit_logger.addHandler(file_handler)
    else:
        # Create a console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(audit_formatter)
        audit_logger.addHandler(console_handler)


def log_security_event(
    event_type: str,
    component: str,
    message: str,
    details: Dict[str, Any],
    level: str = "INFO"
):
    """
    Log a security event.
    
    Args:
        event_type: The type of security event
        component: The component that generated the event
        message: A human-readable message
        details: Additional details about the event
        level: The log level (INFO, WARNING, ERROR, CRITICAL)
    """
    settings = get_security_settings()
    
    if not settings.ENABLE_AUDIT_LOGGING:
        return
    
    # Sanitize sensitive data in details
    sanitized_details = _sanitize_details(details) if settings.SANITIZE_LOGS else details
    
    # Convert details to JSON string
    details_json = json.dumps(sanitized_details)
    
    # Create extra fields for the logger
    extra = {
        "event_type": event_type,
        "component": component,
        "details": details_json
    }
    
    # Log at the appropriate level
    if level == "WARNING":
        audit_logger.warning(message, extra=extra)
    elif level == "ERROR":
        audit_logger.error(message, extra=extra)
    elif level == "CRITICAL":
        audit_logger.critical(message, extra=extra)
    else:
        audit_logger.info(message, extra=extra)


def _sanitize_details(details: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sanitize sensitive data in details.
    
    Args:
        details: The details to sanitize
        
    Returns:
        Sanitized details
    """
    settings = get_security_settings()
    result = {}
    
    for key, value in details.items():
        # Check if this is a sensitive field
        is_sensitive = False
        for sensitive_pattern in settings.LOG_SENSITIVE_FIELDS:
            if sensitive_pattern.lower() in key.lower():
                is_sensitive = True
                break
                
        if is_sensitive:
            # Mask the value
            if isinstance(value, str):
                if len(value) > 4:
                    result[key] = value[:2] + "*" * (len(value) - 4) + value[-2:]
                else:
                    result[key] = "****"
            else:
                result[key] = "[REDACTED]"
        elif isinstance(value, dict):
            # Recursively sanitize nested dictionaries
            result[key] = _sanitize_details(value)
        else:
            result[key] = value
            
    return result


# Initialize audit logging
setup_audit_logging()
