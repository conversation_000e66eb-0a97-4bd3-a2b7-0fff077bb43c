"""
Unified AI Component Executor - Handles all AI components with a single, efficient executor.

This module implements a unified execution pattern for all AI components, eliminating
code duplication and providing consistent behavior across all AI operations.
"""

import asyncio
import json
import logging
import time
import traceback
from typing import Dict, Any, Optional, Union, List
from pydantic import BaseModel, Field, validator
from abc import ABC, abstractmethod

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


# ============================================================================
# Exception Hierarchy for AI Components
# ============================================================================

class AIComponentError(Exception):
    """Base exception for AI component errors."""
    pass


class AIProviderError(AIComponentError):
    """Error from AI provider (OpenAI, Anthropic, etc.)."""
    pass


class ValidationError(AIComponentError):
    """Input validation error."""
    pass


class ConfigurationError(AIComponentError):
    """Component configuration error."""
    pass


# ============================================================================
# Request/Response Schemas
# ============================================================================

class AIComponentRequest(BaseModel):
    """Schema for unified AI component requests."""
    component_type: str = Field(..., description="Type of AI component to execute")
    model_provider: str = Field(default="OpenAI", description="AI model provider")
    api_key: str = Field(..., description="API key for the model provider")
    model_name: str = Field(default="gpt-4o", description="Model name to use")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Temperature for generation")
    base_url: Optional[str] = Field(None, description="Custom base URL for API")
    component_inputs: Dict[str, Any] = Field(default_factory=dict, description="Component-specific inputs")
    request_id: str = Field(..., description="Unique request identifier")
    
    @validator('component_type')
    def validate_component_type(cls, v):
        """Validate that component type is supported."""
        supported_types = {
            "sentiment_analyzer", "classifier", "summarizer",
            "information_extractor", "question_answer_module", "basic_llm_chain"
        }
        if v not in supported_types:
            raise ValueError(f"Unsupported component type: {v}. Supported types: {supported_types}")
        return v


# ============================================================================
# AI Provider Client Factory
# ============================================================================

class AIClientFactory:
    """Factory for creating AI provider clients with connection pooling."""
    
    _client_cache: Dict[str, Any] = {}
    
    @classmethod
    async def get_client(cls, provider: str, api_key: str, base_url: Optional[str] = None):
        """
        Get or create AI client with caching for performance.
        
        Args:
            provider: AI provider name (OpenAI, Anthropic, etc.)
            api_key: API key for authentication
            base_url: Custom base URL if needed
            
        Returns:
            Configured AI client instance
            
        Raises:
            AIProviderError: If provider is not supported or client creation fails
        """
        # Create safe cache key (handle case where api_key might be non-string)
        api_key_str = str(api_key) if api_key else "empty"
        cache_key = f"{provider}:{hash(api_key_str)}:{base_url or 'default'}"
        
        if cache_key in cls._client_cache:
            return cls._client_cache[cache_key]
        
        try:
            if provider in ["OpenAI", "Custom"]:
                from openai import AsyncOpenAI
                client = AsyncOpenAI(
                    api_key=api_key,
                    base_url=base_url if provider == "Custom" else None
                )
                cls._client_cache[cache_key] = client
                return client
            
            elif provider == "Azure OpenAI":
                from openai import AsyncAzureOpenAI
                if not base_url:
                    raise AIProviderError("Azure OpenAI requires base_url")
                client = AsyncAzureOpenAI(
                    api_key=api_key,
                    azure_endpoint=base_url,
                    api_version="2024-02-15-preview"
                )
                cls._client_cache[cache_key] = client
                return client
            
            else:
                raise AIProviderError(f"Unsupported AI provider: {provider}")
                
        except ImportError as e:
            raise AIProviderError(f"Required package not installed for {provider}: {e}")
        except Exception as e:
            raise AIProviderError(f"Failed to create {provider} client: {e}")


# ============================================================================
# Component Configuration System
# ============================================================================

class ComponentConfig:
    """Configuration for individual AI components."""
    
    def __init__(self, system_prompt: str, output_fields: List[str], 
                 temperature_default: float = 0.7, response_format: str = "text",
                 input_mapping: Optional[Dict[str, Any]] = None,
                 prompt_template: Optional[str] = None,
                 validation_rules: Optional[Dict[str, Any]] = None):
        self.system_prompt = system_prompt
        self.output_fields = output_fields
        self.temperature_default = temperature_default
        self.response_format = response_format
        self.input_mapping = input_mapping or {}
        self.prompt_template = prompt_template
        self.validation_rules = validation_rules or {}


# Component configurations following DRY principles
AI_COMPONENT_CONFIGS = {
    
    "sentiment_analyzer": ComponentConfig(
        system_prompt="""You are a sentiment analysis assistant. Analyze the sentiment of the provided text and categorize it as:
1. Positive
2. Negative
3. Neutral

Provide a confidence score between 0 and 1. Format as JSON: {"sentiment": "Positive", "confidence": 0.85}""",
        output_fields=["sentiment", "confidence"],
        temperature_default=0.3,
        response_format="json",
        input_mapping={"text": "text"},  # Updated to unified dual-purpose pattern
        validation_rules={"required": ["text"]}
    ),
    
    "summarizer": ComponentConfig(
        system_prompt="You are a text summarization assistant. Create a concise summary that captures key points.",
        output_fields=["summary"],
        temperature_default=0.5,
        input_mapping={
            "text": "text",  # Updated to unified dual-purpose pattern
            "max_length": "max_length"
        },
        prompt_template="Summarize the following text{max_length_constraint}:\n\n{text}",
        validation_rules={"required": ["text"]}
    ),
    
    "classifier": ComponentConfig(
        system_prompt="You are a text classification assistant. Categorize the text into one of the provided categories.",
        output_fields=["category", "confidence"],
        temperature_default=0.3,
        response_format="json",
        input_mapping={
            "text": "text",  # Updated to unified dual-purpose pattern
            "categories": "categories"  # Updated to unified dual-purpose pattern
        },
        prompt_template="Classify this text into one of these categories: {categories}\n\nText: {text}\n\nRespond with JSON: {{\"category\": \"chosen_category\", \"confidence\": 0.85}}",
        validation_rules={"required": ["text", "categories"]}
    ),
    
    "information_extractor": ComponentConfig(
        system_prompt="You are an information extraction assistant. Extract the requested information from the provided text. Only return the extracted information.",
        output_fields=["extracted_info"],
        temperature_default=0.3,
        input_mapping={
            "text": "text",  # Updated to unified dual-purpose pattern
            "query": "query"  # Updated to unified dual-purpose pattern
        },
        prompt_template="Query: {query}\n\nText: {text}",
        validation_rules={"required": ["text", "query"]}
    ),
    
    "question_answer_module": ComponentConfig(
        system_prompt="You are a question answering assistant. Answer based ONLY on the provided context. If insufficient information, say 'I don't have enough information to answer this question.'",
        output_fields=["answer"],
        temperature_default=0.5,
        input_mapping={
            "question": "question",  # Updated to unified dual-purpose pattern
            "context": "context"  # Updated to unified dual-purpose pattern
        },
        prompt_template="Context: {context}\n\nQuestion: {question}",
        validation_rules={"required": ["question", "context"]}
    ),

    "basic_llm_chain": ComponentConfig(
        system_prompt="DEPRECATED: This component is deprecated. Please use specific AI components (SentimentAnalyzer, Summarizer, etc.) instead.",
        output_fields=["output_text", "full_response"],
        temperature_default=0.7,
        input_mapping={
            "input_variables": "input_variables"
        },
        validation_rules={"required": ["input_variables"]}
    )
}


# ============================================================================
# Response Processing System
# ============================================================================

class ResponseProcessor:
    """Handles processing of AI responses based on component configuration."""
    
    @staticmethod
    def process_response(response: Any, config: ComponentConfig, component_type: str) -> Dict[str, Any]:
        """
        Process AI response based on component configuration.
        
        Args:
            response: Raw AI response
            config: Component configuration
            component_type: Type of component
            
        Returns:
            Processed response dictionary
        """
        try:
            content = response.choices[0].message.content.strip()
            
            # Handle JSON responses
            if config.response_format == "json":
                return ResponseProcessor._process_json_response(content, config, component_type)
            
            # Handle text responses
            return ResponseProcessor._process_text_response(content, config)
            
        except Exception as e:
            logger.error(f"Error processing response for {component_type}: {e}")
            return {"error": f"Failed to process response: {str(e)}"}
    
    @staticmethod
    def _process_json_response(content: str, config: ComponentConfig, component_type: str) -> Dict[str, Any]:
        """Process JSON-formatted responses with fallback handling."""
        try:
            parsed = json.loads(content)
            return parsed
        except json.JSONDecodeError:
            logger.warning(f"Failed to parse JSON response for {component_type}, using fallback")
            
            # Component-specific fallback logic
            if component_type == "sentiment_analyzer":
                sentiment = "Neutral"
                if "positive" in content.lower():
                    sentiment = "Positive"
                elif "negative" in content.lower():
                    sentiment = "Negative"
                return {"sentiment": sentiment, "confidence": 0.5}
            
            elif component_type == "classifier":
                return {"category": "Unknown", "confidence": 0.0, "raw_content": content}
            
            else:
                return {"error": "Failed to parse JSON response", "raw_content": content}
    
    @staticmethod
    def _process_text_response(content: str, config: ComponentConfig) -> Dict[str, Any]:
        """Process text responses."""
        output_fields = config.output_fields
        if len(output_fields) == 1:
            return {output_fields[0]: content}
        else:
            # For multiple fields, return content in the first field
            return {output_fields[0]: content}


# ============================================================================
# Main Unified AI Component
# ============================================================================

@register_component("sentiment_analyzer")
@register_component("summarizer")
@register_component("classifier")
@register_component("information_extractor")
@register_component("question_answer_module")
@register_component("basic_llm_chain")  # Deprecated - use specific AI components instead
class UnifiedAIComponent(BaseComponent):
    """
    Unified AI component executor for all AI operations.
    
    This component implements the unified executor pattern to eliminate code duplication
    and provide consistent behavior across all AI components.
    """
    
    def __init__(self):
        """Initialize the unified AI component."""
        logger.info("Initializing Unified AI Component")
        super().__init__()
        self.request_schema = AIComponentRequest
        logger.info("Unified AI Component initialized successfully")
    
    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate AI component payload.
        
        Args:
            payload: The payload to validate
            
        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        component_type = payload.get("component_type", "unknown")
        
        logger.info(f"Validating AI component payload: {component_type} (request_id: {request_id})")
        
        try:
            # Schema validation
            schema_validation = await super().validate(payload)
            if not schema_validation.is_valid:
                logger.error(f"Schema validation failed for {component_type} (request_id: {request_id}): {schema_validation.error_message}")
                return schema_validation
            
            # Component-specific validation
            config = AI_COMPONENT_CONFIGS.get(component_type)
            if not config:
                error_msg = f"Unknown component type: {component_type}"
                logger.error(f"{error_msg} (request_id: {request_id})")
                return ValidationResult(is_valid=False, error_message=error_msg)
            
            # Detect component type and validate inputs
            component_type = self._detect_component_type(payload)
            component_inputs = self._extract_component_inputs_from_payload(payload, component_type)
            validation_result = self._validate_component_inputs(component_inputs, config, component_type)
            if not validation_result.is_valid:
                logger.error(f"Component input validation failed for {component_type} (request_id: {request_id}): {validation_result.error_message}")
                return validation_result
            
            logger.info(f"Validation successful for {component_type} (request_id: {request_id})")
            return ValidationResult(is_valid=True)
            
        except Exception as e:
            error_msg = f"Unexpected error during validation for {component_type}: {str(e)}"
            logger.error(f"{error_msg} (request_id: {request_id})")
            logger.debug(f"Validation exception details (request_id: {request_id}): {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg)
    
    def _validate_component_inputs(self, inputs: Dict[str, Any], config: ComponentConfig, component_type: str) -> ValidationResult:
        """Validate component-specific inputs."""
        validation_rules = config.validation_rules
        
        # Check required fields
        required_fields = validation_rules.get("required", [])
        for field in required_fields:
            if field not in inputs or not inputs[field]:
                return ValidationResult(
                    is_valid=False,
                    error_message=f"Required field '{field}' is missing or empty for {component_type}"
                )
        
        # Check requires_one_of fields
        requires_one_of = validation_rules.get("requires_one_of", [])
        if requires_one_of:
            if not any(field in inputs and inputs[field] for field in requires_one_of):
                return ValidationResult(
                    is_valid=False,
                    error_message=f"At least one of {requires_one_of} is required for {component_type}"
                )
        
        return ValidationResult(is_valid=True)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process AI component request using unified execution pattern.

        Args:
            payload: Request payload containing component configuration and inputs

        Returns:
            Dictionary containing execution results or error information
        """
        request_id = payload.get("request_id", "unknown")
        start_time = time.time()

        # Detect component type from payload structure (no transformation needed)
        component_type = self._detect_component_type(payload)

        logger.info(f"Processing AI component request: {component_type} (request_id: {request_id})")

        try:
            # Get component configuration
            config = AI_COMPONENT_CONFIGS.get(component_type)
            if not config:
                error_msg = f"Unknown AI component type: {component_type}"
                logger.error(f"{error_msg} (request_id: {request_id})")
                return self._create_error_response(error_msg)

            # Initialize AI client
            client = await self._get_ai_client(payload)

            # Build prompts using configuration-driven approach
            system_prompt = config.system_prompt
            user_prompt = self._build_user_prompt(component_type, payload, config)

            logger.debug(f"Built prompts for {component_type} (request_id: {request_id})")

            # Make AI API call with error handling
            response = await self._make_ai_call(client, system_prompt, user_prompt, payload, config)

            # Process response using component configuration
            result = ResponseProcessor.process_response(response, config, component_type)

            # Add metadata including performance metrics
            result.update(self._extract_metadata(response))

            # Add performance metrics
            execution_time = time.time() - start_time
            result["performance"] = {
                "execution_time_ms": round(execution_time * 1000, 2),
                "component_type": component_type,
                "request_id": request_id
            }

            logger.info(f"AI component {component_type} completed successfully in {execution_time:.3f}s (request_id: {request_id})")
            return self._create_success_response(result)

        except AIComponentError as e:
            error_msg = f"AI component error in {component_type}: {str(e)}"
            logger.error(f"{error_msg} (request_id: {request_id})")
            return self._create_error_response(error_msg, error_type="ai_component_error")

        except Exception as e:
            error_msg = f"Unexpected error processing AI component {component_type}: {str(e)}"
            logger.error(f"{error_msg} (request_id: {request_id})")
            logger.debug(f"Exception details (request_id: {request_id}): {traceback.format_exc()}")
            return self._create_error_response(error_msg, error_type="system_error")

    def _extract_component_inputs_from_payload(self, payload: Dict[str, Any], component_type: str) -> Dict[str, Any]:
        """
        Extract component-specific inputs directly from flattened payload.

        Args:
            payload: Flattened payload from tool executor
            component_type: Component type

        Returns:
            Component-specific inputs
        """
        # Define input fields for each component type
        component_input_fields = {
            "sentiment_analyzer": ["text"],
            "summarizer": ["text", "max_length"],
            "classifier": ["text", "categories"],
            "information_extractor": ["text", "query"],
            "question_answer_module": ["question", "context"],
            "basic_llm_chain": ["input_variables"]
        }

        # Extract relevant fields for the component type
        fields = component_input_fields.get(component_type, ["text"])
        component_inputs = {}

        for field in fields:
            if field in payload:
                component_inputs[field] = payload[field]

        return component_inputs

    def _detect_component_type(self, payload: Dict[str, Any]) -> str:
        """
        Detect component type from payload structure.

        Args:
            payload: Original payload

        Returns:
            Detected component type
        """
        # Check for specific input combinations to detect component type
        if "text" in payload and "query" in payload:
            return "information_extractor"
        elif "question" in payload and "context" in payload:
            return "question_answer_module"
        elif "text" in payload and "categories" in payload:
            return "classifier"
        elif "text" in payload and "max_length" in payload:
            return "summarizer"
        elif "text" in payload:
            return "sentiment_analyzer"
        elif "input_variables" in payload:
            return "basic_llm_chain"
        else:
            # Default fallback
            return "sentiment_analyzer"



    async def _get_ai_client(self, payload: Dict[str, Any]):
        """Get AI client using factory pattern."""
        provider = payload.get("model_provider", "OpenAI")
        api_key_raw = payload.get("api_key")
        base_url = payload.get("base_url")

        # Handle different API key formats
        api_key = self._extract_api_key(api_key_raw)

        if not api_key:
            raise ValidationError("API key is required")

        return await AIClientFactory.get_client(provider, api_key, base_url)

    def _extract_api_key(self, api_key_raw) -> str:
        """
        Extract API key from different formats.

        Handles both:
        1. String format: "sk-..."
        2. Object format: {"value": "sk-...", "use_credential_id": false}

        Args:
            api_key_raw: Raw API key in any format

        Returns:
            API key as string

        Raises:
            ValidationError: If API key format is invalid
        """
        if isinstance(api_key_raw, str):
            return api_key_raw.strip()

        elif isinstance(api_key_raw, dict):
            # Handle object format: {"value": "sk-...", "use_credential_id": false}
            if "value" in api_key_raw:
                api_key_value = api_key_raw["value"]
                if isinstance(api_key_value, str):
                    return api_key_value.strip()
                else:
                    raise ValidationError(f"API key value must be a string, got {type(api_key_value)}")
            else:
                raise ValidationError("API key object must contain 'value' field")

        elif api_key_raw is None:
            return ""

        else:
            raise ValidationError(f"API key must be string or object, got {type(api_key_raw)}")

    def _build_user_prompt(self, component_type: str, payload: Dict[str, Any], config: ComponentConfig) -> str:
        """
        Build user prompt based on component type and configuration.

        Uses template-based approach for consistency and maintainability.
        Works directly with flattened payload structure.
        """
        # Extract component inputs directly from payload (no nesting needed)
        component_inputs = self._extract_component_inputs_from_payload(payload, component_type)

        # Use template if available
        if config.prompt_template:
            try:
                # Handle special formatting for summarizer
                if component_type == "summarizer":
                    max_length = component_inputs.get('max_length')
                    max_length_constraint = f" in no more than {max_length} words" if max_length else ""
                    component_inputs['max_length_constraint'] = max_length_constraint

                return config.prompt_template.format(**component_inputs)
            except KeyError as e:
                raise ValidationError(f"Missing required input for template: {e}")

        # Fallback to component-specific logic
        return self._build_fallback_prompt(component_type, component_inputs)

    def _build_fallback_prompt(self, component_type: str, inputs: Dict[str, Any]) -> str:
        """Build prompt using fallback logic for components without templates."""
        if component_type == "sentiment_analyzer":
            return f"Text to analyze: {inputs.get('text', '')}"

        else:
            # Generic fallback
            return str(inputs)

    async def _make_ai_call(self, client, system_prompt: str, user_prompt: str,
                           payload: Dict[str, Any], config: ComponentConfig):
        """
        Make AI API call with proper error handling and retry logic.

        Args:
            client: AI client instance
            system_prompt: System prompt for the AI
            user_prompt: User prompt with inputs
            payload: Request payload
            config: Component configuration

        Returns:
            AI response object

        Raises:
            AIProviderError: If API call fails
        """
        model_name = payload.get("model_name", "gpt-4o")
        temperature = payload.get("temperature", config.temperature_default)

        try:
            # Prepare messages
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # Make API call with timeout
            response = await asyncio.wait_for(
                client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=4000  # Reasonable default
                ),
                timeout=30.0  # 30 second timeout
            )

            return response

        except asyncio.TimeoutError:
            raise AIProviderError("AI API call timed out after 30 seconds")
        except Exception as e:
            raise AIProviderError(f"AI API call failed: {str(e)}")

    def _extract_metadata(self, response) -> Dict[str, Any]:
        """Extract metadata from AI response."""
        try:
            usage = response.usage
            return {
                "usage": {
                    "prompt_tokens": usage.prompt_tokens,
                    "completion_tokens": usage.completion_tokens,
                    "total_tokens": usage.total_tokens
                },
                "model": response.model,
                "finish_reason": response.choices[0].finish_reason
            }
        except Exception as e:
            logger.warning(f"Failed to extract metadata: {e}")
            return {"metadata_error": str(e)}

    def _create_success_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Create standardized success response."""
        return {
            "status": "success",
            "result": result
        }

    def _create_error_response(self, error_message: str, error_type: str = "unknown") -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "status": "error",
            "error": error_message,
            "error_type": error_type
        }
