"""
Gmail Component - Sends emails and tracks responses.
"""

import os
import re
import logging
import smtplib
import ssl
from email.message import EmailMessage
from email.utils import make_msgid
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, field_validator

from app.core_.base_component import BaseComponent
from app.core_.component_system import register_component
from app.utils.db_handler import create_db_handler

# Setup logger
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("GmailComponent")
except ImportError:
    # Fallback to basic logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


# Request schema for sending emails
class SendEmailRequest(BaseModel):
    """Schema for email sending requests."""
    receiver_email: str = Field(..., description="Email address of the recipient")
    message: str = Field(..., description="Email message content (HTML or plain text)")
    subject: Optional[str] = Field(None, description="Email subject")
    cc_emails: List[str] = Field(default_factory=list, description="CC recipients")
    bcc_emails: List[str] = Field(default_factory=list, description="BCC recipients")
    industry: Optional[str] = Field(None, description="Industry category for the email")
    organization: Optional[str] = Field(None, description="Organization sending the email")
    user_id: Optional[str] = Field(None, description="User ID sending the email")
    db_type: str = Field("sqlite", description="Database type (sqlite or mongodb)")
    db_config: Dict[str, Any] = Field(default_factory=dict, description="Database configuration")
    smtp_server: Optional[str] = Field(None, description="SMTP server address")
    smtp_port: int = Field(587, description="SMTP server port")
    sender_email: Optional[str] = Field(None, description="Sender email address")
    sender_password: Optional[str] = Field(None, description="Sender email password")

    @field_validator('receiver_email')
    def validate_receiver_email(cls, v):
        """Validate receiver email format."""
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError("Invalid email format")
        return v


# Basic email validation is handled by the Pydantic model


@register_component("GmailComponent")
class GmailComponent(BaseComponent):
    """
    Component for sending emails and tracking responses.
    """

    def __init__(self):
        """Initialize the Gmail component."""
        logger.info("Initializing Gmail Component")
        super().__init__()

        # Set the request schema for automatic validation
        self.request_schema = SendEmailRequest

        logger.info("Gmail Component initialized successfully")

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an email sending request.

        Args:
            payload: The request payload

        Returns:
            A dictionary with the processing result
        """
        try:
            # Extract parameters
            receiver_email = payload.get("receiver_email")
            message = payload.get("message")
            subject = payload.get("subject")
            cc_emails = payload.get("cc_emails", [])
            bcc_emails = payload.get("bcc_emails", [])
            industry = payload.get("industry", "")
            organization = payload.get("organization", "")
            user_id = payload.get("user_id", "")

            # Get database configuration
            db_type = payload.get("db_type", "sqlite")
            db_config = payload.get("db_config", {})

            # Get SMTP configuration
            smtp_server = payload.get("smtp_server") or os.getenv("SMTP_SERVER")
            smtp_port = payload.get("smtp_port", 587)
            sender_email = payload.get("sender_email") or os.getenv("SENDER_EMAIL")
            sender_password = payload.get("sender_password") or os.getenv("SENDER_PASSWORD")

            # Validate required parameters
            if not smtp_server or not sender_email or not sender_password:
                return {
                    "success": False,
                    "error": "Missing SMTP configuration. Provide smtp_server, sender_email, and sender_password in payload or as environment variables."
                }

            # Create database handler
            db_handler = create_db_handler(db_type, **db_config)

            # Check if we've already sent an email to this domain
            receiver_domain = receiver_email.split('@')[1].lower()
            if db_handler.check_domain_sent(receiver_domain):
                logger.info(f"Skipping email to {receiver_email} as an email to domain {receiver_domain} was already sent.")
                return {
                    "success": False,
                    "error": f"Email to domain {receiver_domain} was already sent"
                }

            # Create email message
            msg = EmailMessage()
            msg['From'] = sender_email
            msg['To'] = receiver_email

            # Set subject (required)
            if not subject:
                return {
                    "success": False,
                    "error": "Email subject is required"
                }

            msg['Subject'] = subject

            # Add message content
            if message.strip().startswith('<'):  # Assume HTML if starts with <
                msg.add_alternative(message, subtype='html')
            else:
                msg.set_content(message)

            # Automatically generate and set the Message-ID
            msg['Message-ID'] = make_msgid()

            # Add CC and BCC
            if cc_emails:
                valid_cc = [email for email in cc_emails if "@" in email]
                if valid_cc:
                    msg['Cc'] = ", ".join(valid_cc)

            if bcc_emails:
                valid_bcc = [email for email in bcc_emails if "@" in email]
                if valid_bcc:
                    msg['Bcc'] = ", ".join(valid_bcc)

            # Send email
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls(context=ssl.create_default_context())
                server.login(sender_email, sender_password)
                server.send_message(msg)

            # Save record to database
            db_handler.save_email(
                message_id=msg['Message-ID'],
                recipient=receiver_email,
                industry=industry,
                forwarded=0,
                organization=organization,
                user_id=user_id
            )

            # Close database connection
            db_handler.close()

            logger.info(f"Email sent to {receiver_email} successfully!")
            return {
                "success": True,
                "message_id": msg['Message-ID'],
                "recipient": receiver_email
            }

        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
