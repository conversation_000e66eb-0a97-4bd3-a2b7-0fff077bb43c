"""
Select Data Component - Extracts elements from lists or dictionaries.
"""
import logging
import traceback
from typing import Dict, List, Any

try:
    from app.core_.base_component import BaseComponent, ValidationResult
    from app.core_.component_system import register_component
except ImportError as e:
    print(f"CRITICAL: Failed to import core components for SelectDataComponent: {e}")
    raise

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("SelectDataComponent")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


@register_component("SelectDataComponent")
class SelectDataExecutor(BaseComponent):
    """
    Component for extracting elements from lists or dictionaries.

    This component extracts elements from lists using index/slice notation
    or from dictionaries using key/path notation.
    """

    def __init__(self):
        """
        Initialize the SelectDataExecutor component.
        """
        super().__init__()
        logger.info("SelectDataExecutor initialized")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a select data payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.debug(f"Validating select data payload for request_id: {request_id}")

        try:
            # Check for required fields
            if "input_data" not in payload:
                error_msg = f"Missing required field 'input_data' in payload for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"input_data": "required field missing"})

            if "selector" not in payload:
                error_msg = f"Missing required field 'selector' in payload for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"selector": "required field missing"})

            # Validate data_type if present
            if "data_type" in payload:
                data_type = payload["data_type"]
                valid_types = ["Auto-Detect", "List", "Dictionary", None]
                if data_type not in valid_types:
                    error_msg = f"Invalid data_type: {data_type}. Must be one of: {', '.join([str(t) for t in valid_types if t is not None])} or None for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(is_valid=False, error_message=error_msg, error_details={"data_type": f"invalid value: {data_type}"})

            logger.debug(f"Data selection payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg, error_details={"validation_error": str(e)})


    def _select_from_list(self, data: List, selector: str, search_mode: str = "Exact Path", field_matching_mode: str = "Auto-detect") -> Any:
        """
        Selects elements from a list using index/slice notation or smart search.

        Args:
            data: The list to select from
            selector: Index (e.g., '0') or slice (e.g., '1:5') for exact path, or field name for smart search
            search_mode: "Exact Path" for traditional behavior, "Smart Search" for recursive field search
            field_matching_mode: "Auto-detect", "Key-based Only", or "Property-based Only"

        Returns:
            The selected element(s)
        """
        logger.debug(f"Selecting from list using selector: '{selector}' with search mode: '{search_mode}', field matching mode: '{field_matching_mode}'")
        try:
            if search_mode == "Smart Search":
                # Use smart search to find field anywhere in the list structure
                result = self._smart_search_field(data, selector, field_matching_mode)
                return result  # Return None if not found, let caller handle it

            # Original exact path logic for numeric indices
            # Check if selector is a slice
            if ":" in selector:
                parts = selector.split(":")
                start = int(parts[0]) if parts[0] else None
                end = int(parts[1]) if parts[1] else None
                logger.debug(f"Interpreted selector as slice: start={start}, end={end}")
                return data[start:end]
            else:
                # Single index
                index = int(selector)
                logger.debug(f"Interpreted selector as index: {index}")
                return data[index]
        except (ValueError, TypeError):
             logger.error(f"Invalid selector format for list: '{selector}'")
             raise ValueError(f"Invalid selector format for list: '{selector}'. Must be an integer index or slice (e.g., '0', '1:5').")
        except IndexError:
             logger.error(f"Index out of range for list with selector: '{selector}'")
             raise IndexError(f"Index out of range for list with selector: '{selector}'. List size: {len(data)}.")


    def _select_from_dict(self, data: Dict, selector: str, search_mode: str = "Exact Path", field_matching_mode: str = "Auto-detect") -> Any:
        """
        Selects elements from a dictionary using key or path notation, or smart search.

        Args:
            data: The dictionary to select from
            selector: Key name or path (e.g., 'user.name') for exact path, or field name for smart search
            search_mode: "Exact Path" for traditional behavior, "Smart Search" for recursive field search
            field_matching_mode: "Auto-detect", "Key-based Only", or "Property-based Only"

        Returns:
            The selected element
        """
        logger.debug(f"Selecting from dictionary using selector: '{selector}' with search mode: '{search_mode}', field matching mode: '{field_matching_mode}'")
        try:
            if search_mode == "Smart Search":
                # Use smart search to find field anywhere in the structure
                result = self._smart_search_field(data, selector, field_matching_mode)
                return result  # Return None if not found, let caller handle it

            if "." in selector:
                # Path notation (e.g., 'user.name')
                current = data
                path_keys = selector.split(".")
                logger.debug(f"Interpreted selector as path: {path_keys}")
                for i, key in enumerate(path_keys):
                    logger.debug(f"Navigating path, current key: '{key}' (step {i+1}/{len(path_keys)})")
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                        logger.debug(f"Found key '{key}', current data type: {type(current).__name__}")

                        # If we encounter a JSON string, try to parse it
                        if isinstance(current, str) and self._looks_like_json(current):
                            try:
                                import json
                                # Try to fix malformed JSON by adding missing braces
                                json_str = self._fix_malformed_json(current)
                                parsed = json.loads(json_str)
                                current = parsed
                                logger.debug(f"Parsed JSON string at step {i + 1} in path '{selector}', new type: {type(current).__name__}")
                            except (json.JSONDecodeError, ValueError) as e:
                                # If it's not valid JSON, continue with the string
                                logger.debug(f"Value at step {i + 1} looks like JSON but failed to parse: {e}, continuing as string")
                                pass

                    else:
                        logger.error(f"Key '{key}' not found in path '{selector}' at step {i+1}")
                        raise KeyError(f"Key '{key}' not found in path '{selector}'")
                return current
            else:
                # Simple key
                logger.debug(f"Interpreted selector as simple key: '{selector}'")
                value = data[selector]

                # If the value is a JSON string, try to parse it
                if isinstance(value, str) and self._looks_like_json(value):
                    try:
                        import json
                        # Try to fix malformed JSON by adding missing braces
                        json_str = self._fix_malformed_json(value)
                        parsed = json.loads(json_str)
                        logger.debug(f"Parsed JSON string for key '{selector}', new type: {type(parsed).__name__}")
                        return parsed
                    except (json.JSONDecodeError, ValueError) as e:
                        # If it's not valid JSON, return the string as-is
                        logger.debug(f"Value for key '{selector}' looks like JSON but failed to parse: {e}, returning as string")
                        pass

                return value
        except KeyError:
             # Re-raise the specific KeyError from above
             raise
        except TypeError:
             logger.error(f"Cannot select from non-dictionary type during path traversal with selector: '{selector}'")
             raise TypeError(f"Cannot select from non-dictionary type during path traversal with selector: '{selector}'")

    def _smart_search_field(self, data: Any, field_name: str, field_matching_mode: str = "Auto-detect") -> Any:
        """
        Recursively search for a field name anywhere in the data structure.

        Args:
            data: The data structure to search in
            field_name: The field name to search for
            field_matching_mode: "Auto-detect", "Key-based Only", or "Property-based Only"

        Returns:
            The value of the first occurrence of the field, or None if not found
        """
        logger.debug(f"Smart searching for field '{field_name}' in {type(data).__name__} with mode '{field_matching_mode}'")

        if isinstance(data, dict):
            # Key-based search
            if field_matching_mode in ["Auto-detect", "Key-based Only"]:
                if field_name in data:
                    logger.debug(f"Found field '{field_name}' directly in dictionary (key-based)")
                    return data[field_name]

            # Property-based search in dictionary
            if field_matching_mode in ["Auto-detect", "Property-based Only"]:
                property_result = self._search_property_based_in_structure(data, field_name)
                if property_result is not None:
                    logger.debug(f"Found field '{field_name}' in dictionary (property-based)")
                    return property_result

            # Recursively search in all values (respecting field matching mode)
            for key, value in data.items():
                result = self._smart_search_field(value, field_name, field_matching_mode)
                if result is not None:
                    logger.debug(f"Found field '{field_name}' in nested structure under key '{key}'")
                    return result

        elif isinstance(data, list):
            # Property-based search in list
            if field_matching_mode in ["Auto-detect", "Property-based Only"]:
                mcp_result = self._search_mcp_property_structure(data, field_name)
                if mcp_result is not None:
                    logger.debug(f"Found field '{field_name}' in list (property-based)")
                    return mcp_result

            # Recursively search in each item of the list (respecting field matching mode)
            for i, item in enumerate(data):
                result = self._smart_search_field(item, field_name, field_matching_mode)
                if result is not None:
                    logger.debug(f"Found field '{field_name}' in list item at index {i}")
                    return result

        # Field not found in this branch
        return None

    def _search_property_based_in_structure(self, data: Any, property_name: str) -> Any:
        """
        Search for property-based field in a data structure.

        This method looks for objects with 'property_name' and 'data' fields,
        where property_name matches the target field name.

        Args:
            data: The data structure to search in
            property_name: The property name to search for

        Returns:
            The data value for the matching property_name, or None if not found
        """
        if isinstance(data, dict):
            # Check if this dict itself has the property structure
            if ("property_name" in data and
                "data" in data and
                data["property_name"] == property_name):
                logger.debug(f"Found property-based field '{property_name}' in dict")
                return data["data"]

            # Recursively search in nested structures
            for key, value in data.items():
                result = self._search_property_based_in_structure(value, property_name)
                if result is not None:
                    return result

        elif isinstance(data, list):
            # Search through array for property-based structure
            for item in data:
                result = self._search_property_based_in_structure(item, property_name)
                if result is not None:
                    return result

        return None

    def _search_mcp_property_structure(self, data: List, property_name: str) -> Any:
        """
        Search for MCP-style property structure in a list.

        This method specifically looks for MCP format:
        [{"property_name": "field", "data": "value", "data_type": "string"}, ...]

        Args:
            data: The list to search in
            property_name: The property name to search for

        Returns:
            The data value for the matching property_name, or None if not found
        """
        if isinstance(data, list):
            # Search through array for property-based structure
            for item in data:
                if (isinstance(item, dict) and
                    "property_name" in item and
                    "data" in item and
                    item["property_name"] == property_name):
                    logger.debug(f"Found property-based field '{property_name}' with data: {type(item['data']).__name__}")
                    return item["data"]
        elif isinstance(data, dict):
            # Check if this dict itself has the property structure
            if ("property_name" in data and
                "data" in data and
                data["property_name"] == property_name):
                logger.debug(f"Found property-based field '{property_name}' in dict")
                return data["data"]

        return None

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the data selection operation.

        Args:
            payload: The request payload containing:
                - input_data: The data to select from
                - selector: The selector expression
                - data_type: The type of data structure (optional)
                - search_mode: The search mode (optional)
                - field_matching_mode: The field matching mode (optional)

        Returns:
            A dictionary containing the result of the operation
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing select data request for request_id: {request_id}")
        logger.debug(f"Payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            # Use the tool_parameters as the actual payload
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # Get inputs from parameters
            input_data = parameters.get("input_data")
            selector = parameters.get("selector")
            data_type = parameters.get("data_type", "Auto-Detect")
            search_mode = parameters.get("search_mode", "Exact Path")
            field_matching_mode = parameters.get("field_matching_mode", "Auto-detect")

            # Adjust selector if data is wrapped in a 'value' field
            original_selector = selector
            selector = self._adjust_selector_for_wrapped_data(input_data, selector)

            logger.debug(f"Original selector: '{original_selector}', Adjusted selector: '{selector}' for request_id {request_id}")
            logger.debug(f"Using search mode: '{search_mode}', field matching mode: '{field_matching_mode}' for request_id {request_id}")

            # Check if input_data is None
            if input_data is None:
                error_msg = f"Input data is None for request_id {request_id}"
                logger.error(error_msg)
                return {"output_data": None, "error": error_msg}

            # Auto-detect data type if needed
            if data_type == "Auto-Detect" or data_type is None:
                logger.debug(f"Auto-detecting data type for input_data (type {type(input_data).__name__}) for request_id {request_id}")
                if isinstance(input_data, list):
                    data_type = "List"
                    logger.debug(f"Auto-detected data type: List for request_id {request_id}")
                elif isinstance(input_data, dict):
                    data_type = "Dictionary"
                    logger.debug(f"Auto-detected data type: Dictionary for request_id {request_id}")
                else:
                    error_msg = f"Cannot auto-detect data type for {type(input_data).__name__}. Please specify data type for request_id {request_id}."
                    logger.error(error_msg)
                    return {"output_data": None, "error": error_msg}
            else:
                logger.debug(f"Using specified data type: {data_type} for request_id {request_id}")

            # Select data based on type
            try:
                if data_type == "List":
                    if not isinstance(input_data, list):
                        error_msg = f"Expected a list for data_type 'List', got {type(input_data).__name__} for request_id {request_id}"
                        logger.error(error_msg)
                        return {"output_data": None, "error": error_msg}

                    logger.debug(f"Selecting from list using selector: '{selector}' with search mode: '{search_mode}', field matching mode: '{field_matching_mode}' for request_id {request_id}")
                    result = self._select_from_list(input_data, selector, search_mode, field_matching_mode)

                elif data_type == "Dictionary":
                    if not isinstance(input_data, dict):
                        error_msg = f"Expected a dictionary for data_type 'Dictionary', got {type(input_data).__name__} for request_id {request_id}"
                        logger.error(error_msg)
                        return {"output_data": None, "error": error_msg}

                    logger.debug(f"Selecting from dictionary using selector: '{selector}' with search mode: '{search_mode}', field matching mode: '{field_matching_mode}' for request_id {request_id}")
                    result = self._select_from_dict(input_data, selector, search_mode, field_matching_mode)

                else:
                    error_msg = f"Unsupported data type: {data_type}. Must be one of: 'Auto-Detect', 'List', 'Dictionary', or None for request_id {request_id}"
                    logger.error(error_msg)
                    return {"output_data": None, "error": error_msg}

                logger.info(f"Data selected successfully for request_id {request_id}. Result type: {type(result).__name__}")
                logger.debug(f"Selection result for request_id {request_id}: {result}")
                return {"output_data": result, "error": None}

            except (IndexError, KeyError, ValueError) as e:
                error_msg = f"Selection error: {str(e)} for request_id {request_id}"
                logger.error(error_msg)
                logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
                return {"output_data": None, "error": error_msg}

        except Exception as e:
            # Catch all exceptions and return error status
            error_msg = f"Unexpected error during data selection for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {"output_data": None, "error": error_msg}

    def _adjust_selector_for_wrapped_data(self, input_data, selector):
        """
        Adjust the selector if the input data is wrapped in a 'value' field.

        This handles cases where dual-purpose inputs wrap data like:
        {"value": {...}} and the user provides selector "data.script"
        We automatically convert it to "value.data.script"

        Args:
            input_data: The input data structure
            selector: The original selector

        Returns:
            The adjusted selector
        """
        # Check if input_data is wrapped in a 'value' field
        if (isinstance(input_data, dict) and
            len(input_data) == 1 and
            "value" in input_data and
            not selector.startswith("value.")):

            adjusted_selector = f"value.{selector}"
            logger.debug(f"Adjusted selector from '{selector}' to '{adjusted_selector}' for wrapped data")
            return adjusted_selector

        return selector

    def _looks_like_json(self, value):
        """
        Check if a string looks like it could be JSON.

        Args:
            value: The string to check

        Returns:
            True if the string looks like JSON, False otherwise
        """
        if not isinstance(value, str):
            return False

        # Remove leading/trailing whitespace
        value = value.strip()

        # Check for standard JSON patterns
        if (value.startswith('{') and value.endswith('}')) or \
           (value.startswith('[') and value.endswith(']')):
            return True

        # Check for malformed JSON that starts with a key (missing opening brace)
        if value.startswith('"') and ':' in value and ('}' in value or ']' in value):
            return True

        return False

    def _fix_malformed_json(self, value):
        """
        Try to fix common malformed JSON patterns.

        Args:
            value: The potentially malformed JSON string

        Returns:
            A fixed JSON string
        """
        value = value.strip()

        # If it starts with a quote and contains colons, it might be missing opening brace
        if value.startswith('"') and ':' in value and not value.startswith('{'):
            # Add missing opening brace
            value = '{' + value
            logger.debug(f"Added missing opening brace to JSON string")

        # Count braces to see if we need to add closing braces
        open_braces = value.count('{')
        close_braces = value.count('}')

        if open_braces > close_braces:
            # Add missing closing braces
            missing_braces = open_braces - close_braces
            value = value + ('}' * missing_braces)
            logger.debug(f"Added {missing_braces} missing closing brace(s)")

        return value


# Verify registration at module load time
try:
    from app.core_.component_system import COMPONENT_REGISTRY
    if "SelectDataComponent" in COMPONENT_REGISTRY:
        logger.info("SelectDataComponent successfully registered in COMPONENT_REGISTRY")
    else:
        logger.error("SelectDataComponent NOT found in COMPONENT_REGISTRY after registration")
except Exception as e:
    logger.error(f"Failed to verify SelectDataComponent registration: {e}")