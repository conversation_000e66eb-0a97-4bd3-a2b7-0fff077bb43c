# Kafka connection settings
KAFKA_BOOTSTRAP_SERVERS="localhost:9092"

# Node Executor Ka<PERSON>ka topics
KAFKA_CONSUMER_TOPIC="node-execution-request"
KAFKA_RESULTS_TOPIC="node_results"
KAFKA_TOOL_EXECUTOR_LOGS_TOPIC="tool_executor_logs"
KAFKA_CONSUMER_GROUP_ID="node_executor_service"
DEFAULT_NODE_RETRIES=3

# Logging settings
LOG_LEVEL="INFO"

# Security settings

# API Component Security
SECURITY_API_URL_ALLOWLIST=["example.com", "api.example.com", "api.weatherapi.com"]
SECURITY_API_URL_BLOCKLIST=["localhost", "127.0.0.1", "0.0.0.0", "::1"]
SECURITY_API_MAX_TIMEOUT=60
SECURITY_API_ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]
SECURITY_API_MAX_CONTENT_SIZE=10485760
SECURITY_API_ALLOWED_CONTENT_TYPES=["application/json", "text/plain", "text/html", "application/xml"]
SECURITY_API_ENABLE_PROXY=false

# Document Component Security
SECURITY_DOC_ALLOWED_PATHS=["./data", "./documents"]
SECURITY_DOC_BLOCKED_PATHS=["/etc", "/var", "/usr", "/bin", "/sbin", "C:\\Windows", "C:\\Program Files"]
SECURITY_DOC_MAX_FILE_SIZE=10485760

# Rate Limiting
SECURITY_RATE_LIMITING_ENABLED=true

# Authentication (placeholder for future implementation)
SECURITY_AUTH_ENABLED=false

# Kafka Security (uncomment and configure as needed)
# SECURITY_KAFKA_SECURITY_PROTOCOL="SSL"
# SECURITY_KAFKA_SASL_MECHANISM="PLAIN"
# SECURITY_KAFKA_SASL_USERNAME="user"
# SECURITY_KAFKA_SASL_PASSWORD="password"
# SECURITY_KAFKA_SSL_CAFILE="/path/to/ca.pem"
# SECURITY_KAFKA_SSL_CERTFILE="/path/to/cert.pem"
# SECURITY_KAFKA_SSL_KEYFILE="/path/to/key.pem"

# Resource Limits
SECURITY_MAX_CONCURRENT_TASKS=50
SECURITY_TASK_TIMEOUT=300
SECURITY_WORKER_THREADS=4

# Logging Security
SECURITY_SANITIZE_LOGS=true
SECURITY_LOG_SENSITIVE_FIELDS=["password", "token", "secret", "key", "auth", "credential", "apikey", "api_key", "access_token"]
SECURITY_ENABLE_AUDIT_LOGGING=true
# SECURITY_AUDIT_LOG_FILE="./logs/audit.log"

# Component Validation
SECURITY_STRICT_VALIDATION=true

# Content Security
SECURITY_SCAN_FOR_MALICIOUS_CONTENT=true
SECURITY_MALICIOUS_CONTENT_PATTERNS=["<!--#exec", "<?php", "<script>", "eval(", "document.cookie", "javascript:", "onload=", "onerror=", "fetch(", "XMLHttpRequest"]

# Network Security
SECURITY_ENABLE_NETWORK_ISOLATION=false
# SECURITY_ALLOWED_NETWORKS="***********/24,10.0.0.0/8"  # Uncomment to restrict network access

# Components are auto-discovered and their topics are auto-generated.

# Weather API settings
WEATHER_API_KEY="your_api_key_here"
WEATHER_API_BASE_URL="http://api.weatherapi.com/v1"
