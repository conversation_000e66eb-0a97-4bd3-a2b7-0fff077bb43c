stages:
 - setup
 - build
 - notify_build
 - deploy
 - notify_final


setup:
  stage: setup
  image: google/cloud-sdk
  variables:
      GOOGLE_SERVICE_KEY: $SERVICE_ACCOUNT_KEY
  script:
      - mkdir -p files
      - echo "$GOOGLE_SERVICE_KEY" > service-account-key.json
      - gcloud auth activate-service-account --key-file=service-account-key.json --project=${PROJECT_ID}
      - gcloud auth print-access-token > files/gcloud_token
  artifacts:
      untracked: false
      when: on_success
      expire_in: 1 hr
      paths:
          - files
  only:
    - dev        

build:
  stage: build
  image: docker:latest
  tags:
    - self-hosted

  services:
    - docker:dind
  dependencies:
      - setup
  variables:
      GOOGLE_SERVICE_KEY: $SERVICE_ACCOUNT_KEY
      GAR_HOSTNAME: "us-central1-docker.pkg.dev"
      PROJECT_ID: "$PROJECT_ID"
      REPOSITORY: "$REPOSITORY"
      IMAGE_NAME: "$CI_PROJECT_NAME"
      REPO_URL: $REPO_URL

  before_script:
      - cat files/gcloud_token | docker login -u oauth2accesstoken --password-stdin https://us-central1-docker.pkg.dev
  script:
    - |
      docker build \
        -t "us-central1-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$IMAGE_NAME:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}" .
    - docker push "us-central1-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$IMAGE_NAME:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}"
  only:
  - dev

slack_notification_build:
  stage: notify_build
  image: curlimages/curl:7.86.0
  script:
    - |
      MESSAGE="Build failed: $CI_COMMIT_REF_NAME $CI_PIPELINE_URL"
    - |
      curl -H "Content-type: application/json" --data '{"channel": "ruh-cicd","text": "'"${MESSAGE}"'"}' -X POST ${SLACK_WEBHOOK_SECRET}
    - exit 1
  only:
    - qa
    - dev
    - prod
  when: on_failure



deploy:
  stage: deploy
  image: google/cloud-sdk:latest
  variables:
      GOOGLE_SERVICE_KEY: $SERVICE_ACCOUNT_KEY
      GAR_HOSTNAME: "us-central1-docker.pkg.dev"
      PROJECT_ID: "$PROJECT_ID"
      REPOSITORY: "$REPOSITORY"
      IMAGE_NAME: "$CI_PROJECT_NAME" 
  script:
    - echo "$GOOGLE_SERVICE_KEY" > service-account-key.json  # Save the service account key to a file
    - gcloud auth activate-service-account --key-file=service-account-key.json --project=${PROJECT_ID}
    - gcloud container clusters get-credentials ${CLUSTER_NAME} --region ${REGION} --project ${PROJECT_ID}
    - DEPLOYMENT_EXISTS=$(kubectl get deployment -n $REPOSITORY node-executor-service-ai-dp --no-headers --ignore-not-found | wc -l)
    # If the deployment doesn't exist, create it and deploy the application
    -  |
      if [ "$DEPLOYMENT_EXISTS" -eq "0" ]; then
          echo " Deployment not existed so creating new one"
          sed -i \
          -e "s|<VERSION>|$CI_COMMIT_SHORT_SHA|g" \
          -e "s|<PROJECT_ID>|$PROJECT_ID|g" \
          -e "s|<REPOSITORY>|$REPOSITORY|g" \
          -e "s|<IMAGE_NAME>|$IMAGE_NAME|g" \
          -e "s|<ENV>|$CI_COMMIT_REF_NAME|g" \
          k8s-manifest-$CI_COMMIT_REF_NAME.yml
          kubectl apply -f k8s-manifest-$CI_COMMIT_REF_NAME.yml
        
      else
          echo "Rolling updates "
          kubectl set image deployment/node-executor-service-ai-dp node-executor-service-ai=us-central1-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$IMAGE_NAME:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA  -n $REPOSITORY
          fi
    - echo "Successfully deployed to GKE"
  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS_EMOJI="✅"
        STATUS_TEXT="succeeded"
      else
        STATUS_EMOJI="❌"
        STATUS_TEXT="failed"
      fi
      echo "CI_JOB_STATUS: $CI_JOB_STATUS"
      echo "CI_PIPELINE_URL: $CI_PIPELINE_URL"
      
      curl -X POST -H 'Content-type: application/json' \
        --data "{\"channel\":\"ruh-cicd\",\"text\":\"${STATUS_EMOJI} Pipeline ${STATUS_TEXT}! Stage: $CI_JOB_STAGE. Check the logs: $CI_PIPELINE_URL\"}" \
        $SLACK_WEBHOOK_SECRET 
  dependencies:
    - build
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "qa" || $CI_COMMIT_REF_NAME == "uat"