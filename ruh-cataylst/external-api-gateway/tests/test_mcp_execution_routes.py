import pytest
import json
import sys
import os
from unittest.mock import patch, AsyncMock
from fastapi import status
from fastapi.testclient import Test<PERSON>lient

# Add the app directory to the path so we can import from it
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from app.main import app
from app.services.kafka_service import kafka_service

# Create a test client
client = TestClient(app)


@pytest.fixture
def mock_kafka_service():
    """Mock the kafka_service.send_mcp_execution_request method"""
    with patch.object(
        kafka_service, "send_mcp_execution_request", new_callable=AsyncMock
    ) as mock_send:
        yield mock_send


def test_execute_mcp_server_endpoint_success(mock_kafka_service):
    """Test successful MCP server execution"""
    # Mock the response from kafka_service
    mock_kafka_service.return_value = {"result": "success", "data": "test_data"}

    # Test data
    request_data = {
        "server_script_path": "test_path",
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
    }

    # Make the request
    response = client.post("/api/v1/mcp-execute/server", json=request_data)

    # Verify the response
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"result": "success", "data": "test_data"}

    # Verify that kafka_service.send_mcp_execution_request was called with the correct data
    mock_kafka_service.assert_called_once_with(request_data)


def test_execute_mcp_server_endpoint_error(mock_kafka_service):
    """Test MCP server execution with error"""
    # Mock the kafka_service to raise an exception
    mock_kafka_service.side_effect = Exception("Test error")

    # Test data
    request_data = {
        "server_script_path": "test_path",
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
    }

    # Make the request
    response = client.post("/api/v1/mcp-execute/server", json=request_data)

    # Verify the response
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert "Internal server error" in response.json()["detail"]


def test_execute_mcp_server_async_endpoint_success(mock_kafka_service):
    """Test successful async MCP server execution"""
    # Mock the response from kafka_service
    mock_kafka_service.return_value = "test-request-id"

    # Test data
    request_data = {
        "server_script_path": "test_path",
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
    }

    # Make the request
    response = client.post("/api/v1/mcp-execute/server/async", json=request_data)

    # Verify the response
    assert response.status_code == status.HTTP_202_ACCEPTED
    assert response.json() == {"request_id": "test-request-id", "status": "accepted"}

    # Verify that kafka_service.send_mcp_execution_request was called with the correct data
    # The async_execution flag should be added to the request data
    expected_request_data = request_data.copy()
    expected_request_data["async_execution"] = True
    mock_kafka_service.assert_called_once_with(expected_request_data)


def test_execute_mcp_server_validation_error():
    """Test MCP server execution with validation error"""
    # Test data with missing required field
    request_data = {
        # Missing server_script_path
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
    }

    # Make the request
    response = client.post("/api/v1/mcp-execute/server", json=request_data)

    # Verify the response
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "field required" in response.json()["detail"][0]["msg"]
