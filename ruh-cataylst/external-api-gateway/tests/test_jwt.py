from jose import jwt
import json
import os
from dotenv import load_dotenv

load_dotenv()

# Your JWT token
token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************.i9ZUqUzyRsxLFzUEYCKfFD58nwfTknbjlq8K4oE7R98'

# JWT secret from .env
jwt_secret = os.getenv('JWT_SECRET_KEY', '4f93b5f0d1c834b1f2a92b6cc2192a9e2c6f3c1d5fd3491e98263a3ea50e8d3b5aeaa298e3e7b06c15a27d0c81210f58a0c3d5d78b011fa4d3e24b2f83cf98b7')

print(f"JWT Secret from env: {jwt_secret}")
print(f"Token: {token[:50]}...")

try:
    # Decode without verification first to see the payload
    unverified = jwt.decode(token, options={'verify_signature': False})
    print('\nUnverified payload:')
    print(json.dumps(unverified, indent=2))
    
    # Now try to decode with verification
    verified = jwt.decode(token, jwt_secret, algorithms=['HS256'])
    print('\nVerified payload:')
    print(json.dumps(verified, indent=2))
    
except jwt.ExpiredSignatureError:
    print('\nToken has expired')
except jwt.JWTError as e:
    print(f'\nJWT Error: {e}')
except Exception as e:
    print(f'\nError: {e}')

# Test Redis connection
print("\n" + "="*50)
print("Testing Redis connection...")

try:
    from app.utils.redis.redis_service import RedisService
    redis_service = RedisService()
    
    # Test basic connection
    print("Redis connection established successfully")
    
    # Test the specific key pattern used in auth
    email = "<EMAIL>"
    env = os.getenv('ENV', 'dev')
    redis_login_token_hash_key = "_user_login_"
    key = f"{env}{redis_login_token_hash_key}{email}"
    
    print(f"Looking for Redis key: {key}")
    session = redis_service.get_data_from_redis(key, "token")
    print(f"Session data found: {session is not None}")
    if session:
        print(f"Session data: {session}")
    
except Exception as e:
    print(f"Redis connection error: {e}")
