import pytest
import json
import sys
import os
from unittest.mock import patch, MagicMock

# Add the app directory to the path so we can import from it
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Mock the requests module to prevent actual HTTP requests
sys.modules["requests"] = MagicMock()

# Now import the functions
from app.test import test_mcp_execution


@pytest.fixture
def mock_response():
    """Create a mock response object for testing."""
    mock_resp = MagicMock()
    mock_resp.status_code = 202
    mock_resp.headers = {"Content-Type": "application/json"}
    mock_resp.json.return_value = {"RequestId": "test-request-id-123"}
    return mock_resp


def test_mcp_execution_function(mock_response):
    """Test the MCP execution function."""
    # Test data
    server_script_path = "https://test-server.com/sse"
    tool_name = "TestTool"
    tool_parameters = {"param1": "value1", "param2": "value2"}

    # Mock the send_api_request function
    with patch("app.test.send_api_request", return_value=mock_response) as mock_send:
        # Call the function
        result = test_mcp_execution(server_script_path, tool_name, tool_parameters)

        # Verify the result
        assert result == "test-request-id-123"

        # Verify that send_api_request was called with the correct arguments
        mock_send.assert_called_once()
        args = mock_send.call_args[0]
        assert args[0] == "/mcp-execute/server"
        assert args[1] == {
            "server_script_path": server_script_path,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
        }


def test_mcp_execution_function_error():
    """Test the MCP execution function when the API call fails."""
    # Test data
    server_script_path = "https://test-server.com/sse"
    tool_name = "TestTool"
    tool_parameters = {"param1": "value1", "param2": "value2"}

    # Mock the send_api_request function to return None (error case)
    with patch("app.test.send_api_request", return_value=None) as mock_send:
        # Call the function
        result = test_mcp_execution(server_script_path, tool_name, tool_parameters)

        # Verify the result
        assert result is None

        # Verify that send_api_request was called with the correct arguments
        mock_send.assert_called_once()
        args = mock_send.call_args[0]
        assert args[0] == "/mcp-execute/server"
        assert args[1] == {
            "server_script_path": server_script_path,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
        }
