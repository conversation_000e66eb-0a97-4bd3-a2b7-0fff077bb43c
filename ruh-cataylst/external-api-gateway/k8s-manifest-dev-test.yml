apiVersion: v1
kind: ServiceAccount
metadata:
  name: external-gateway-service-test-ai-sa
  namespace: ruh-catalyst
  labels:
    name: external-gateway-service-test-ai-sa
    namespace: ruh-catalyst
    app: external-gateway-service-test-ai
    deployment: external-gateway-service-test-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: external-gateway-service-test-ai-dp
  namespace: ruh-catalyst
  labels:
    name: external-gateway-service-test-ai-dp
    namespace: ruh-catalyst
    app: external-gateway-service-test-ai
    serviceaccount: external-gateway-service-test-ai-sa
    deployment: external-gateway-service-test-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: external-gateway-service-test-ai
      deployment: external-gateway-service-test-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-catalyst
        app: external-gateway-service-test-ai
        deployment: external-gateway-service-test-ai-dp
    spec:
      serviceAccountName: external-gateway-service-test-ai-sa      
      containers:
      - name: external-gateway-service-test-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 8000
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: external-gateway-service-test-ai-svc
  namespace: ruh-catalyst
spec:
  selector:
    app: external-gateway-service-test-ai
    deployment: external-gateway-service-test-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:external-gateway-service-test-node-executor-hpa
#   namespace:ruh-catalyst
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:external-gateway-service-test-node-executor-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: external-gateway-service-test-ingress
  namespace: ruh-catalyst
spec:
  ingressClassName: nginx
  rules:
  - host: external-gateway-service-test-dev.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: external-gateway-service-test-ai-svc
            port:
              number: 80

