from fastapi import API<PERSON>outer, Depends, HTTPException, status
from fastapi.responses import StreamingResponse, JSONResponse
import traceback
from app.core.auth_guard import role_required
from app.schemas.kafka import (
    ServerWorkflowRequest,
    WorkflowRequest,
    ExecutionRequest,
    ApprovalRequest,
)
from app.services.kafka_service import kafka_service
from app.core.security import validate_server_auth_key

workflow_orchestration_router = APIRouter(prefix="/workflow-execute", tags=["WorkflowExecute"])


@workflow_orchestration_router.post(
    "/execute",
    response_model=str,
    status_code=status.HTTP_202_ACCEPTED,
    name="Start Workflow",
)
async def start_workflow_endpoint(
    workflow_request: WorkflowRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
) -> JSONResponse:
    """
    Endpoint to start a new workflow (Request-Response).
    Returns correlation ID in JSON response.
    """
    try:
        user_id = current_user["user_id"]

        print(f"[USER_ID] {user_id}")
        workflow_data = workflow_request.dict()

        workflow_data["user_id"] = user_id

        correlation_id = await kafka_service.send_kafka_workflow_request(
            workflow_data, approval=workflow_request.approval
        )

        return JSONResponse(
            content={"correlationId": correlation_id}, status_code=status.HTTP_202_ACCEPTED
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@workflow_orchestration_router.post(
    "/server/execute",
    response_model=str,
    status_code=status.HTTP_202_ACCEPTED,
    name="Start Workflow Server",
    dependencies=[Depends(validate_server_auth_key)],
)
async def start_workflow_server_endpoint(
    workflow_request: ServerWorkflowRequest,
) -> JSONResponse:
    """
    Endpoint to start a new workflow (Request-Response).
    Returns correlation ID in JSON response.
    """
    try:
        workflow_data = workflow_request.dict()

        correlation_id = await kafka_service.send_kafka_workflow_request(
            workflow_data, approval=workflow_request.approval
        )

        return JSONResponse(
            content={"correlationId": correlation_id}, status_code=status.HTTP_202_ACCEPTED
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@workflow_orchestration_router.post(
    "/re-execute",
    response_model=str,
    status_code=status.HTTP_202_ACCEPTED,
    name="Send Execution Request",
)
async def send_execution_request_endpoint(
    execution_request: ExecutionRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
) -> JSONResponse:
    """
    Endpoint to send an execution request (retry, regenerate, re-execute) (Request-Response).
    Returns correlation ID in JSON response.
    """
    try:
        user_id = current_user["user_id"]

        correlation_id_exec = getattr(execution_request, "correlationId", None)
        action = getattr(execution_request, "action", None)
        node_id = getattr(execution_request, "node_id", None)

        # Ensure mandatory fields exist
        if correlation_id_exec is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Missing correlationId"
            )
        if action is None:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Missing action")
        if node_id is None:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Missing node_id")

        # Dynamically build kwargs only if attributes exist
        kafka_request_kwargs = {
            "correlation_id": correlation_id_exec,
            "action": action,
            "node_id": node_id,
        }

        if hasattr(execution_request, "data") and execution_request.data:
            kafka_request_kwargs["data"] = execution_request.data.dict()
            kafka_request_kwargs["data"]["user_id"] = user_id

        if hasattr(execution_request, "params"):
            kafka_request_kwargs["params"] = execution_request.params

        correlation_id = await kafka_service.send_kafka_execution_request(**kafka_request_kwargs)

        return JSONResponse(
            content={"correlationId": correlation_id}, status_code=status.HTTP_202_ACCEPTED
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = traceback.format_exc()
        print(error_details)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@workflow_orchestration_router.post(
    "/approve",
    response_model=str,
    status_code=status.HTTP_202_ACCEPTED,
    name="Send Approval Request",
)
async def send_approval_request_endpoint(approval_request: ApprovalRequest) -> JSONResponse:
    """
    Endpoint to send an approval request (approve/reject) (Request-Response).
    Returns correlation ID in JSON response.
    """
    try:
        correlation_id_approval = approval_request.correlationId
        correlation_id = await kafka_service.send_kafka_approval_request(
            correlation_id=correlation_id_approval, decision=approval_request.decision
        )
        return JSONResponse(
            content={"correlationId": correlation_id}, status_code=status.HTTP_202_ACCEPTED
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@workflow_orchestration_router.get("/stream/{correlation_id}")
async def stream_workflow_updates(correlation_id: str) -> StreamingResponse:
    """Streams workflow updates via SSE by consuming Kafka messages for a specific correlation ID."""
    return StreamingResponse(
        kafka_service.kafka_response_generator(correlation_id), media_type="text/event-stream"
    )
