from fastapi import APIRouter
from fastapi.responses import StreamingResponse
import json
import queue
import logging

from app.helper.sse_manager import SseManager
from app.schemas.sse import SSERequestSchema


sse_router = APIRouter(prefix="/sse", tags=["sse"])

# Create a singleton SSE manager
sse_manager = SseManager()

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


@sse_router.get(
    "",
    summary="Server-Sent Events Endpoint",
    description="Establishes a Server-Sent Events (SSE) connection for real-time updates. "
    "Clients must provide a `user_id` as a query parameter to uniquely identify themselves.",
    response_description="A streaming response with SSE events.",
)
def get_sse(api_key: str, key_type: str, correlation_id: str):
    """
    Server-Sent Events (SSE) endpoint for real-time updates.

    This endpoint allows clients to establish an SSE connection to receive real-time updates.
    Clients must provide a `user_id` as a query parameter to uniquely identify themselves.

    - **user_id**: Unique identifier for the client.
    """

    user_id = get_user_id_from_keys(api_key, key_type, correlation_id)

    # Use the user_id as the client_id
    client_id = user_id
    client_queue = queue.Queue()

    def event_stream():
        """
        Generator function to stream events to the client.
        """
        try:
            # Add the client to the SSE manager
            sse_manager.add_client(client_id, client_queue)

            while not sse_manager.is_shutting_down:
                try:
                    # Wait for an event from the queue
                    event = client_queue.get(timeout=10)
                    # Only process events meant for this client or broadcast events
                    if event.get("client_id") is None or event.get("client_id") == client_id:
                        yield (
                            f"event: {event['event']}\ndata: {json.dumps(event['data'])}"
                            f"\ntype: {event['type']}\nid: {event['timestamp']}\n\n"
                        )
                except queue.Empty:
                    # Send a keep-alive event if no events are received
                    yield "event: keep-alive\ndata: keep-alive\n\n"

        except GeneratorExit:
            # Handle client disconnection
            logger.info(f"Client {client_id} connection closed")
        finally:
            # Remove the client from the SSE manager
            sse_manager.remove_client(client_id)

    # Return a streaming response with the event stream
    return StreamingResponse(event_stream(), media_type="text/event-stream")


@sse_router.post(
    "/trigger-sample-event/{target_client_id}",
    summary="Trigger Sample Event for a Specific Client",
    description="Triggers a sample SSE event for a specific client identified by `target_client_id`.",
    response_description="A confirmation message indicating the event was triggered.",
)
def trigger_sample_event(target_client_id: str):
    """
    Trigger a sample SSE event for a specific client.

    - **target_client_id**: The unique identifier of the client to send the event to.
    """
    # Send event to the specific client
    sse_manager.send_update(
        "video_stream",
        {"message": f"Sample event triggered for client {target_client_id}"},
        client_id=target_client_id,
    )
    return {"status": f"Event triggered for client {target_client_id}"}


@sse_router.post(
    "/trigger-broadcast-event",
    summary="Trigger Broadcast Event for All Clients",
    description="Triggers a sample SSE event that is broadcast to all connected clients.",
    response_description="A confirmation message indicating the broadcast event was triggered.",
)
def trigger_broadcast_event():
    """
    Trigger a sample SSE event for all connected clients.

    This endpoint broadcasts an event to all clients connected via the SSE endpoint.
    """
    # Broadcast event to all clients
    sse_manager.send_update(
        "video_stream", {"message": "Broadcast event triggered"}, client_id=None
    )
    return {"status": "Broadcast event triggered"}


def get_user_id_from_keys(api_key: str, key_type: str, correlation_id: str) -> str:
    """
    Placeholder function to get user ID from API key and key type.
    In a real application, this would query a database or an external service.
    """
    # Simulate a user ID retrieval based on API key and key type
    if api_key == "test" and key_type == "auth":
        return str(correlation_id)
    else:
        raise ValueError("Invalid API key or key type")
