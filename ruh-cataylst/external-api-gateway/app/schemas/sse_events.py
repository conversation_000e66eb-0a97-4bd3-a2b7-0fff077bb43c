"""
SSE Event Names Enum

This module defines all the possible event names that can be sent via Server-Sent Events (SSE)
from the external API gateway. These event names help frontend applications to properly
handle different types of events and implement appropriate logic for each event type.
"""

from enum import Enum


class SSEEventNames(str, Enum):
    """
    Enumeration of all possible SSE event names sent by the external API gateway.
    
    These event names are used in the 'event:' field of SSE messages to help
    frontend applications identify and handle different types of events appropriately.
    """
    
    # Connection Events
    CONNECTION = "connection"
    """Initial connection event sent when SSE stream is established"""
    
    # Keep-Alive Events
    KEEP_ALIVE = "keep-alive"
    """Keep-alive event sent periodically to maintain connection"""
    
    # Workflow Events
    WORKFLOW_COMPLETED = "workflow-completed"
    """Workflow execution completed successfully"""
    
    WORKFLOW_FAILED = "workflow-failed"
    """Workflow execution failed"""
    
    WORKFLOW_CANCELLED = "workflow-cancelled"
    """Workflow execution was cancelled"""
    
    WORKFLOW_UPDATE = "workflow-update"
    """General workflow status update (running, pending, etc.)"""
    
    # MCP (Model Context Protocol) Events
    MCP_SUCCESS = "mcp-success"
    """MCP tool execution completed successfully"""
    
    MCP_ERROR = "mcp-error"
    """MCP tool execution failed with error"""
    
    MCP_UPDATE = "mcp-update"
    """General MCP execution status update (processing, etc.)"""
    
    # Error Events
    ERROR = "error"
    """General error event for any error conditions"""
    
    # Generic Events
    UPDATE = "update"
    """Generic update event for any other data"""
    
    # SSE Manager Events (from sse_routes.py)
    MESSAGE = "message"
    """Generic message event from SSE manager"""


class SSEEventCategories:
    """
    Categorized groupings of SSE event names for easier frontend handling.
    """
    
    # Terminal events that indicate completion/end of process
    TERMINAL_EVENTS = {
        SSEEventNames.WORKFLOW_COMPLETED,
        SSEEventNames.WORKFLOW_FAILED,
        SSEEventNames.WORKFLOW_CANCELLED,
        SSEEventNames.MCP_SUCCESS,
        SSEEventNames.MCP_ERROR,
        SSEEventNames.ERROR
    }
    
    # Workflow-related events
    WORKFLOW_EVENTS = {
        SSEEventNames.WORKFLOW_COMPLETED,
        SSEEventNames.WORKFLOW_FAILED,
        SSEEventNames.WORKFLOW_CANCELLED,
        SSEEventNames.WORKFLOW_UPDATE
    }
    
    # MCP-related events
    MCP_EVENTS = {
        SSEEventNames.MCP_SUCCESS,
        SSEEventNames.MCP_ERROR,
        SSEEventNames.MCP_UPDATE
    }
    
    # Error events
    ERROR_EVENTS = {
        SSEEventNames.WORKFLOW_FAILED,
        SSEEventNames.MCP_ERROR,
        SSEEventNames.ERROR
    }
    
    # Success events
    SUCCESS_EVENTS = {
        SSEEventNames.WORKFLOW_COMPLETED,
        SSEEventNames.MCP_SUCCESS
    }
    
    # Connection management events
    CONNECTION_EVENTS = {
        SSEEventNames.CONNECTION,
        SSEEventNames.KEEP_ALIVE
    }


# Example usage for frontend developers:
"""
Frontend JavaScript Example:

const eventSource = new EventSource('/api/v1/workflow-execute/stream/correlation-id-123');

// Handle specific event types
eventSource.addEventListener('workflow-completed', (event) => {
    const data = JSON.parse(event.data);
    console.log('Workflow completed:', data);
    // Handle successful completion
});

eventSource.addEventListener('workflow-failed', (event) => {
    const data = JSON.parse(event.data);
    console.error('Workflow failed:', data);
    // Handle failure
});

eventSource.addEventListener('keep-alive', (event) => {
    console.log('Connection alive');
    // Optional: Update UI to show connection status
});

eventSource.addEventListener('mcp-success', (event) => {
    const data = JSON.parse(event.data);
    console.log('MCP tool executed successfully:', data);
    // Handle MCP success
});

// Handle all terminal events
const terminalEvents = ['workflow-completed', 'workflow-failed', 'workflow-cancelled', 'mcp-success', 'mcp-error', 'error'];
terminalEvents.forEach(eventType => {
    eventSource.addEventListener(eventType, (event) => {
        console.log(`Terminal event received: ${eventType}`);
        eventSource.close(); // Close connection on terminal events
    });
});
"""
