from pydantic import BaseModel, Field, model_validator, validator, field_validator
from pydantic.config import Extra
from typing import Optional, Dict, Any, List, Union


class TransitionSpecificValue(BaseModel):
    """Schema for transition-specific field values."""

    value: Union[str, int, float, bool, List[Any], Dict[str, Any], None]
    transition_id: str


class WorkflowPayloadTemplate(BaseModel):
    """
    Template payload for user input with transition-specific field values.

    This class only supports the transition-specific format:
    - Transition-specific values: {"field_name": {"value": "specific_value", "transition_id": "target-transition"}}
    - Simple string values for non-transition fields: {"field_name": "simple_value"}

    All fields in user_dependent_fields must use the transition-specific format.
    """

    class Config:
        extra = Extra.allow  # Allow dynamic fields

    @model_validator(mode="before")
    @classmethod
    def validate_template_format(cls, values):
        """Validate that all fields follow the required format."""
        if not isinstance(values, dict):
            return values

        # No specific validation needed here as we allow both formats
        # The validation will be done at the WorkflowPayload level
        return values


class WorkflowPayload(BaseModel):
    """
    Payload for triggering the workflow with transition-specific field targeting.

    This payload structure requires:
    - user_dependent_fields: Array of field names that must exist in user_payload_template
    - user_payload_template: Object containing field values, supporting both simple values and transition-specific format
    - Transition-specific format: {"value": "...", "transition_id": "..."}
    - Simple values are allowed for any field
    - No backward compatibility with old schema formats
    """

    user_dependent_fields: List[str] = Field(
        ...,
        description="List of user-dependent field names that require values from the user_payload_template",
    )
    user_payload_template: WorkflowPayloadTemplate = Field(
        ...,
        description="Template containing field values. All fields listed in user_dependent_fields must exist here.",
    )

    @model_validator(mode="after")
    def validate_payload_format(self):
        """Validate that the payload follows the required format."""
        # Get the actual template data
        if hasattr(self.user_payload_template, "model_dump"):
            template_dict = self.user_payload_template.model_dump()
        else:
            # Fallback to accessing attributes directly
            template_dict = self.user_payload_template.__dict__

        # Validate that all fields in user_dependent_fields exist in the template
        for field_name in self.user_dependent_fields:
            if field_name not in template_dict:
                raise ValueError(
                    f"Field '{field_name}' listed in user_dependent_fields is missing from user_payload_template"
                )

            field_value = template_dict[field_name]

            # If it's a dict, validate it has the correct structure for transition-specific format
            if isinstance(field_value, dict):
                if not ("value" in field_value and "transition_id" in field_value):
                    raise ValueError(
                        f"Field '{field_name}' uses object format but is missing required 'value' and 'transition_id' properties. Got: {field_value}"
                    )
            # Simple values are allowed for user_dependent_fields

        return self


class WorkflowRequest(BaseModel):
    """Request schema for starting a new workflow (task-requests topic)."""

    workflow_id: str = Field(..., description="Identifier for the workflow type")
    payload: WorkflowPayload = Field(
        ...,
        description="Payload containing user input template, fields, and global context definitions",
    )
    approval: bool = Field(
        False, description="Flag indicating if approval is required for workflow transitions"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "workflow_id": "test-workflow",
                "user_id": "user-123",
                "approval": True,
                "payload": {
                    "user_dependent_fields": ["topic", "format", "voice_id"],
                    "user_payload_template": {
                        "topic": {
                            "value": "test topic for transition 2",
                            "transition_id": "transition-2",
                        },
                        "format": "mp4",
                        "voice_id": {"value": "voice-123", "transition_id": "transition-2"},
                    },
                },
            }
        }


class ServerWorkflowRequest(BaseModel):
    """Request schema for starting a new workflow (task-requests topic)."""

    user_id: str = Field(..., description="User ID")
    workflow_id: str = Field(..., description="Identifier for the workflow type")
    payload: WorkflowPayload = Field(
        ...,
        description="Payload containing user input template, fields, and global context definitions",
    )
    approval: bool = Field(
        False, description="Flag indicating if approval is required for workflow transitions"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "workflow_id": "test-workflow",
                "user_id": "user-123",
                "approval": True,
                "payload": {
                    "user_dependent_fields": ["topic", "format", "voice_id"],
                    "user_payload_template": {
                        "topic": {
                            "value": "test topic for transition 2",
                            "transition_id": "transition-2",
                        },
                        "format": "mp4",
                        "voice_id": {"value": "voice-123", "transition_id": "transition-2"},
                    },
                },
            }
        }


class ExecutionRequest(BaseModel):
    """Request schema for execution requests (execution-requests topic)."""

    correlationId: str = Field(..., description="Correlation ID of the workflow to execute")
    action: str = Field(..., description="Action type: 'retry', 'regenerate', 're-execute'")
    data: Optional[WorkflowRequest] = Field(
        None, description="Required for 'retry' and 're-execute' actions"
    )
    node_id: str = Field(..., description="Node ID (transition ID) for execution")
    params: Optional[Dict[str, Any]] = Field(
        None, description="Optional: Parameters to override for regenerate action"
    )

    @model_validator(mode="before")
    @classmethod
    def check_data_requirement(cls, values):
        if not isinstance(values, dict):
            return values

        action = values.get("action")
        data = values.get("data")

        if action in {"retry", "re-execute"} and data is None:
            raise ValueError(f"'data' is required when action is '{action}'")

        return values

    class Config:
        json_schema_extra = {
            "example": {
                "correlationId": "workflow-instance-123",
                "action": "re-execute",
                "node_id": "process_data_server",
                "data": {
                    "workflow_id": "test-workflow",
                    "user_id": "user-123",
                    "approval": True,
                    "payload": {
                        "user_dependent_fields": ["topic", "format", "voice_id"],
                        "user_payload_template": {
                            "topic": {
                                "value": "test topic for transition 2",
                                "transition_id": "transition-2",
                            },
                            "format": "mp4",
                            "voice_id": {"value": "voice-123", "transition_id": "transition-2"},
                        },
                    },
                },
                "params": {"prompt": "Additional parameters"},
            }
        }


class ApprovalRequest(BaseModel):
    """Request schema for approval requests (approval-requests topic)."""

    correlationId: str = Field(..., description="Correlation ID of the workflow awaiting approval")
    decision: str = Field(..., description="Approval decision: 'approve' or 'reject'")

    class Config:
        json_schema_extra = {
            "example": {"correlationId": "workflow-instance-xyz", "decision": "approve"}
        }


class MCPToolParameters(BaseModel):
    """Schema for complex nested tool parameters."""

    class Config:
        extra = Extra.allow  # Allows any additional fields
        arbitrary_types_allowed = True  # Allows complex nested types


class MCPExecutionRequest(BaseModel):
    """Request schema for MCP execution requests using smart routing."""

    # Required smart routing parameters
    mcp_id: str = Field(
        ...,
        min_length=1,
        description="MCP server identifier for smart routing (required)",
        example="0d183177-51a1-498a-b190-b10a93e7f668",
    )
    tool_name: str = Field(
        ...,
        min_length=1,
        description="Name of the tool to execute (required)",
        example="script_generate",
    )
    tool_parameters: Dict[str, Any] = Field(
        ..., description="Parameters to be passed to the tool (required)"
    )

    @field_validator("tool_parameters")
    @classmethod
    def validate_tool_parameters_not_empty(cls, v):
        """Ensure tool_parameters is not an empty dictionary"""
        if not v:
            raise ValueError("tool_parameters cannot be empty")
        return v

    # Optional parameters
    retries: Optional[int] = Field(3, description="Number of retries for the MCP execution")
    correlation_id: Optional[str] = Field(
        None,
        description="Optional correlation ID for request tracking",
        example="workflow-123-step-456",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "mcp_id": "0d183177-51a1-498a-b190-b10a93e7f668",
                "tool_name": "script_generate",
                "tool_parameters": {
                    "topic": "Wildlife",
                    "script_type": "TOPIC",
                    "video_type": "SHORT",
                },
                "retries": 3,
                "correlation_id": "workflow-123-step-456",
            }
        }


class ServerMCPExecutionRequest(BaseModel):
    """Request schema for MCP execution requests using smart routing with server authentication."""

    # User identification for server authentication
    user_id: str = Field(..., description="User ID for server-authenticated requests")

    # Required smart routing parameters
    mcp_id: str = Field(
        ...,
        min_length=1,
        description="MCP server identifier for smart routing (required)",
        example="0d183177-51a1-498a-b190-b10a93e7f668",
    )
    tool_name: str = Field(
        ...,
        min_length=1,
        description="Name of the tool to execute (required)",
        example="script_generate",
    )
    tool_parameters: Dict[str, Any] = Field(
        ..., description="Parameters to be passed to the tool (required)"
    )

    @field_validator("tool_parameters")
    @classmethod
    def validate_tool_parameters_not_empty(cls, v):
        """Ensure tool_parameters is not an empty dictionary"""
        if not v:
            raise ValueError("tool_parameters cannot be empty")
        return v

    # Optional parameters
    retries: Optional[int] = Field(3, description="Number of retries for the MCP execution")
    correlation_id: Optional[str] = Field(
        None,
        description="Optional correlation ID for request tracking",
        example="workflow-123-step-456",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
                "mcp_id": "0d183177-51a1-498a-b190-b10a93e7f668",
                "tool_name": "script_generate",
                "tool_parameters": {
                    "topic": "Wildlife",
                    "script_type": "TOPIC",
                    "video_type": "SHORT",
                },
                "retries": 3,
                "correlation_id": "workflow-123-step-456",
            }
        }


class MCPExecutionResponse(BaseModel):
    """Response schema for MCP execution requests."""

    success: bool = Field(..., description="Whether the request was successfully submitted")
    message: str = Field(..., description="Response message")
    request_id: str = Field(..., description="Unique request ID for tracking execution")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "MCP execution request submitted successfully",
                "request_id": "12345678-1234-1234-1234-123456789012",
            }
        }
