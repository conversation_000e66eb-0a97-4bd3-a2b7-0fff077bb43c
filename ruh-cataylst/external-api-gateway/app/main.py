from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.routers import sse_routes, workflow_execution_routes, mcp_execution_routes
from app.core.config import settings

from app.services.kafka_service import kafka_service

app = FastAPI(title=settings.APP_NAME, openapi_url=f"{settings.API_V1_STR}/openapi.json")

# Set up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_CREDENTIALS,
    allow_methods=settings.CORS_METHODS,
    allow_headers=settings.CORS_HEADERS,
)

# Include routers
app.include_router(sse_routes.sse_router, prefix=settings.API_V1_STR)
app.include_router(
    workflow_execution_routes.workflow_orchestration_router, prefix=settings.API_V1_STR
)
app.include_router(mcp_execution_routes.mcp_execution_router, prefix=settings.API_V1_STR)


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


@app.on_event("startup")
async def startup_event():
    await kafka_service.initialize()


@app.on_event("shutdown")
async def shutdown_event():
    await kafka_service.stop_services()


# # Add shutdown event handler
# @app.on_event("shutdown")
# async def on_shutdown():
#     await shutdown_sse_connections()
