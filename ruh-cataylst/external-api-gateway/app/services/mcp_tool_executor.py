"""
MCP Tool Executor for the external-api-gateway.
This module provides functionality to execute MCP tools via Kafka messaging.
"""

import asyncio
import json
import logging
import uuid
from typing import Any, Dict, Optional

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from aiokafka.errors import KafkaError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Kafka topics
MCP_REQUEST_TOPIC = "mcp-execution-request"
MCP_RESULT_TOPIC = "mcp_results"


class MCPToolExecutionError(Exception):
    """Exception raised for errors during MCP tool execution."""

    pass


class MCPToolExecutor:
    """
    Executes MCP tools via Kafka messaging.

    This class is responsible for:
    1. Sending execution requests to MCP servers via Kafka
    2. Receiving and processing execution results
    3. Handling timeouts and errors
    """

    def __init__(self, producer: AIOKafkaProducer, bootstrap_servers: str):
        """
        Initialize the MCP Tool Executor.

        Args:
            producer: A running AIOKafkaProducer instance
            bootstrap_servers: Kafka bootstrap servers
        """
        self.logger = logger

        # Validate producer
        if producer is None:
            raise ValueError("A running AIOKafkaProducer instance must be provided.")
        if not getattr(producer._sender, "_running", True):
            self.logger.warning("The provided Kafka Producer may not be running.")

        self.producer = producer
        self._bootstrap_servers = bootstrap_servers
        self._request_topic = MCP_REQUEST_TOPIC
        self._results_topic = MCP_RESULT_TOPIC

        # Consumer setup
        self._consumer = None
        self._consumer_task = None
        self._consumer_group_id = f"mcp-executor-{uuid.uuid4()}"

        # Track pending requests
        self._pending_requests = {}

    async def start(self):
        """Start the internal consumer for receiving results."""
        await self._start_internal_consumer()

    async def stop(self):
        """Stop the internal consumer."""
        await self._stop_internal_consumer()

    async def __aenter__(self):
        """Context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        await self.stop()

    async def _start_internal_consumer(self):
        """Start the internal consumer for receiving results."""
        if self._consumer is not None:
            self.logger.warning("Internal consumer already started.")
            return

        self.logger.info("Starting MCPToolExecutor internal consumer...")
        try:
            self._consumer = AIOKafkaConsumer(
                self._results_topic,
                bootstrap_servers=self._bootstrap_servers,
                group_id=self._consumer_group_id,
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await self._consumer.start()
            self.logger.info(
                f"Internal consumer started. Listening for results on: '{self._results_topic}', Group: '{self._consumer_group_id}'"
            )

            self._consumer_task = asyncio.create_task(
                self._consume_loop(),
                name=f"MCPExecutorConsumer-{self._consumer_group_id[:8]}",
            )
            self.logger.info("Background result consumer loop started.")

        except KafkaError as e:
            self.logger.error(f"Failed to start internal consumer: {e}", exc_info=True)
            await self._stop_internal_consumer()
            raise

    async def _stop_internal_consumer(self):
        """Stop the internal consumer."""
        self.logger.info("Stopping MCPToolExecutor internal consumer components...")

        if self._consumer_task and not self._consumer_task.done():
            self.logger.debug("Cancelling background consumer task...")
            self._consumer_task.cancel()
            try:
                await self._consumer_task
            except asyncio.CancelledError:
                self.logger.debug("Consumer task successfully cancelled.")
            except Exception as e:
                self.logger.error(f"Error during consumer task cancellation: {e}", exc_info=True)
        self._consumer_task = None

        if self._consumer:
            self.logger.debug("Stopping Kafka consumer...")
            await self._consumer.stop()
            self._consumer = None

    async def _consume_loop(self):
        """Background task for consuming results from Kafka."""
        if not self._consumer:
            self.logger.error("Consumer not initialized in _consume_loop.")
            return

        try:
            while True:
                try:
                    async for msg in self._consumer:
                        self.logger.debug(f"Result consumer received message: Offset={msg.offset}")

                        try:
                            # Parse the message
                            result_json = json.loads(msg.value.decode("utf-8"))
                            request_id = result_json.get("request_id")

                            if not request_id:
                                self.logger.warning("Received result without request_id")
                                continue

                            # Check if we have a pending request for this result
                            future = self._pending_requests.pop(request_id, None)

                            if future and not future.done():
                                # Check for error in the result
                                error_data = result_json.get("error")
                                if error_data:
                                    self.logger.warning(
                                        f"Received error response for request_id {request_id}: {error_data}"
                                    )
                                    future.set_exception(
                                        MCPToolExecutionError(
                                            f"Tool execution failed: {error_data}"
                                        )
                                    )
                                else:
                                    self.logger.debug(
                                        f"Received valid result for request_id {request_id}"
                                    )
                                    future.set_result(result_json)
                            elif future and future.done():
                                self.logger.warning(
                                    f"Received result for already completed/cancelled request_id {request_id}"
                                )
                            else:
                                self.logger.warning(
                                    f"Received result for unknown or timed-out request_id: {request_id}"
                                )

                        except json.JSONDecodeError:
                            self.logger.warning(
                                f"Could not decode JSON from results topic: {msg.value.decode('utf-8', errors='ignore')}"
                            )
                        except Exception as e:
                            self.logger.error(
                                f"Error processing result message: {e}", exc_info=True
                            )

                except asyncio.CancelledError:
                    self.logger.info("Result consumer loop cancelled.")
                    break
        except Exception as e:
            self.logger.error(f"Result consumer loop unexpectedly terminated: {e}", exc_info=True)
            if self._pending_requests:
                err = MCPToolExecutionError(f"Consumer loop failed: {e}")
                for req_id, fut in self._pending_requests.items():
                    if not fut.done():
                        fut.set_exception(err)

    async def execute_tool(
        self, server_script_path: str, tool_name: str, tool_parameters: dict, timeout: int = 300
    ) -> Any:
        """
        Execute an MCP tool via Kafka.

        Args:
            server_script_path: Path to the MCP server script
            tool_name: Name of the tool to execute
            tool_parameters: Parameters to pass to the tool
            timeout: Timeout in seconds (default: 300)

        Returns:
            The result of the tool execution

        Raises:
            RuntimeError: If the consumer or producer is not running
            MCPToolExecutionError: If the tool execution fails
            asyncio.TimeoutError: If the execution times out
        """
        # Check if consumer is running
        if not self._consumer or not self._consumer_task or self._consumer_task.done():
            raise RuntimeError(
                "MCPToolExecutor's internal consumer is not running. Call start() or use 'async with'."
            )

        # Check if producer is running
        if not self.producer or not getattr(self.producer._sender, "_running", True):
            raise RuntimeError("The provided Kafka Producer is not running or not available.")

        # Generate a unique request ID
        request_id = str(uuid.uuid4())
        self.logger.info(f"Executing tool '{tool_name}' via Kafka (request_id: {request_id})")

        # Prepare the payload
        payload = {
            "server_script_path": server_script_path,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "request_id": request_id,
        }

        # Create a future for the result
        future = asyncio.Future()
        self._pending_requests[request_id] = future

        try:
            # Send the request
            self.logger.debug(f"Sending request to topic '{self._request_topic}': {payload}")
            # Use the payload directly - the producer's value_serializer will handle serialization
            await self.producer.send(self._request_topic, value=payload)
            self.logger.debug(f"Request {request_id} sent successfully")

            # Wait for the result with timeout
            self.logger.debug(
                f"Waiting for result for request {request_id} (timeout: {timeout}s)..."
            )
            result = await asyncio.wait_for(future, timeout=timeout)
            self.logger.info(f"Result received for request {request_id}")
            return result

        except asyncio.TimeoutError:
            self.logger.error(f"Timeout waiting for result for request {request_id}")
            self._pending_requests.pop(request_id, None)
            raise asyncio.TimeoutError(f"Timeout waiting for result for request {request_id}")
        except KafkaError as e:
            self.logger.error(f"Kafka error during tool execution {request_id}: {e}", exc_info=True)
            self._pending_requests.pop(request_id, None)
            raise MCPToolExecutionError(f"Kafka error executing request {request_id}: {e}") from e
        except Exception as e:
            self.logger.error(f"Error during tool execution {request_id}: {e}", exc_info=True)
            self._pending_requests.pop(request_id, None)
            raise e
