from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from aiokafka import <PERSON><PERSON>afkaConsumer, AIOKafkaProducer
from aiokafka.errors import KafkaError
from typing import AsyncGenerator, Dict, Any, Optional
import json
import os
import logging
import time
import uuid
from dotenv import load_dotenv
import asyncio
from app.helper.sse_manager import S<PERSON><PERSON><PERSON><PERSON>

# We'll use the Kafka producer directly instead of MCPToolExecutor

load_dotenv()

# Kafka broker configuration
KAFKA_BROKER = f'{os.getenv("KAFKA_BROKER_HOST")}:{os.getenv("KAFKA_BROKER_PORT")}'
WORKFLOW_REQUEST_TOPIC = "workflow-requests"
EXECUTION_REQUEST_TOPIC = "execution-requests"
APPROVAL_REQUEST_TOPIC = "approval-requests"
REPLY_TOPIC = "workflow-responses"
MCP_REQUEST_TOPIC = "mcp-execution-request"
MCP_REPLY_TOPIC = "mcp_results"

# Dictionary to store response queues for SSE streams, keyed by correlation_id
response_queues: Dict[str, asyncio.Queue] = {}

logging.getLogger("kafka").setLevel(logging.DEBUG)


class KafkaService:
    _instance = None
    _initialized = False
    _consumer_task = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def initialize(self):
        if self._initialized:
            return

        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BROKER,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            max_request_size=524288000,
        )

        self.consumer = AIOKafkaConsumer(
            REPLY_TOPIC,
            MCP_REPLY_TOPIC,  # Add MCP results topic
            bootstrap_servers=KAFKA_BROKER,
            group_id=f"workflow-consumer-group",
            auto_offset_reset="latest",
            session_timeout_ms=30000,
            max_poll_interval_ms=60000,
            enable_auto_commit=True,
            value_deserializer=lambda m: json.loads(m.decode("utf-8")),
            auto_commit_interval_ms=10000,
        )

        self.used_ids = set()
        self.initialized = True
        self.init_lock = asyncio.Lock()
        self.sse_manager = SseManager()

        # Start the services
        await self._start_services()
        self._initialized = True

    async def _start_services(self):
        """Start the Kafka producer and consumer services."""
        try:
            await self.producer.start()
            await self.consumer.start()

            # Start the background consumer task
            if not self._consumer_task or self._consumer_task.done():
                self._consumer_task = asyncio.create_task(self._background_consumer_task())
                print("Background consumer task started")

            print("All Kafka services started successfully")
        except Exception as e:
            print(f"Error starting Kafka services: {e}")
            # Cleanup in case of startup failure
            await self._cleanup_services()
            raise

    async def _cleanup_services(self):
        """Cleanup helper method for error cases."""
        if hasattr(self, "producer") and self.producer:
            try:
                await self.producer.stop()
            except Exception as e:
                print(f"Error stopping producer: {e}")

        if hasattr(self, "consumer") and self.consumer:
            try:
                await self.consumer.stop()
            except Exception as e:
                print(f"Error stopping consumer: {e}")

        if self._consumer_task and not self._consumer_task.done():
            self._consumer_task.cancel()
            try:
                await self._consumer_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                print(f"Error cancelling consumer task: {e}")

    async def stop_services(self):
        """Stop all Kafka services properly."""
        if not self._initialized:
            return

        print("Stopping Kafka services...")

        # Cancel the consumer task first
        if self._consumer_task and not self._consumer_task.done():
            print("Cancelling background consumer task...")
            self._consumer_task.cancel()
            try:
                await self._consumer_task
            except asyncio.CancelledError:
                print("Background consumer task cancelled successfully")

        # Then stop the consumer and producer
        if hasattr(self, "consumer"):
            print("Stopping Kafka consumer...")
            await self.consumer.stop()

        if hasattr(self, "producer"):
            print("Stopping Kafka producer...")
            await self.producer.stop()

        self._initialized = False
        print("All Kafka services stopped successfully")

    async def _background_consumer_task(self):
        """The main consumer loop task for workflow responses and MCP results."""
        print("Starting background consumer task for workflow responses and MCP results...")
        try:
            async for msg in self.consumer:
                try:
                    # Get correlation ID or request ID from headers
                    correlation_id = None
                    request_id = None

                    for key, value in msg.headers:
                        if key == "correlationId":
                            correlation_id = value.decode("utf-8")
                            break
                        elif key == "request_id":
                            request_id = value.decode("utf-8")
                            break

                    # If we didn't find an ID in the headers, check the message value
                    if not correlation_id and not request_id and isinstance(msg.value, dict):
                        correlation_id = msg.value.get("correlationId")
                        request_id = msg.value.get("request_id")

                    # Handle workflow responses
                    if correlation_id:
                        response = msg.value
                        print(
                            f"Background Consumer - Received response for correlationId {correlation_id}: {response}"
                        )
                        queue = response_queues.get(correlation_id)

                        if not queue:
                            print(
                                f"No queue found for correlationId: {correlation_id}, creating a new queue."
                            )
                            queue = asyncio.Queue()
                            response_queues[correlation_id] = queue

                        self.sse_manager.send_update(
                            "workflow_response", response, str(correlation_id)
                        )
                        await queue.put(response)

                    # Handle MCP responses
                    elif request_id:
                        response = msg.value
                        print(
                            f"Background Consumer - Received MCP response for request_id {request_id}: {response}"
                        )
                        queue = response_queues.get(request_id)

                        if not queue:
                            print(
                                f"No queue found for request_id: {request_id}, creating a new queue."
                            )
                            queue = asyncio.Queue()
                            response_queues[request_id] = queue

                        self.sse_manager.send_update("mcp_response", response, None)
                        await queue.put(response)
                    else:
                        print(
                            "Background Consumer - Received message without correlationId or request_id:",
                            msg.value,
                        )

                except json.JSONDecodeError as e:
                    print(f"Error decoding message: {e}")
                except Exception as e:
                    print(f"Error processing message: {e}")

        except Exception as e:
            print(f"Background consumer error: {e}")
            # Only attempt to restart if not shutting down
            if self._initialized:
                print("Attempting to restart consumer task in 5 seconds...")
                await asyncio.sleep(5)
                if self._initialized:  # Check again after sleep
                    self._consumer_task = asyncio.create_task(self._background_consumer_task())

    async def _ensure_initialized(self):
        """Make sure services are started before using them."""
        if not self._initialized:
            await self.initialize()

    async def send_kafka_workflow_request(
        self, workflow_json_content: Dict[str, Any], approval: bool = False
    ) -> str:
        """
        Sends an initial workflow task to the task-requests Kafka topic.
        Generates and returns a new correlation ID.
        """
        try:
            # Ensure services are initialized
            await self._ensure_initialized()

            message = {
                "task_id": int(time.time()),
                "task_type": "workflow",
                "data": workflow_json_content,
                "approval": approval,
            }

            correlation_id = str(uuid.uuid4())
            response_queues[correlation_id] = asyncio.Queue()

            headers = [
                ("correlationId", correlation_id.encode("utf-8")),
                ("reply-topic", REPLY_TOPIC.encode("utf-8")),
            ]

            print("[INFO] Sending Task Request to Kafka")
            await self.producer.send(WORKFLOW_REQUEST_TOPIC, message, headers=headers)
            # Make sure to flush to ensure messages are actually sent
            await self.producer.flush()
            print(f"Task Request Sent (Request-Response & SSE): {json.dumps(message, indent=2)}")
            print(f"Correlation ID (Request-Response & SSE): {correlation_id}")

            return correlation_id

        except Exception as e:
            print(f"Error sending task request to Kafka: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send task request to Kafka: {str(e)}",
            )

    async def send_kafka_execution_request(
        self,
        correlation_id: str,
        action: str,
        data: Optional[Dict[str, Any]] = {},
        node_id: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Sends an execution request to the execution-requests Kafka topic.
        Now expects correlation_id as a parameter (no new ID generation).
        """
        try:
            await self._ensure_initialized()

            message = {"action": action}
            if data:
                message["data"] = data
            if node_id:
                message["node_id"] = node_id
            if params:
                message["params"] = params

            headers = [
                ("correlationId", correlation_id.encode("utf-8")),
                ("reply-topic", REPLY_TOPIC.encode("utf-8")),
            ]

            await self.producer.send(EXECUTION_REQUEST_TOPIC, message, headers=headers)

            await self.producer.flush()
            print(
                f"Execution Request Sent (Request-Response & SSE): {json.dumps(message, indent=2)}"
            )
            print(f"Correlation ID (Request-Response & SSE): {correlation_id}")

            return correlation_id

        except Exception as e:
            print(f"Error sending execution request to Kafka: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send execution request to Kafka: {str(e)}",
            )

    async def send_kafka_approval_request(self, correlation_id: str, decision: str) -> str:
        """
        Sends an approval request to the approval-requests topic.
        Now expects correlation_id as a parameter (no new ID generation).
        """
        if decision.lower() not in ["approve", "reject"]:
            raise ValueError("Decision must be 'approve' or 'reject'")

        try:
            # Ensure services are initialized
            await self._ensure_initialized()

            message = {"decision": decision.lower()}

            headers = [
                ("correlationId", correlation_id.encode("utf-8")),
                ("reply-topic", REPLY_TOPIC.encode("utf-8")),
            ]

            await self.producer.send(APPROVAL_REQUEST_TOPIC, message, headers=headers)
            # Make sure to flush to ensure messages are actually sent
            await self.producer.flush()
            print(
                f"Approval Request Sent (Request-Response & SSE): {json.dumps(message, indent=2)}"
            )
            print(f"Correlation ID (Request-Response & SSE): {correlation_id}")

            return correlation_id

        except Exception as e:
            print(f"Error sending approval request to Kafka (Request-Response & SSE): {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send approval request to Kafka (Request-Response & SSE): {str(e)}",
            )

    async def send_mcp_execution_request(self, request_data: Dict[str, Any]) -> str:
        """
        Sends an MCP execution request directly to Kafka using smart routing.

        Args:
            request_data: Dictionary containing:
                Required:
                - mcp_id: MCP server identifier for smart routing
                - user_id: User identifier for credential retrieval (added by route from authentication)
                - tool_name: Name of the tool to execute
                - tool_parameters: Parameters to pass to the tool

                Optional:
                - retries: Number of retries (default: 3)
                - correlation_id: Optional correlation ID for request tracking

        Returns:
            Returns the request_id for tracking the asynchronous execution

        Raises:
            HTTPException: If the execution fails or validation errors occur
        """
        try:
            # Ensure services are initialized
            await self._ensure_initialized()

            # Extract required fields (validation already done by Pydantic schema)
            mcp_id = request_data["mcp_id"]
            user_id = request_data["user_id"]
            tool_name = request_data["tool_name"]
            tool_parameters = request_data["tool_parameters"]

            # Extract optional fields with defaults
            retries = request_data.get("retries", 3)
            correlation_id = request_data.get("correlation_id")

            # Generate a unique request ID
            request_id = str(uuid.uuid4())

            # Create a queue for this request_id if it doesn't exist
            if request_id not in response_queues:
                response_queues[request_id] = asyncio.Queue()

            # Prepare the payload matching MCP executor service expectations exactly
            payload = {
                "mcp_id": mcp_id,
                "user_id": user_id,
                "tool_name": tool_name,
                "tool_parameters": tool_parameters,
                "retries": retries,
                "request_id": request_id,
            }

            # Add correlation_id if provided
            if correlation_id:
                payload["correlation_id"] = correlation_id

            # Log payload structure for verification
            print(f"📤 Sending MCP execution request to Kafka:")
            print(f"   mcp_id: {mcp_id}")
            print(f"   user_id: {user_id}")
            print(f"   tool_name: {tool_name}")
            print(f"   tool_parameters: {tool_parameters}")
            print(f"   retries: {retries}")
            print(f"   request_id: {request_id}")
            if correlation_id:
                print(f"   correlation_id: {correlation_id}")

            # Send the request directly to Kafka
            headers = [
                ("request_id", request_id.encode("utf-8")),
                ("reply-topic", MCP_REPLY_TOPIC.encode("utf-8")),
            ]

            # Send the message to Kafka
            await self.producer.send(MCP_REQUEST_TOPIC, payload, headers=headers)
            await self.producer.flush()

            print(f"✅ MCP execution request {request_id} sent to Kafka successfully")
            print(f"   Topic: {MCP_REQUEST_TOPIC}")
            print(f"   Payload structure verified for MCP executor service compatibility")

            return request_id

        except KeyError as ke:
            # Handle missing required fields (shouldn't happen with Pydantic validation)
            print(f"❌ Missing required field in MCP execution request: {ke}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing required field: {str(ke)}",
            )
        except HTTPException as http_exc:
            # Re-raise HTTP exceptions
            raise http_exc
        except Exception as e:
            # Handle other exceptions
            print(f"❌ Error sending MCP execution request to Kafka: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send MCP execution request: {str(e)}",
            )

    # We no longer need the _execute_mcp_tool_async method as we're sending directly to Kafka

    def _determine_event_name(self, response_data: Dict[str, Any]) -> str:
        """
        Determine the appropriate event name based on response data content.

        Args:
            response_data: The response data dictionary

        Returns:
            str: The event name to use for this response
        """
        # Check for workflow status
        workflow_status = response_data.get("workflow_status")
        if workflow_status:
            if workflow_status == "completed":
                return "workflow-completed"
            elif workflow_status == "failed":
                return "workflow-failed"
            elif workflow_status == "cancelled":
                return "workflow-cancelled"
            else:
                return "workflow-update"

        # Check for MCP status
        mcp_status = response_data.get("mcp_status")
        if mcp_status:
            if mcp_status == "success":
                return "mcp-success"
            elif mcp_status == "error":
                return "mcp-error"
            else:
                return "mcp-update"

        # Check for error conditions
        if response_data.get("error"):
            return "error"

        # Check for specific message types
        message = response_data.get("message", "")
        if "Stream connected" in message:
            return "connection"

        # Default to generic update event
        return "update"

    async def kafka_response_generator(self, id_value: str) -> AsyncGenerator[str, None]:
        """
        Asynchronous generator to yield SSE formatted responses from the response queue for a given ID.
        This ID can be either a correlation_id for workflow responses or a request_id for MCP responses.
        Keeps the stream alive if any connection is established at any time.
        """
        await self._ensure_initialized()

        if id_value in self.used_ids:
            error_message = {
                "error": "This ID has already been used and cannot be reused.",
                "id": id_value,
            }
            yield f"event: error\ndata: {json.dumps(error_message)}\n\n"
            return

        # Mark ID as used
        self.used_ids.add(id_value)

        queue = response_queues.get(id_value)
        if not queue:
            queue = asyncio.Queue()
            response_queues[id_value] = queue
            print(f"Created new queue for ID: {id_value}")

        try:
            # Send an initial message to confirm the connection
            initial_message = {"message": "Stream connected", "id": id_value}
            yield f"event: connection\ndata: {json.dumps(initial_message)}\n\n"

            while True:
                try:
                    response_data = await asyncio.wait_for(queue.get(), timeout=30.0)
                    if response_data:
                        print(f"Sending SSE data for ID {id_value}: {response_data}")

                        # Determine event name based on response data
                        event_name = self._determine_event_name(response_data)
                        yield f"event: {event_name}\ndata: {json.dumps(response_data)}\n\n"

                        workflow_terminal_states = {"completed", "failed", "cancelled"}
                        mcp_terminal_states = {"success", "error"}

                        workflow_status = response_data.get("workflow_status")
                        mcp_status = response_data.get("mcp_status")
                        error = response_data.get("error")
                        # Check if we should end the stream
                        # Check for terminal states
                        if workflow_status in workflow_terminal_states:
                            if workflow_status == "failed" and error:
                                print(f"[Workflow Error] ID {id_value}: {error}")
                            else:
                                print(
                                    f"Ending workflow stream for ID {id_value} with status: {workflow_status}"
                                )
                            break

                        elif mcp_status in mcp_terminal_states:
                            if mcp_status == "error" and error:
                                print(f"[MCP Error] ID {id_value}: {error}")
                            else:
                                print(
                                    f"Ending MCP stream for ID {id_value} with status: {mcp_status}"
                                )
                            break
                        elif error:
                            print(f"Ending stream for ID {id_value} due to unknown error: {error}")
                            break
                except asyncio.TimeoutError:
                    # Send a keep-alive message if no data is received within the timeout
                    print(f"Sending keep-alive for ID {id_value}")
                    yield "event: keep-alive\ndata: keep-alive\n\n"
        except Exception as e:
            print(f"Error in SSE stream for ID {id_value}: {e}")
            error_message = {
                "error": "Error streaming responses",
                "details": str(e),
                "id": id_value,
            }
            yield f"event: error\ndata: {json.dumps(error_message)}\n\n"
        finally:
            # Clean up the queue after streaming ends
            if id_value in response_queues:
                print(f"Deleting queue for ID {id_value}")
                del response_queues[id_value]

    async def cleanup_resources(self):
        """Clean up resources like response queues."""
        global response_queues
        response_queues.clear()


# Create a global instance but don't initialize it
kafka_service = KafkaService()
