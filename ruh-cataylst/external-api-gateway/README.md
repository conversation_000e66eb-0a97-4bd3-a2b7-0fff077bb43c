# Workflow Execution Engine

FastAPI-based API Gateway focused on workflow execution and Kafka integration. It provides a simple way for external developers to interact with the workflow execution engine.

## Features

- Start workflow executions
- Monitor workflow progress in real-time
- Send execution requests for specific workflow nodes
- Handle approval steps in workflows
- API key authentication
- Direct Kafka integration

## Architecture

The Workflow Execution Engine follows this architecture:

```
External Developer Applications
       |
       v
+----------------+
|  Simplified    |
|  Workflow API  |  <---- API Key Authentication
+----------------+
       |
       v
+----------------------------------+
|                                  |
|  +------------+  +------------+  |
|  | Workflow   |  | Kafka      |  |
|  | Service    |  | Event Bus  |  |
|  +------------+  +------------+  |
|                                  |
+----------------------------------+
       |
       v
+----------------+
| Orchestration  |
| Server         |  <---- ORCHESTRATION_SERVER_AUTH_KEY
+----------------+
```

## Prerequisites

- Python 3.11 or higher
- Poetry (Python package manager)
- Kafka broker
- Workflow Orchestration Server

## Setup

1. Install dependencies:

```bash
poetry install
```

2. Set up environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Application Settings
APP_NAME=workflow-engine
DEBUG=false
API_V1_STR=/api/v1

# Kafka Settings
KAFKA_BROKER_PORT=9092
KAFKA_BROKER_HOST=localhost

# API Keys
ORCHESTRATION_SERVER_AUTH_KEY=your-orchestration-key
WORKFLOW_CLIENT_API_KEYS=dev-workflow-key,test-workflow-key

# CORS Settings
CORS_ORIGINS=["http://localhost", "http://localhost:3000"]
CORS_CREDENTIALS=true
CORS_METHODS=["*"]
CORS_HEADERS=["*"]
```

## Running the Service

Use the provided script:

```bash
chmod +x run_local.sh
./run_local.sh
```

Or run manually:

```bash
# Install dependencies
poetry install

# Generate gRPC code
poetry run python -m app.scripts.generate_grpc

# Start the service
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

The API Gateway will start on port 8000.

## API Documentation

Once running, access the API documentation at:

- Swagger UI: <http://localhost:8000/docs>
- ReDoc: <http://localhost:8000/redoc>

For detailed documentation, see:

- [Workflow Engine Documentation](WORKFLOW_ENGINE_DOCUMENTATION.md)
- [Example Code](examples/workflow_client_example.py)

### Available Endpoints

#### Workflow Client API

- POST `/api/v1/workflow-client/start`: Start a workflow
- POST `/api/v1/workflow-client/execute`: Execute a workflow node
- POST `/api/v1/workflow-client/approve`: Approve or reject a workflow step
- GET `/api/v1/workflow-client/stream/{correlation_id}`: Stream workflow updates

#### Workflow API

- POST `/api/v1/workflows`: Create a workflow
- GET `/api/v1/workflows/{workflow_id}`: Get workflow details
- GET `/api/v1/workflows/orchestration/{workflow_id}`: Get workflow for orchestration
- DELETE `/api/v1/workflows/{workflow_id}`: Delete workflow
- GET `/api/v1/workflows`: List all workflows
- PUT `/api/v1/workflows/{workflow_id}`: Update workflow
- GET `/api/v1/workflows/user/{user_id}`: Get workflows by user ID

#### Kafka API

- POST `/api/v1/kafka/workflow-requests`: Start a workflow
- POST `/api/v1/kafka/execution-requests`: Send execution request
- POST `/api/v1/kafka/approval-requests`: Send approval request
- GET `/api/v1/kafka/stream/{correlation_id}`: Stream workflow updates

#### MCP Execution API

- POST `/api/v1/mcp-execute/server`: Execute MCP server tool using smart routing
- GET `/api/v1/mcp-execute/stream/{request_id}`: Stream MCP execution updates

## Integration Options

You can interact with the engine in two ways:

1. **REST API**: Simple HTTP endpoints with API key authentication
2. **Direct Kafka Integration**: Using the provided client library for direct Kafka messaging

### API Key Authentication

All workflow client endpoints use API key authentication. You need to include your API key in the `X-API-Key` header:

```
X-API-Key: your-api-key
```

### Orchestration Server Authentication

For direct Kafka integration, you need to provide the `ORCHESTRATION_SERVER_AUTH_KEY` when initializing the client. This key is used to authenticate with the orchestration server.

### MCP Execution

The API Gateway supports executing MCP (Model Context Protocol) server tools using smart routing. This allows you to execute tools on remote MCP servers without needing to know the server URLs or manage authentication manually.

#### Smart Routing

Smart routing automatically:

- Extracts `user_id` from the authenticated user context
- Looks up MCP server configuration using `mcp_id`
- Retrieves user-specific OAuth credentials using the authenticated user's ID
- Determines the appropriate connection type (SSE, HTTP, or STDIO)
- Handles authentication and tool execution

#### Example MCP Execution Request

```bash
curl -X 'POST' \
  'http://localhost:8000/api/v1/mcp-execute/server' \
  -H 'accept: application/json' \
  -H 'X-API-Key: dev-workflow-key' \
  -H 'Content-Type: application/json' \
  -d '{
  "mcp_id": "0d183177-51a1-498a-b190-b10a93e7f668",
  "tool_name": "script_generate",
  "tool_parameters": {
    "topic": "Wildlife Adventure",
    "script_type": "TOPIC",
    "video_type": "SHORT"
  },
  "retries": 3,
  "correlation_id": "optional-tracking-id"
}'
```

**Note:** The `user_id` is automatically extracted from the authenticated user context and does not need to be included in the request body.

The response includes a `request_id` that can be used to stream execution updates:

```bash
curl -X 'GET' \
  'http://localhost:8000/api/v1/mcp-execute/stream/{request_id}' \
  -H 'accept: text/event-stream'
```

## Project Structure

```
workflow-engine/
├── app/
│   ├── api/           # API routes and endpoints
│   │   └── routers/   # Router modules
│   ├── client/        # Client libraries
│   ├── core/          # Core functionality
│   ├── grpc/          # Generated gRPC code
│   ├── schemas/       # Pydantic models
│   └── services/      # Service clients
├── examples/          # Example code
├── proto-definitions/ # Proto files
├── .env.example       # Example environment variables
├── poetry.lock        # Lock file for dependencies
├── pyproject.toml     # Project configuration
├── WORKFLOW_ENGINE_DOCUMENTATION.md # Detailed documentation
└── README.md          # This file
```

## Kafka Resilience

The Workflow Execution Engine is designed to be resilient to Kafka unavailability:

- The application will start and run even if Kafka is not available
- Clear error messages are provided when Kafka is unavailable
- API endpoints will still respond with appropriate error messages
- The application will automatically use Kafka when it becomes available

This makes the engine suitable for development and testing environments where Kafka might not always be running.

## Testing

### Testing with Swagger UI

The easiest way to test the API is using the Swagger UI:

1. Open <http://localhost:8000/docs> in your browser
2. Click the "Authorize" button and enter your API key
3. Try out the different endpoints

### Testing with curl

You can also test the API using curl:

```bash
# Test creating a workflow
curl -X 'POST' \
  'http://localhost:8000/api/v1/workflows' \
  -H 'accept: application/json' \
  -H 'X-API-Key: dev-workflow-key' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Test Workflow",
  "description": "A test workflow",
  "workflow": {
    "nodes": [
      {
        "id": "start_node",
        "type": "start",
        "next": "process_node"
      },
      {
        "id": "process_node",
        "type": "process",
        "next": "end_node"
      },
      {
        "id": "end_node",
        "type": "end"
      }
    ]
  },
  "visibility": "public",
  "owner_type": "user",
  "status": "active",
  "category": "test",
  "tags": ["test", "example"],
  "permissions": {
    "read": ["all"],
    "write": ["owner"]
  }
}'

# Test starting a workflow
curl -X 'POST' \
  'http://localhost:8000/api/v1/workflow-client/start' \
  -H 'accept: application/json' \
  -H 'X-API-Key: dev-workflow-key' \
  -H 'Content-Type: application/json' \
  -d '{
  "workflow_id": "1",
  "payload": {
    "input_text": "Hello, world!",
    "process_type": "uppercase"
  },
  "approval": false
}'
```

### Running Automated Tests

Run automated tests with:

```bash
poetry run pytest
```

## Generating gRPC Code

After modifying proto files:

```bash
poetry run python -m app.scripts.generate_grpc
```
