# Application settings
APP_NAME="MCP-executor"
APP_ENV="Production"
APP_DEBUG="False"

# Communication ports
KAFKA_BOOTSTRAP_SERVERS="kafka.ruh.ai:9094"
KAFKA_CONSUMER_TOPIC="mcp-execution-request"
KAFKA_CONSUMER_GROUP_ID="mcp_executor_service"
KAFKA_RESULTS_TOPIC="mcp_results"

# MCP Execution Settings
DEFAULT_MCP_RETRIES=3
LOG_LEVEL="INFO"
MAX_CONCURRENT_TASKS=10

# API Gateway Configuration
# For local development
API_BASE_URL="http://localhost:8000"
# For Kubernetes deployment (uncomment and use this instead)
# API_BASE_URL="http://gateway-service-ai-svc.ruh-dev.svc.cluster.local"

# Authentication and Security
SERVER_AUTH_KEY="your-server-auth-key-here"
CREDENTIAL_CACHE_TTL=300

# SSH Configuration for MCP Client
DEFAULT_SSH_HOST=""
DEFAULT_SSH_USER=""
SSH_PORT=22
SSH_KEY_PATH=""
DEFAULT_SSH_KEY_CONTENT=""

# Container API Settings
CONTAINER_API_TIMEOUT=30

# MCP Configuration API Settings
MCP_CONFIG_CACHE_TTL=300
MCP_CONFIG_TIMEOUT=10

