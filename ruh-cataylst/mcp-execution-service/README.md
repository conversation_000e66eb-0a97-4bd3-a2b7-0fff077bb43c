# MCP Execution Service - Dual Connection Support

A microservice designed to execute Multi-modal Conversational Processor (MCP) tools via Kafka messaging with support for both SSE (Server-Sent Events) and SSH Docker connections. This service consumes messages from a Kafka topic, executes the requested MCP tools using the appropriate connection type, and sends the results back to a results topic.

## 🚀 Key Features

- **Smart Routing**: Intelligent execution method selection based on MCP configuration
- **Container Lifecycle Management**: Automated container creation, execution, and cleanup
- **Dual Connection Support**: Both SSE and SSH Docker connections
- **Automatic Connection Selection**: Based on request parameters or MCP configuration
- **Fallback Mechanisms**: Graceful degradation when primary methods fail
- **Asynchronous Processing**: Via Kafka messaging
- **Production Ready**: Comprehensive error handling and monitoring
- **Easy Testing**: Multiple test scripts and demo tools

## Architecture Overview

```mermaid
flowchart TB
    subgraph Client
        A[Client Application]
    end

    subgraph Messaging
        B[Kafka Broker]
    end

    subgraph "MCP Execution Service"
        C[Kafka Consumer]
        D[MCP Executor]
        E[Dual Connection Client]
        F[Kafka Producer]
    end

    subgraph "SSE Servers"
        G[SSE MCP Server 1]
        H[SSE MCP Server 2]
    end

    subgraph "SSH Docker Servers"
        I[SSH Server + Docker]
        J[Remote Docker Container]
        K[MCP Server in Container]
    end

    A -->|Send Request| B
    B -->|Consume Request| C
    C -->|Process Request| D
    D -->|Select Connection Type| E
    E -->|SSE Connection| G & H
    E -->|SSH Docker Connection| I
    I -->|Run Container| J
    J -->|Execute MCP| K
    K -->|Return Results| J
    J -->|Results| I
    I -->|SSH Response| E
    G & H -->|SSE Response| E
    E -->|Results| D
    D -->|Format Results| F
    F -->|Send Results| B
    B -->|Deliver Results| A

    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef sse fill:#9f9,stroke:#333,stroke-width:1px;
    classDef ssh fill:#ff9,stroke:#333,stroke-width:1px;
    class D,E primary;
    class A,B,C,F secondary;
    class G,H sse;
    class I,J,K ssh;
```

## 🧠 Smart Routing

The service now includes intelligent routing that automatically determines the execution method based on MCP configuration:

### How It Works

1. **MCP Configuration Lookup**: Fetches configuration from `/api/v1/mcps/{mcp_id}`
2. **URL Prioritization**: Applies priority logic to determine execution method
3. **Automatic Execution**: Routes to container or URL execution based on configuration
4. **Fallback Support**: Falls back to alternative methods if primary fails

### Priority Logic

When multiple URLs are present in MCP configuration:

1. `image_name` + `type: "stdio"` → **Container execution** (highest priority)
2. `image_name` + any type → **Container execution**
3. `url` + `type: "sse"` → **URL execution**
4. `url` + any type → **URL execution**

### Configuration Example

```json
{
  "id": "my-mcp-server",
  "urls": [
    {
      "image_name": "my-mcp:latest",
      "type": "stdio"
    },
    {
      "url": "http://example.com/sse",
      "type": "sse"
    }
  ]
}
```

**Result**: Uses container execution with fallback to URL if container fails.

For detailed information, see [Smart Routing Documentation](README_SMART_ROUTING.md).

## Connection Types

### 📡 SSE (Server-Sent Events) Connections

- **HTTP/HTTPS** connections to MCP servers
- **Persistent connections** for efficient communication
- **Standard web protocols** for easy deployment
- **Compatible** with existing MCP server implementations

### 🌐 Streamable HTTP Connections

- **HTTP/HTTPS** connections with streamable protocol
- **Bidirectional communication** for enhanced interaction
- **Modern HTTP streaming** for real-time data exchange
- **Authentication support** via headers

### 🐳 SSH Docker Connections

- **SSH connections** to remote servers
- **Docker containers** for isolated MCP execution
- **Flexible deployment** on any SSH-accessible server
- **Secure authentication** via SSH keys

## Features

- **Triple Connection Support**: SSE, Streamable HTTP, and SSH Docker connections
- **Automatic Connection Selection**: Based on request parameters
- **Asynchronous Processing**: MCP tool execution requests via Kafka
- **Automatic Retry Mechanism**: For failed executions with exponential backoff
- **Concurrency Control**: With configurable semaphores
- **Dead Letter Queue**: For permanently failed executions
- **JSON Parsing**: Of MCP tool results
- **Comprehensive Logging**: With configurable levels
- **Production Ready**: Error handling and monitoring
- **Easy Testing**: Multiple test scripts and validation tools

## Prerequisites

- **Python 3.11 or higher**
- **Poetry** (Python package manager)
- **Kafka broker** (for message processing)

### For SSE Connections

- **MCP servers** accessible via HTTP/HTTPS

### For SSH Docker Connections

- **SSH access** to remote servers
- **Docker** installed on SSH servers
- **MCP Docker images** available on SSH servers

### SSH Environment Variables (Optional)

SSH connection parameters can be configured via environment variables:

- **`SSH_HOST`** - Remote server hostname or IP address
- **`SSH_USER`** - SSH username for authentication
- **`SSH_PORT`** - SSH port (default: 22)
- **`SSH_KEY_PATH`** - Path to SSH private key file (optional)

## Installation

### Using Poetry (Recommended)

1. Clone the repository:

   ```bash
   git clone https://gitlab.rapidinnovation.tech/ruh-catalyst/mcp-execution-service.git
   cd mcp-execution-service
   ```

2. Install dependencies:

   ```bash
   poetry install
   ```

### Using Docker

1. Build the Docker image:

   ```bash
   docker build -t mcp-execution-service .
   ```

2. Run the container:

   ```bash
   docker run -d --name mcp-execution-service \
     -e KAFKA_BOOTSTRAP_SERVERS=kafka:9092 \
     -e KAFKA_CONSUMER_TOPIC=mcp-execution-request \
     -e KAFKA_CONSUMER_GROUP_ID=mcp_executor_service \
     -e KAFKA_RESULTS_TOPIC=mcp_results \
     -e LOG_LEVEL=INFO \
     -e MAX_CONCURRENT_TASKS=10 \
     mcp-execution-service \
     python -m app.main
   ```

## Configuration

Create a `.env` file based on the provided `.env.example`:

```bash
# Application settings
APP_NAME="MCP-executor"
APP_ENV="Production"
APP_DEBUG="False"

# Kafka settings
KAFKA_BOOTSTRAP_SERVERS="kafka:9092"
KAFKA_CONSUMER_TOPIC="mcp-execution-request"
KAFKA_CONSUMER_GROUP_ID="mcp_executor_service"
KAFKA_RESULTS_TOPIC="mcp_results"
DEFAULT_MCP_RETRIES=3
LOG_LEVEL="INFO"
MAX_CONCURRENT_TASKS=10
```

### Configuration Parameters

| Parameter               | Description                                      | Default               |
| ----------------------- | ------------------------------------------------ | --------------------- |
| KAFKA_BOOTSTRAP_SERVERS | Kafka broker addresses                           | localhost:9092        |
| KAFKA_CONSUMER_TOPIC    | Topic to consume execution requests from         | mcp-execution-request |
| KAFKA_CONSUMER_GROUP_ID | Consumer group ID for the service                | mcp_executor_service  |
| KAFKA_RESULTS_TOPIC     | Topic to publish execution results to            | mcp_results           |
| DEFAULT_MCP_RETRIES     | Number of retry attempts for failed executions   | 3                     |
| LOG_LEVEL               | Logging level (INFO, DEBUG, WARNING, ERROR)      | INFO                  |
| MAX_CONCURRENT_TASKS    | Maximum number of concurrent MCP tool executions | 10                    |

## Quick Start

### 1. Start the MCP Server

```bash
# Start with default settings
python run_mcp_server.py

# Start with debug logging
python run_mcp_server.py --log-level DEBUG

# Check configuration
python run_mcp_server.py --check-config
```

### 2. Send Test Requests

#### SSE Connection Request

```bash
python send_mcp_request.py sse \
  --url http://localhost:8080/sse \
  --tool echo_tool \
  --params '{"message": "Hello SSE!"}'
```

#### Streamable HTTP Connection Request

```bash
python send_mcp_request.py streamable \
  --url http://localhost:8080/mcp \
  --tool echo_tool \
  --params '{"message": "Hello Streamable HTTP!"}'
```

#### SSH Docker Connection Request

```bash
python scripts/send_mcp_request.py ssh \
  --image flamboyant_bartik \
  --tool git_directory_structure \
  --params '{"repo_url": "https://github.com/activepieces/activepieces"}'
```

### 3. Run Comprehensive Tests

```bash
# Test both connection types
python test_mcp_execution.py \
  --sse-url http://localhost:8080/sse \
  --ssh-host server.example.com \
  --ssh-user ubuntu

# Run complete demo
python demo_mcp_dual_connections.py \
  --sse-url http://localhost:8080/sse \
  --ssh-host server.example.com \
  --ssh-user ubuntu
```

## Running the Service

### Enhanced Server Runner (Recommended)

```bash
# Start with default settings
python run_mcp_server.py

# Start with custom configuration
python run_mcp_server.py \
  --log-level DEBUG \
  --max-concurrent-tasks 20 \
  --verbose
```

### Original Method (Still Supported)

```bash
# Using Poetry
poetry run python -m app.main

# Using the local script
./run_local.sh
```

### Production Deployment

For production, use the enhanced server runner with appropriate configuration:

```bash
python run_mcp_server.py \
  --log-level WARNING \
  --max-concurrent-tasks 50
```

## Message Formats

### SSE Connection Request

```json
{
  "request_id": "unique-request-id",
  "server_script_path": "http://mcp-server:8080/sse",
  "connection_type": "sse",
  "tool_name": "example_tool",
  "tool_parameters": {
    "param1": "value1",
    "param2": "value2"
  },
  "retries": 3
}
```

### Streamable HTTP Connection Request

```json
{
  "request_id": "unique-request-id",
  "server_script_path": "http://mcp-server:8080/mcp",
  "connection_type": "streamable_http",
  "headers": {
    "Authorization": "Bearer token123"
  },
  "tool_name": "example_tool",
  "tool_parameters": {
    "param1": "value1",
    "param2": "value2"
  },
  "retries": 3
}
```

### SSH Docker Connection Request

```json
{
  "request_id": "unique-request-id",
  "ssh_host": "server.example.com",
  "ssh_user": "ubuntu",
  "ssh_port": 22,
  "ssh_key_path": "/path/to/private/key",
  "docker_image": "mcp-text-server",
  "tool_name": "example_tool",
  "tool_parameters": {
    "param1": "value1",
    "param2": "value2"
  },
  "retries": 3
}
```

### Response Message (Success)

```json
{
  "request_id": "unique-request-id",
  "result": [
    {
      "type": "text",
      "text": "Tool execution result"
    }
  ],
  "mcp_status": "success"
}
```

### Response Message (Error)

```json
{
  "request_id": "unique-request-id",
  "error": "Error message description",
  "mcp_status": "error"
}
```

## Available Scripts

### Server Scripts

- **`run_mcp_server.py`** - Enhanced server runner with dual connection support
- **`app/main.py`** - Original server entry point (still supported)

### Client Scripts

- **`send_mcp_request.py`** - Send individual requests to the service
- **`test_mcp_execution.py`** - Comprehensive test suite for both connection types
- **`demo_mcp_dual_connections.py`** - Complete demo showing both connections

### Test Scripts

- **`tests/test_sse_connection.py`** - SSE-specific tests
- **`tests/test_dual_connection_integration.py`** - Dual connection comparative tests
- **`tests/quick_sse_validation.py`** - Quick validation (no external dependencies)

## Error Handling

- **Automatic Retries**: Failed executions are retried with exponential backoff
- **Error Responses**: After all retries are exhausted, errors are sent to the results topic
- **Dead Letter Queue**: Additional error information is sent to `mcp_dead_letter_queue`
- **Connection-Specific Errors**: Different error handling for SSE vs SSH Docker connections
- **Comprehensive Logging**: Detailed error information for debugging

## Testing and Validation

### Quick Validation

```bash
# Test basic functionality without external dependencies
python tests/quick_sse_validation.py
```

### SSE Connection Tests

```bash
# Test SSE functionality through Kafka
python tests/run_sse_tests.py
```

### Dual Connection Integration Tests

```bash
# Test both connection types comparatively
python tests/test_dual_connection_integration.py \
  --sse-url http://localhost:8080/sse \
  --ssh-host server.example.com \
  --ssh-user ubuntu
```

### Shell Script Tests

```bash
# Easy-to-use shell wrapper
./tests/run_dual_tests.sh \
  --ssh-host server.example.com \
  --ssh-user ubuntu
```

## Development

### Project Structure

```
mcp-execution-service/
├── app/
│   ├── __init__.py
│   ├── main.py                 # Original entry point
│   ├── config/                 # Configuration
│   │   ├── __init__.py
│   │   └── config.py           # Settings using Pydantic
│   └── core_/                  # Core functionality
│       ├── __init__.py
│       ├── client.py           # Dual connection MCP client
│       ├── kafka_service.py    # Enhanced Kafka consumer/producer
│       └── mcp_executor.py     # MCP execution logic
├── tests/                      # Test suite
│   ├── test_sse_connection.py  # SSE-specific tests
│   ├── test_dual_connection_integration.py # Dual connection tests
│   ├── quick_sse_validation.py # Quick validation
│   └── run_*.py               # Test runners
├── run_mcp_server.py          # Enhanced server runner
├── send_mcp_request.py        # Request sender tool
├── test_mcp_execution.py      # Execution test suite
├── demo_mcp_dual_connections.py # Demo script
├── .env.example               # Example environment variables
├── Dockerfile                 # Docker configuration
├── poetry.lock                # Poetry lock file
├── pyproject.toml             # Poetry dependencies
└── README.md                  # This file
```

## Troubleshooting

### Common Issues

#### Kafka Connection Failed

```
❌ Failed to start Kafka consumer/producer: KafkaError
```

**Solution**: Check Kafka broker is running and `KAFKA_BOOTSTRAP_SERVERS` is correct

#### SSE Connection Failed

```
❌ SSE connection failed: Connection refused
```

**Solution**: Verify SSE server is running and accessible at the specified URL

#### SSH Docker Connection Failed

```
❌ SSH Docker connection failed: Authentication failed
```

**Solution**: Check SSH credentials, key permissions, and Docker availability on SSH server

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
python run_mcp_server.py --log-level DEBUG --verbose
```

### Health Checks

```bash
# Check server configuration
python run_mcp_server.py --check-config

# Test basic functionality
python tests/quick_sse_validation.py
```

## Examples

### Complete Workflow Example

```bash
# Terminal 1: Start the server
python run_mcp_server.py --verbose

# Terminal 2: Send SSE request
python scripts/send_mcp_request.py sse \
  --url http://localhost:8080/sse \
  --tool echo_tool \
  --params '{"message": "Hello SSE!"}'

# Terminal 3: Send SSH Docker request
python scripts/send_mcp_request.py ssh \
  --host server.example.com \
  --user ubuntu \
  --key ~/.ssh/id_rsa \
  --tool echo_tool \
  --params '{"message": "Hello SSH Docker!"}'
```

### SSH Environment Variables Example

```bash
# Set SSH connection environment variables
export SSH_HOST="server.example.com"
export SSH_USER="ubuntu"
export SSH_PORT="22"
export SSH_KEY_PATH="~/.ssh/id_rsa"

# Now SSH Docker requests can omit connection parameters
python send_mcp_request.py ssh \
  --tool echo_tool \
  --params '{"message": "Using environment variables!"}'
```

### Production Example

```bash
# Set production environment
export KAFKA_BOOTSTRAP_SERVERS="kafka-cluster:9092"
export LOG_LEVEL="WARNING"
export MAX_CONCURRENT_TASKS="50"

# Set SSH defaults for production
export SSH_HOST="prod-mcp-server.company.com"
export SSH_USER="mcp-service"
export SSH_KEY_PATH="/etc/ssh/mcp-service-key"

# Start production server
python run_mcp_server.py \
  --max-concurrent-tasks 50 \
  --log-level WARNING
```

## License

[Specify license information here]

## Contributors

[List contributors here]
