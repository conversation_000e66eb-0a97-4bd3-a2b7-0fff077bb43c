#!/usr/bin/env python3
"""
MCP Dual Connection Demo Script

This script demonstrates the complete workflow of the MCP execution service
with both SSE and SSH Docker connection types. It shows how to:

1. Start the MCP server (in background)
2. Send requests using both connection types
3. Validate responses
4. Compare performance between connection types

Usage:
    python demo_mcp_dual_connections.py [options]
"""

import asyncio
import argparse
import json
import logging
import subprocess
import sys
import time
import uuid
from pathlib import Path
from typing import Dict, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config.config import settings
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MCPDualConnectionDemo:
    """Demo class for MCP dual connection functionality."""

    def __init__(self, timeout: int = 30):
        self.timeout = timeout
        self.server_process: Optional[subprocess.Popen] = None

    async def start_server_background(self) -> bool:
        """Start MCP server in background."""
        try:
            logger.info("🚀 Starting MCP server in background...")

            # Start server process
            self.server_process = subprocess.Popen(
                [sys.executable, "run_mcp_server.py", "--log-level", "WARNING"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=project_root,
            )

            # Give server time to start
            await asyncio.sleep(5)

            # Check if server is still running
            if self.server_process.poll() is None:
                logger.info("✅ MCP server started successfully")
                return True
            else:
                logger.error("❌ MCP server failed to start")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to start MCP server: {e}")
            return False

    def stop_server(self):
        """Stop the background MCP server."""
        if self.server_process:
            logger.info("⏹️ Stopping MCP server...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=10)
                logger.info("✅ MCP server stopped")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ Force killing MCP server...")
                self.server_process.kill()
                self.server_process.wait()

    async def send_request(self, payload: Dict[str, Any]) -> str:
        """Send request to Kafka topic."""
        producer = None
        try:
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            )
            await producer.start()

            request_id = payload["request_id"]
            await producer.send_and_wait(settings.kafka_consumer_topic, value=payload)

            return request_id

        finally:
            if producer:
                await producer.stop()

    async def wait_for_response(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Wait for response from Kafka results topic."""
        consumer = None
        try:
            consumer = AIOKafkaConsumer(
                settings.kafka_results_topic,
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=f"mcp-demo-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await consumer.start()

            start_time = time.time()
            while time.time() - start_time < self.timeout:
                try:
                    messages = await consumer.getmany(timeout_ms=1000)

                    for tp, msgs in messages.items():
                        for msg in msgs:
                            try:
                                response = json.loads(msg.value.decode("utf-8"))
                                if response.get("request_id") == request_id:
                                    return response
                            except json.JSONDecodeError:
                                continue

                except Exception as e:
                    logger.warning(f"Error consuming messages: {e}")

                await asyncio.sleep(0.1)

            return None

        finally:
            if consumer:
                await consumer.stop()

    async def demo_sse_connection(self, sse_url: str) -> Dict[str, Any]:
        """Demonstrate SSE connection."""
        logger.info(f"📡 Demonstrating SSE connection to {sse_url}")

        request_id = str(uuid.uuid4())
        payload = {
            "request_id": request_id,
            "server_script_path": sse_url,
            "tool_name": "echo_tool",
            "tool_parameters": {
                "message": "Hello from SSE demo!",
                "timestamp": time.time(),
            },
            "retries": 2,
        }

        start_time = time.time()

        try:
            await self.send_request(payload)
            logger.info(f"📤 Sent SSE request: {request_id}")

            response = await self.wait_for_response(request_id)
            end_time = time.time()

            if response:
                success = response.get("mcp_status") == "success"
                logger.info(
                    f"📥 SSE response received: {'✅ Success' if success else '❌ Failed'}"
                )

                return {
                    "connection_type": "SSE",
                    "success": success,
                    "response_time": end_time - start_time,
                    "response": response,
                }
            else:
                logger.error("❌ No SSE response received")
                return {
                    "connection_type": "SSE",
                    "success": False,
                    "response_time": end_time - start_time,
                    "error": "No response received",
                }

        except Exception as e:
            logger.error(f"❌ SSE demo failed: {e}")
            return {
                "connection_type": "SSE",
                "success": False,
                "response_time": time.time() - start_time,
                "error": str(e),
            }

    async def demo_ssh_docker_connection(
        self,
        ssh_host: str,
        ssh_user: str,
        ssh_port: int = 22,
        ssh_key_path: Optional[str] = None,
        docker_image: str = "mcp-text-server",
    ) -> Dict[str, Any]:
        """Demonstrate SSH Docker connection."""
        logger.info(
            f"🐳 Demonstrating SSH Docker connection to {ssh_user}@{ssh_host}:{ssh_port}"
        )

        request_id = str(uuid.uuid4())
        payload = {
            "request_id": request_id,
            "ssh_host": ssh_host,
            "ssh_user": ssh_user,
            "ssh_port": ssh_port,
            "docker_image": docker_image,
            "tool_name": "echo_tool",
            "tool_parameters": {
                "message": "Hello from SSH Docker demo!",
                "timestamp": time.time(),
            },
            "retries": 2,
        }

        if ssh_key_path:
            payload["ssh_key_path"] = ssh_key_path

        start_time = time.time()

        try:
            await self.send_request(payload)
            logger.info(f"📤 Sent SSH Docker request: {request_id}")

            response = await self.wait_for_response(request_id)
            end_time = time.time()

            if response:
                success = response.get("mcp_status") == "success"
                logger.info(
                    f"📥 SSH Docker response received: {'✅ Success' if success else '❌ Failed'}"
                )

                return {
                    "connection_type": "SSH_Docker",
                    "success": success,
                    "response_time": end_time - start_time,
                    "response": response,
                }
            else:
                logger.error("❌ No SSH Docker response received")
                return {
                    "connection_type": "SSH_Docker",
                    "success": False,
                    "response_time": end_time - start_time,
                    "error": "No response received",
                }

        except Exception as e:
            logger.error(f"❌ SSH Docker demo failed: {e}")
            return {
                "connection_type": "SSH_Docker",
                "success": False,
                "response_time": time.time() - start_time,
                "error": str(e),
            }

    def print_demo_results(
        self, sse_result: Dict[str, Any], ssh_result: Dict[str, Any]
    ):
        """Print comprehensive demo results."""
        print("\n" + "=" * 80)
        print("🎯 MCP DUAL CONNECTION DEMO RESULTS")
        print("=" * 80)

        # SSE Results
        print(f"\n📡 SSE Connection Results:")
        print(f"   Success: {'✅' if sse_result['success'] else '❌'}")
        print(f"   Response Time: {sse_result['response_time']:.2f}s")
        if sse_result["success"]:
            result_data = sse_result["response"].get("result", [])
            print(f"   Result: {result_data}")
        else:
            error = sse_result.get("error", "Unknown error")
            print(f"   Error: {error}")

        # SSH Docker Results
        print(f"\n🐳 SSH Docker Connection Results:")
        print(f"   Success: {'✅' if ssh_result['success'] else '❌'}")
        print(f"   Response Time: {ssh_result['response_time']:.2f}s")
        if ssh_result["success"]:
            result_data = ssh_result["response"].get("result", [])
            print(f"   Result: {result_data}")
        else:
            error = ssh_result.get("error", "Unknown error")
            print(f"   Error: {error}")

        # Comparison
        print(f"\n📊 Performance Comparison:")
        if sse_result["success"] and ssh_result["success"]:
            ratio = ssh_result["response_time"] / sse_result["response_time"]
            print(
                f"   SSH Docker is {ratio:.1f}x {'slower' if ratio > 1 else 'faster'} than SSE"
            )
            print(f"   Both connection types are working correctly! ✅")
        elif sse_result["success"]:
            print(f"   Only SSE connection is working ⚠️")
        elif ssh_result["success"]:
            print(f"   Only SSH Docker connection is working ⚠️")
        else:
            print(f"   Both connection types failed ❌")

        print("=" * 80)

    async def run_demo(
        self,
        sse_url: Optional[str] = None,
        ssh_host: Optional[str] = None,
        ssh_user: Optional[str] = None,
        ssh_port: int = 22,
        ssh_key_path: Optional[str] = None,
        docker_image: str = "mcp-text-server",
        start_server: bool = True,
    ) -> bool:
        """Run the complete dual connection demo."""
        logger.info("🎬 Starting MCP Dual Connection Demo")
        logger.info("=" * 60)

        try:
            # Start server if requested
            if start_server:
                server_started = await self.start_server_background()
                if not server_started:
                    logger.error("❌ Cannot proceed without MCP server")
                    return False

                # Give server additional time to fully initialize
                logger.info("⏳ Waiting for server to fully initialize...")
                await asyncio.sleep(3)

            results = {}

            # Demo SSE connection if URL provided
            if sse_url:
                results["sse"] = await self.demo_sse_connection(sse_url)
                await asyncio.sleep(2)  # Brief pause between demos
            else:
                logger.warning("⚠️ No SSE URL provided, skipping SSE demo")
                results["sse"] = {
                    "connection_type": "SSE",
                    "success": False,
                    "response_time": 0,
                    "error": "Not tested",
                }

            # Demo SSH Docker connection if host/user provided
            if ssh_host and ssh_user:
                results["ssh"] = await self.demo_ssh_docker_connection(
                    ssh_host=ssh_host,
                    ssh_user=ssh_user,
                    ssh_port=ssh_port,
                    ssh_key_path=ssh_key_path,
                    docker_image=docker_image,
                )
            else:
                logger.warning("⚠️ No SSH host/user provided, skipping SSH Docker demo")
                results["ssh"] = {
                    "connection_type": "SSH_Docker",
                    "success": False,
                    "response_time": 0,
                    "error": "Not tested",
                }

            # Print results
            self.print_demo_results(results["sse"], results["ssh"])

            # Determine overall success
            any_success = results["sse"]["success"] or results["ssh"]["success"]
            return any_success

        finally:
            if start_server:
                self.stop_server()


def create_cli_parser() -> argparse.ArgumentParser:
    """Create command-line interface parser."""
    parser = argparse.ArgumentParser(
        description="MCP Dual Connection Demo",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Demo both connection types
  python demo_mcp_dual_connections.py \\
    --sse-url http://localhost:8080/sse \\
    --ssh-host server.example.com \\
    --ssh-user ubuntu

  # Demo SSE only
  python demo_mcp_dual_connections.py --sse-url http://localhost:8080/sse

  # Demo SSH Docker only
  python demo_mcp_dual_connections.py --ssh-host server.com --ssh-user ubuntu

  # Use existing server (don't start new one)
  python demo_mcp_dual_connections.py \\
    --sse-url http://localhost:8080/sse \\
    --no-start-server
        """,
    )

    # Connection options
    parser.add_argument("--sse-url", help="SSE server URL to demo")
    parser.add_argument("--ssh-host", help="SSH server hostname")
    parser.add_argument("--ssh-user", help="SSH username")
    parser.add_argument("--ssh-port", type=int, default=22, help="SSH port")
    parser.add_argument("--ssh-key", help="SSH private key path")
    parser.add_argument(
        "--docker-image", default="mcp-text-server", help="Docker image name"
    )

    # Demo options
    parser.add_argument(
        "--no-start-server",
        action="store_true",
        help="Don't start MCP server (use existing)",
    )
    parser.add_argument(
        "--timeout", type=int, default=30, help="Response timeout in seconds"
    )
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    return parser


async def main():
    """Main entry point."""
    parser = create_cli_parser()
    args = parser.parse_args()

    # Validate arguments
    if not args.sse_url and not (args.ssh_host and args.ssh_user):
        logger.error(
            "❌ Must provide either --sse-url or both --ssh-host and --ssh-user"
        )
        parser.print_help()
        return 1

    # Configure logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create demo instance
    demo = MCPDualConnectionDemo(timeout=args.timeout)

    try:
        success = await demo.run_demo(
            sse_url=args.sse_url,
            ssh_host=args.ssh_host,
            ssh_user=args.ssh_user,
            ssh_port=args.ssh_port,
            ssh_key_path=args.ssh_key,
            docker_image=args.docker_image,
            start_server=not args.no_start_server,
        )

        return 0 if success else 1

    except KeyboardInterrupt:
        logger.info("⏹️ Demo cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"💥 Demo failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    print("🎬 MCP Dual Connection Demo")
    print("   Demonstrates both SSE and SSH Docker connections")
    print("")

    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Demo cancelled")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
