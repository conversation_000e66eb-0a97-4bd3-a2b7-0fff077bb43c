#!/usr/bin/env python3
"""
MCP Request Sender

This script sends MCP tool execution requests to the Kafka topic and listens
for responses. It supports both SSE and SSH Docker connection types.

Usage:
    # SSE connection
    python send_mcp_request.py sse --url http://localhost:8080/sse --tool echo_tool --params '{"message": "hello"}'

    # SSH Docker connection
    python send_mcp_request.py ssh --host server.com --user ubuntu --tool echo_tool --params '{"message": "hello"}'
"""

import asyncio
import os
import argparse
import json
import logging
import sys
import time
import uuid
from pathlib import Path
from typing import Dict, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

print(os.getenv("KAFKA_BOOTSTRAP_SERVERS"))
print(os.getenv("KAFKA_CONSUMER_TOPIC"))
print(os.getenv("KAFKA_RESULTS_TOPIC"))
print(os.getenv("KAFKA_CONSUMER_GROUP_ID"))


class MCPRequestSender:
    """Class for sending MCP requests and receiving responses."""

    def __init__(self, timeout: int = 30):
        self.timeout = timeout

    async def send_sse_request(
        self,
        server_url: str,
        tool_name: str,
        tool_parameters: Dict[str, Any],
        retries: int = 3,
    ) -> str:
        """Send SSE connection request."""
        request_id = str(uuid.uuid4())

        payload = {
            "request_id": request_id,
            "server_script_path": server_url,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "retries": retries,
        }

        logger.info(f"📤 Sending SSE request to {server_url}")
        logger.info(f"   Tool: {tool_name}")
        logger.info(f"   Parameters: {tool_parameters}")
        logger.info(f"   Request ID: {request_id}")

        await self._send_to_kafka(payload)
        return request_id

    async def send_ssh_request(
        self,
        tool_name: str,
        tool_parameters: Dict[str, Any],
        docker_image: str = "mcp-text-server",
        retries: int = 3,
    ) -> str:
        """Send SSH Docker connection request."""
        request_id = str(uuid.uuid4())

        payload = {
            "request_id": request_id,
            "docker_image": docker_image,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "retries": retries,
        }

        logger.info(f"   Docker Image: {docker_image}")
        logger.info(f"   Tool: {tool_name}")
        logger.info(f"   Parameters: {tool_parameters}")
        logger.info(f"   Request ID: {request_id}")

        await self._send_to_kafka(payload)
        return request_id

    async def send_streamable_request(
        self,
        server_url: str,
        tool_name: str,
        tool_parameters: Dict[str, Any],
        retries: int = 3,
        headers: Optional[Dict[str, str]] = None,
    ) -> str:
        """Send Streamable HTTP connection request."""
        request_id = str(uuid.uuid4())

        payload = {
            "request_id": request_id,
            "server_script_path": server_url,
            "connection_type": "streamable_http",
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "retries": retries,
        }

        if headers:
            payload["headers"] = headers

        logger.info(f"📤 Sending Streamable HTTP request to {server_url}")
        logger.info(f"   Tool: {tool_name}")
        logger.info(f"   Parameters: {tool_parameters}")
        logger.info(f"   Request ID: {request_id}")

        await self._send_to_kafka(payload)
        return request_id

    async def _send_to_kafka(self, payload: Dict[str, Any]):
        """Send payload to Kafka topic."""
        producer = None
        try:
            producer = AIOKafkaProducer(
                bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS"),
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
                max_request_size=524288000,
            )
            await producer.start()

            # Send the request directly to Kafka
            headers = [
                ("request_id", payload.get("request_id").encode("utf-8")),
                ("reply-topic", os.getenv("KAFKA_RESULTS_TOPIC").encode("utf-8")),
            ]

            await producer.send(
                os.getenv("KAFKA_CONSUMER_TOPIC"),
                value=payload,
                headers=headers,
            )
            logger.info(
                f"✅ Request sent to Kafka topic: {os.getenv('KAFKA_CONSUMER_TOPIC')}"
            )

        except Exception as e:
            logger.error(f"❌ Failed to send request to Kafka: {e}")
            raise
        finally:
            if producer:
                await producer.stop()

    async def wait_for_response(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Wait for response from Kafka results topic."""
        consumer = None
        try:
            consumer = AIOKafkaConsumer(
                os.getenv("KAFKA_RESULTS_TOPIC"),
                bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS"),
                group_id=os.getenv("KAFKA_CONSUMER_GROUP_ID"),
                auto_offset_reset="latest",
                enable_auto_commit=True,
                session_timeout_ms=30000,
                max_poll_interval_ms=60000,
                value_deserializer=lambda m: json.loads(m.decode("utf-8")),
                auto_commit_interval_ms=10000,
            )
            await consumer.start()

            logger.info(f"📥 Waiting for response (timeout: {self.timeout}s)...")
            start_time = time.time()

            while time.time() - start_time < self.timeout:
                try:
                    messages = await consumer.getmany(timeout_ms=1000)

                    logger.info(f"📥 Received {len(messages)} messages")

                    for tp, msgs in messages.items():
                        for msg in msgs:
                            try:
                                response = json.loads(msg.value.decode("utf-8"))
                                if response.get("request_id") == request_id:
                                    logger.info(
                                        f"✅ Received response for request: {request_id}"
                                    )
                                    return response
                            except json.JSONDecodeError:
                                continue

                except Exception as e:
                    logger.warning(f"Error consuming messages: {e}")

                await asyncio.sleep(0.1)

            logger.warning(f"⏰ Timeout waiting for response after {self.timeout}s")
            return None

        finally:
            if consumer:
                await consumer.stop()

    def print_response(self, response: Dict[str, Any]):
        """Print formatted response."""
        print("\n" + "=" * 60)
        print("📋 MCP EXECUTION RESULT")
        print("=" * 60)

        request_id = response.get("request_id", "unknown")
        status = response.get("mcp_status", "unknown")

        print(f"Request ID: {request_id}")
        print(f"Status: {status}")

        if status == "success":
            print("✅ Execution successful!")
            result = response.get("result", [])
            print(f"\nResult:")
            if isinstance(result, list) and result:
                for i, item in enumerate(result):
                    print(f"  [{i}] {item}")
            else:
                print(f"  {result}")
        elif status == "error":
            print("❌ Execution failed!")
            error = response.get("error", "Unknown error")
            print(f"\nError: {error}")
        else:
            print(f"⚠️ Unknown status: {status}")
            print(f"\nRaw response: {response}")

        print("=" * 60)


def create_cli_parser() -> argparse.ArgumentParser:
    """Create command-line interface parser."""
    parser = argparse.ArgumentParser(
        description="Send MCP tool execution requests",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    subparsers = parser.add_subparsers(dest="connection_type", help="Connection type")

    # SSE connection subparser
    sse_parser = subparsers.add_parser("sse", help="Send SSE connection request")
    sse_parser.add_argument("--url", required=True, help="SSE server URL")
    sse_parser.add_argument("--tool", required=True, help="Tool name to execute")
    sse_parser.add_argument(
        "--params", required=True, help="Tool parameters as JSON string"
    )
    sse_parser.add_argument("--retries", type=int, default=3, help="Number of retries")
    sse_parser.add_argument("--headers", help="HTTP headers as JSON string")

    # Streamable HTTP connection subparser
    streamable_parser = subparsers.add_parser(
        "streamable", help="Send Streamable HTTP connection request"
    )
    streamable_parser.add_argument(
        "--url", required=True, help="Streamable HTTP server URL"
    )
    streamable_parser.add_argument("--tool", required=True, help="Tool name to execute")
    streamable_parser.add_argument(
        "--params", required=True, help="Tool parameters as JSON string"
    )
    streamable_parser.add_argument(
        "--retries", type=int, default=3, help="Number of retries"
    )
    streamable_parser.add_argument("--headers", help="HTTP headers as JSON string")

    # SSH Docker connection subparser
    ssh_parser = subparsers.add_parser("ssh", help="Send SSH Docker connection request")

    ssh_parser.add_argument(
        "--image", default="mcp-text-server", help="Docker image name"
    )
    ssh_parser.add_argument("--tool", required=True, help="Tool name to execute")
    ssh_parser.add_argument(
        "--params", required=True, help="Tool parameters as JSON string"
    )
    ssh_parser.add_argument("--retries", type=int, default=3, help="Number of retries")

    # Common options
    parser.add_argument(
        "--timeout", type=int, default=30, help="Response timeout in seconds"
    )
    parser.add_argument(
        "--no-wait", action="store_true", help="Don't wait for response"
    )
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    return parser


async def main():
    """Main entry point."""
    parser = create_cli_parser()
    args = parser.parse_args()

    if not args.connection_type:
        parser.print_help()
        return 1

    # Configure logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Parse tool parameters
    try:
        tool_parameters = json.loads(args.params)
    except json.JSONDecodeError as e:
        logger.error(f"❌ Invalid JSON in tool parameters: {e}")
        return 1

    # Parse headers if provided
    headers = None
    if hasattr(args, "headers") and args.headers:
        try:
            headers = json.loads(args.headers)
        except json.JSONDecodeError as e:
            logger.error(f"❌ Invalid JSON in headers: {e}")
            return 1

    # Create sender
    sender = MCPRequestSender(timeout=args.timeout)

    try:
        # Send request based on connection type
        if args.connection_type == "sse":
            request_id = await sender.send_sse_request(
                server_url=args.url,
                tool_name=args.tool,
                tool_parameters=tool_parameters,
                retries=args.retries,
            )
        elif args.connection_type == "streamable":
            request_id = await sender.send_streamable_request(
                server_url=args.url,
                tool_name=args.tool,
                tool_parameters=tool_parameters,
                retries=args.retries,
                headers=headers,
            )
        elif args.connection_type == "ssh":
            request_id = await sender.send_ssh_request(
                docker_image=args.image,
                tool_name=args.tool,
                tool_parameters=tool_parameters,
                retries=args.retries,
            )
        else:
            logger.error(f"❌ Unknown connection type: {args.connection_type}")
            return 1

        if args.no_wait:
            print(f"✅ Request sent with ID: {request_id}")
            print("   Use --no-wait=false to wait for response")
            return 0

        # Wait for response
        response = await sender.wait_for_response(request_id)

        if response:
            sender.print_response(response)

            # Return appropriate exit code
            status = response.get("mcp_status")
            return 0 if status == "success" else 1
        else:
            logger.error("❌ No response received within timeout")
            return 1

    except KeyboardInterrupt:
        logger.info("⏹️ Request cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    print("📤 MCP Request Sender")
    print("   Send requests to MCP execution service via Kafka")
    print("")

    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Cancelled by user")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
