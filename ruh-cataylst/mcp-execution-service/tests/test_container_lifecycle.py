#!/usr/bin/env python3
"""
Test script for container lifecycle integration in MCP executor service.
"""
import asyncio
import logging
import json
from app.services.container_client import ContainerManagementClient
from app.core_.mcp_executor import MCPExecutor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MockProducer:
    """Mock Kafka producer for testing."""
    
    async def send(self, topic, message, headers=None):
        logger.info(f"Mock producer sending to {topic}: {message}")
        
    async def send_and_wait(self, topic, value):
        logger.info(f"Mock producer sending and waiting to {topic}: {value}")


async def test_container_client():
    """Test the container management client."""
    logger.info("Testing container management client...")
    
    client = ContainerManagementClient()
    
    # Test create container
    logger.info("Testing create container...")
    success, message, container_id = await client.create_container(
        mcp_id="test-mcp-id",
        user_id="test-user-id",
        container_type="stdio",
        env={"TEST_VAR": "test_value"}
    )
    
    logger.info(f"Create container result: success={success}, message={message}, container_id={container_id}")
    
    if success and container_id:
        # Test get status
        logger.info("Testing get container status...")
        success, message, details = await client.get_container_status(container_id)
        logger.info(f"Get status result: success={success}, message={message}, details={details}")
        
        # Test stop container
        logger.info("Testing stop container...")
        success, message = await client.stop_container(container_id)
        logger.info(f"Stop container result: success={success}, message={message}")
        
        # Test delete container
        logger.info("Testing delete container...")
        success, message = await client.delete_container(container_id)
        logger.info(f"Delete container result: success={success}, message={message}")


async def test_mcp_executor_with_container_lifecycle():
    """Test the MCP executor with container lifecycle."""
    logger.info("Testing MCP executor with container lifecycle...")
    
    # Create mock producer
    mock_producer = MockProducer()
    
    # Create MCP executor
    executor = MCPExecutor(producer=mock_producer, logger=logger)
    
    # Test parameters
    test_params = {
        "server_script_path": "http://localhost:8080/sse",
        "tool_name": "test_tool",
        "tool_parameters": {"message": "Hello from container lifecycle test!"},
        "retries": 1,
        "correlation_id": "test-correlation-id",
        "user_id": "test-user-id",
        "mcp_id": "test-mcp-id"
    }
    
    try:
        # This should use the container lifecycle
        result = await executor.execute_tool(**test_params)
        logger.info(f"MCP executor result: {result}")
    except Exception as e:
        logger.error(f"MCP executor failed (expected for test): {e}")
    
    # Test without user_id and mcp_id (should use regular execution)
    test_params_no_lifecycle = {
        "server_script_path": "http://localhost:8080/sse",
        "tool_name": "test_tool",
        "tool_parameters": {"message": "Hello from regular execution!"},
        "retries": 1,
        "correlation_id": "test-correlation-id-2"
    }
    
    try:
        # This should use regular execution
        result = await executor.execute_tool(**test_params_no_lifecycle)
        logger.info(f"MCP executor result (regular): {result}")
    except Exception as e:
        logger.error(f"MCP executor failed (expected for test): {e}")


async def main():
    """Main test function."""
    logger.info("Starting container lifecycle integration tests...")
    
    try:
        # Test 1: Container client
        await test_container_client()
        
        logger.info("=" * 50)
        
        # Test 2: MCP executor with container lifecycle
        await test_mcp_executor_with_container_lifecycle()
        
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
    
    logger.info("Container lifecycle integration tests completed.")


if __name__ == "__main__":
    asyncio.run(main())
