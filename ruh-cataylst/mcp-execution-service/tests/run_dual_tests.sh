#!/bin/bash
# Dual Connection Integration Test Runner
# This script provides easy-to-use commands for running dual connection tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
SSE_URL="http://localhost:8080/sse"
SSH_HOST=""
SSH_USER=""
SSH_PORT="22"
SSH_KEY=""
DOCKER_IMAGE="mcp-text-server"
OUTPUT_FILE=""
QUICK_MODE=false
VERBOSE=false
QUIET=false

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_usage() {
    echo -e "${BLUE}Dual Connection Integration Test Runner${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Required Options:"
    echo "  --ssh-host HOST        SSH server hostname or IP address"
    echo "  --ssh-user USER        SSH username"
    echo ""
    echo "Optional Options:"
    echo "  --sse-url URL          SSE server URL (default: $SSE_URL)"
    echo "  --ssh-port PORT        SSH port (default: $SSH_PORT)"
    echo "  --ssh-key PATH         Path to SSH private key file"
    echo "  --docker-image IMAGE   Docker image name (default: $DOCKER_IMAGE)"
    echo "  --output FILE          Output file for detailed JSON report"
    echo "  --quick                Run quick test mode (fewer test cases)"
    echo "  --verbose              Enable verbose logging"
    echo "  --quiet                Reduce logging output"
    echo "  --help                 Show this help message"
    echo ""
    echo "Examples:"
    echo "  # Basic test"
    echo "  $0 --ssh-host server.example.com --ssh-user ubuntu"
    echo ""
    echo "  # Test with SSH key"
    echo "  $0 --ssh-host ************* --ssh-user deploy --ssh-key ~/.ssh/id_rsa"
    echo ""
    echo "  # Quick test with custom SSE URL"
    echo "  $0 --sse-url https://api.example.com/sse --ssh-host server.com --ssh-user testuser --quick"
    echo ""
    echo "  # Verbose test with output file"
    echo "  $0 --ssh-host server.com --ssh-user ubuntu --verbose --output test_results.json"
}

print_error() {
    echo -e "${RED}Error: $1${NC}" >&2
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

check_requirements() {
    print_info "Checking requirements..."
    
    # Check if Python is available
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check if the test script exists
    if [ ! -f "$SCRIPT_DIR/test_dual_connection_integration.py" ]; then
        print_error "Test script not found: $SCRIPT_DIR/test_dual_connection_integration.py"
        exit 1
    fi
    
    # Check if we can import required modules
    cd "$PROJECT_ROOT"
    if ! python3 -c "import asyncio, json, uuid, logging, aiokafka" 2>/dev/null; then
        print_warning "Some Python dependencies may be missing. Install with:"
        echo "  pip install aiokafka pytest pytest-asyncio"
    fi
    
    print_success "Requirements check passed"
}

validate_ssh_connection() {
    if [ -n "$SSH_KEY" ]; then
        if [ ! -f "$SSH_KEY" ]; then
            print_error "SSH key file not found: $SSH_KEY"
            exit 1
        fi
        
        print_info "Testing SSH connection with key..."
        if ssh -i "$SSH_KEY" -p "$SSH_PORT" -o ConnectTimeout=10 -o BatchMode=yes "$SSH_USER@$SSH_HOST" "echo 'SSH connection test successful'" 2>/dev/null; then
            print_success "SSH connection test passed"
        else
            print_warning "SSH connection test failed. The test may still work if the server allows the connection."
        fi
    else
        print_info "Testing SSH connection..."
        if ssh -p "$SSH_PORT" -o ConnectTimeout=10 -o BatchMode=yes "$SSH_USER@$SSH_HOST" "echo 'SSH connection test successful'" 2>/dev/null; then
            print_success "SSH connection test passed"
        else
            print_warning "SSH connection test failed. The test may still work if the server allows the connection."
        fi
    fi
}

check_kafka_connection() {
    print_info "Checking Kafka configuration..."
    
    cd "$PROJECT_ROOT"
    if python3 -c "
from app.config.config import settings
print(f'Kafka Bootstrap Servers: {settings.kafka_bootstrap_servers}')
print(f'Consumer Topic: {settings.kafka_consumer_topic}')
print(f'Results Topic: {settings.kafka_results_topic}')
" 2>/dev/null; then
        print_success "Kafka configuration loaded successfully"
    else
        print_warning "Could not load Kafka configuration. Check your environment variables."
    fi
}

run_test() {
    print_info "Starting dual connection integration test..."
    
    # Build command
    cmd="python3 $SCRIPT_DIR/test_dual_connection_integration.py"
    cmd="$cmd --sse-url '$SSE_URL'"
    cmd="$cmd --ssh-host '$SSH_HOST'"
    cmd="$cmd --ssh-user '$SSH_USER'"
    cmd="$cmd --ssh-port $SSH_PORT"
    
    if [ -n "$SSH_KEY" ]; then
        cmd="$cmd --ssh-key '$SSH_KEY'"
    fi
    
    if [ -n "$DOCKER_IMAGE" ]; then
        cmd="$cmd --docker-image '$DOCKER_IMAGE'"
    fi
    
    if [ -n "$OUTPUT_FILE" ]; then
        cmd="$cmd --output '$OUTPUT_FILE'"
    fi
    
    if [ "$QUICK_MODE" = true ]; then
        cmd="$cmd --quick"
    fi
    
    if [ "$VERBOSE" = true ]; then
        cmd="$cmd --verbose"
    fi
    
    if [ "$QUIET" = true ]; then
        cmd="$cmd --quiet"
    fi
    
    print_info "Executing: $cmd"
    echo ""
    
    cd "$PROJECT_ROOT"
    if eval "$cmd"; then
        print_success "✅ Dual connection integration test PASSED!"
        return 0
    else
        print_error "❌ Dual connection integration test FAILED!"
        return 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --sse-url)
            SSE_URL="$2"
            shift 2
            ;;
        --ssh-host)
            SSH_HOST="$2"
            shift 2
            ;;
        --ssh-user)
            SSH_USER="$2"
            shift 2
            ;;
        --ssh-port)
            SSH_PORT="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --docker-image)
            DOCKER_IMAGE="$2"
            shift 2
            ;;
        --output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --quick)
            QUICK_MODE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --quiet)
            QUIET=true
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Validate required arguments
if [ -z "$SSH_HOST" ] || [ -z "$SSH_USER" ]; then
    print_error "SSH host and user are required"
    print_usage
    exit 1
fi

# Main execution
main() {
    echo -e "${BLUE}🔧 Dual Connection Integration Test Runner${NC}"
    echo "=================================================="
    echo ""
    
    check_requirements
    echo ""
    
    check_kafka_connection
    echo ""
    
    validate_ssh_connection
    echo ""
    
    run_test
    
    if [ $? -eq 0 ]; then
        echo ""
        print_success "🎉 All tests completed successfully!"
        if [ -n "$OUTPUT_FILE" ]; then
            print_info "📄 Detailed report saved to: $OUTPUT_FILE"
        fi
    else
        echo ""
        print_error "💥 Tests failed. Check the logs above for details."
        exit 1
    fi
}

# Run main function
main
