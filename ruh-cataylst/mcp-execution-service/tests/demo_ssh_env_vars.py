#!/usr/bin/env python3
"""
SSH Environment Variables Demo

This script demonstrates how to use SSH environment variables with the MCP client.
It shows different ways to configure SSH connections using environment variables,
constructor parameters, or a combination of both.

Usage:
    # Set environment variables first
    export SSH_HOST="server.example.com"
    export SSH_USER="ubuntu"
    export SSH_PORT="22"
    export SSH_KEY_PATH="~/.ssh/id_rsa"

    # Then run the demo
    python demo_ssh_env_vars.py
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core_.client import MCPClient


def print_client_config(client: MCPClient, title: str):
    """Print client configuration in a formatted way."""
    print(f"\n📋 {title}")
    print("-" * 50)
    print(f"   Connection Type: {client.connection_type}")
    print(f"   SSH Host: {client.ssh_host}")
    print(f"   SSH User: {client.ssh_user}")
    print(f"   SSH Port: {client.ssh_port}")
    print(f"   SSH Key Path: {client.ssh_key_path}")
    print(f"   Docker Image: {client.docker_image}")


def demo_environment_variables():
    """Demonstrate using SSH environment variables."""
    print("🌍 SSH Environment Variables Demo")
    print("=" * 60)

    # Check current environment variables
    print("\n🔍 Current SSH Environment Variables:")
    ssh_env_vars = ["SSH_HOST", "SSH_USER", "SSH_PORT", "SSH_KEY_PATH"]
    for var in ssh_env_vars:
        value = os.getenv(var)
        if value:
            print(f"   {var}: {value}")
        else:
            print(f"   {var}: (not set)")

    # Demo 1: Using only environment variables
    print("\n" + "=" * 60)
    print("📋 Demo 1: Using Only Environment Variables")

    if os.getenv("SSH_HOST") and os.getenv("SSH_USER"):
        try:
            client = MCPClient()
            print_client_config(client, "Client created from environment variables")
            print("✅ Success: Client created using only environment variables")
        except Exception as e:
            print(f"❌ Failed to create client from environment variables: {e}")
    else:
        print("⚠️ SSH_HOST and SSH_USER environment variables not set")
        print("   Set them with:")
        print("   export SSH_HOST='your-server.com'")
        print("   export SSH_USER='your-username'")

    # Demo 2: Mixing environment variables and constructor parameters
    print("\n" + "=" * 60)
    print("📋 Demo 2: Mixing Environment Variables and Constructor Parameters")

    try:
        # Use environment variables for host/user, override port and key
        client = MCPClient(
            ssh_port=2222,  # Override environment SSH_PORT
            ssh_key_path="/custom/path/to/key",  # Override environment SSH_KEY_PATH
            docker_image="custom-mcp-server",  # Custom docker image
        )
        print_client_config(client, "Client with mixed configuration")
        print("✅ Success: Environment variables + constructor overrides work")
    except Exception as e:
        print(f"❌ Failed to create client with mixed config: {e}")

    # Demo 3: Constructor parameters override environment variables
    print("\n" + "=" * 60)
    print("📋 Demo 3: Constructor Parameters Override Environment Variables")

    try:
        client = MCPClient(
            ssh_host="override-server.com",
            ssh_user="override-user",
            ssh_port=3333,
            ssh_key_path="/override/key/path",
            docker_image="override-image",
        )
        print_client_config(client, "Client with all constructor overrides")
        print("✅ Success: Constructor parameters override environment variables")
    except Exception as e:
        print(f"❌ Failed to create client with constructor overrides: {e}")

    # Demo 4: Show precedence order
    print("\n" + "=" * 60)
    print("📋 Demo 4: Parameter Precedence Order")
    print("\nPrecedence order (highest to lowest):")
    print("   1. Constructor parameters (explicit)")
    print("   2. Environment variables")
    print("   3. Default values")
    print("\nExample:")
    print("   - SSH_HOST env var: 'env-server.com'")
    print("   - Constructor ssh_host: 'constructor-server.com'")
    print("   - Result: 'constructor-server.com' (constructor wins)")


def demo_error_handling():
    """Demonstrate error handling scenarios."""
    print("\n" + "=" * 60)
    print("🚨 Error Handling Demo")
    print("=" * 60)

    # Save original environment
    original_ssh_port = os.environ.get("SSH_PORT")

    try:
        # Demo: Invalid SSH_PORT environment variable
        print("\n📋 Testing Invalid SSH_PORT Environment Variable")
        os.environ["SSH_PORT"] = "invalid-port"

        try:
            client = MCPClient()
            print("❌ Expected error but client was created")
        except ValueError as e:
            if "SSH_PORT environment variable must be an integer" in str(e):
                print(f"✅ Correctly caught invalid SSH_PORT: {e}")
            else:
                print(f"❌ Unexpected error: {e}")

        # Demo: Missing required parameters
        print("\n📋 Testing Missing Required Parameters")
        # Clear SSH environment variables temporarily
        ssh_host_backup = os.environ.get("SSH_HOST")
        ssh_user_backup = os.environ.get("SSH_USER")

        if "SSH_HOST" in os.environ:
            del os.environ["SSH_HOST"]
        if "SSH_USER" in os.environ:
            del os.environ["SSH_USER"]

        try:
            client = MCPClient()
            print("❌ Expected error but client was created")
        except ValueError as e:
            if "No SSH_HOST or SSH_USER environment variables found" in str(e):
                print(f"✅ Correctly caught missing parameters: {e}")
            else:
                print(f"❌ Unexpected error: {e}")

        # Restore environment variables
        if ssh_host_backup:
            os.environ["SSH_HOST"] = ssh_host_backup
        if ssh_user_backup:
            os.environ["SSH_USER"] = ssh_user_backup

    finally:
        # Restore original SSH_PORT
        if original_ssh_port:
            os.environ["SSH_PORT"] = original_ssh_port
        elif "SSH_PORT" in os.environ:
            del os.environ["SSH_PORT"]


def demo_practical_usage():
    """Show practical usage examples."""
    print("\n" + "=" * 60)
    print("💡 Practical Usage Examples")
    print("=" * 60)

    print("\n📋 Example 1: Development Environment")
    print("# Set up development SSH defaults in your shell profile:")
    print("export SSH_HOST='dev-mcp-server.local'")
    print("export SSH_USER='developer'")
    print("export SSH_KEY_PATH='~/.ssh/dev_key'")
    print("")
    print("# Then in your Python code:")
    print("client = MCPClient()  # Uses environment variables")

    print("\n📋 Example 2: Production Environment")
    print("# Set production SSH defaults:")
    print("export SSH_HOST='prod-mcp-server.company.com'")
    print("export SSH_USER='mcp-service'")
    print("export SSH_PORT='2222'")
    print("export SSH_KEY_PATH='/etc/ssh/mcp-service-key'")
    print("")
    print("# Production code can rely on environment:")
    print("client = MCPClient(docker_image='prod-mcp-server:v1.2.3')")

    print("\n📋 Example 3: Testing with Overrides")
    print("# Use environment defaults but override for testing:")
    print("test_client = MCPClient(")
    print("    ssh_host='test-server.local',  # Override for testing")
    print("    docker_image='mcp-server:test'  # Test image")
    print(")")

    print("\n📋 Example 4: Docker Compose Integration")
    print("# In docker-compose.yml:")
    print("services:")
    print("  mcp-service:")
    print("    environment:")
    print("      - SSH_HOST=mcp-backend")
    print("      - SSH_USER=mcp")
    print("      - SSH_KEY_PATH=/secrets/mcp-key")


def main():
    """Main demo function."""
    print("🚀 SSH Environment Variables Demo")
    print("   Demonstrating SSH parameter configuration options")
    print("")

    try:
        demo_environment_variables()
        demo_error_handling()
        demo_practical_usage()

        print("\n" + "=" * 60)
        print("✅ Demo completed successfully!")
        print("   SSH environment variable functionality is working correctly")
        print("=" * 60)

    except Exception as e:
        print(f"\n💥 Demo failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
