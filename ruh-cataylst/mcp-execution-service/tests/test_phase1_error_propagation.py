#!/usr/bin/env python3
"""
Test script for Phase 1 implementation: Core Error Propagation Infrastructure.

Tests error categorization, classification, and Kafka error response formatting.
"""

import pytest
import json
from unittest.mock import MagicMock
from datetime import datetime

from app.core_.exceptions import (
    MCPExecutorError, ErrorCategory, ErrorCode,
    PayloadValidationError, CredentialRetrievalError,
    ContainerCreationError, MCPAuthenticationError
)
from app.core_.error_handler import (
    classify_exception, is_retryable_error, 
    extract_error_context, format_kafka_error_response,
    create_validation_error, create_infrastructure_error
)


class TestErrorCategories:
    """Test error categories and codes are properly defined."""
    
    def test_error_categories_exist(self):
        """Test that all error categories are defined."""
        assert ErrorCategory.VALIDATION_ERROR.value == "validation_error"
        assert ErrorCategory.AUTHENTICATION_ERROR.value == "authentication_error"
        assert ErrorCategory.INFRASTRUCTURE_ERROR.value == "infrastructure_error"
        assert ErrorCategory.MCP_ERROR.value == "mcp_error"
        assert ErrorCategory.SYSTEM_ERROR.value == "system_error"
        assert ErrorCategory.KAFKA_ERROR.value == "kafka_error"
    
    def test_error_codes_exist(self):
        """Test that error codes are properly defined."""
        assert ErrorCode.INVALID_PAYLOAD.value == "invalid_payload"
        assert ErrorCode.CREDENTIAL_NOT_FOUND.value == "credential_not_found"
        assert ErrorCode.CONTAINER_CREATION_FAILED.value == "container_creation_failed"
        assert ErrorCode.MCP_EXECUTION_FAILED.value == "mcp_execution_failed"


class TestMCPExecutorError:
    """Test the enhanced MCPExecutorError base class."""
    
    def test_basic_error_creation(self):
        """Test basic error creation with categorization."""
        error = MCPExecutorError(
            "Test error",
            error_category=ErrorCategory.VALIDATION_ERROR,
            error_code=ErrorCode.INVALID_PAYLOAD,
            retryable=False
        )
        
        assert error.message == "Test error"
        assert error.error_category == ErrorCategory.VALIDATION_ERROR
        assert error.error_code == ErrorCode.INVALID_PAYLOAD
        assert error.retryable is False
    
    def test_kafka_error_conversion(self):
        """Test conversion to Kafka-safe error response."""
        error = MCPExecutorError(
            "Test error",
            error_category=ErrorCategory.AUTHENTICATION_ERROR,
            error_code=ErrorCode.CREDENTIAL_NOT_FOUND,
            details={"user_id": "test_user", "password": "secret123"},
            retryable=True
        )
        
        kafka_error = error.to_kafka_error()
        
        assert kafka_error["error_type"] == "authentication_error"
        assert kafka_error["error_code"] == "credential_not_found"
        assert kafka_error["error_message"] == "Test error"
        assert kafka_error["retryable"] is True
        assert kafka_error["details"]["user_id"] == "test_user"
        assert kafka_error["details"]["password"] == "[REDACTED]"  # Sensitive data redacted
    
    def test_details_sanitization(self):
        """Test that sensitive details are properly sanitized."""
        error = MCPExecutorError(
            "Test error",
            details={
                "user_id": "test_user",
                "password": "secret123",
                "token": "bearer_token",
                "api_key": "api_secret",
                "normal_field": "normal_value",
                "long_field": "x" * 600  # Very long string
            }
        )
        
        sanitized = error._sanitize_details()
        
        assert sanitized["user_id"] == "test_user"
        assert sanitized["password"] == "[REDACTED]"
        assert sanitized["token"] == "[REDACTED]"
        assert sanitized["api_key"] == "[REDACTED]"
        assert sanitized["normal_field"] == "normal_value"
        assert sanitized["long_field"].endswith("... [TRUNCATED]")


class TestSpecificExceptions:
    """Test specific exception classes with new categorization."""
    
    def test_payload_validation_error(self):
        """Test PayloadValidationError categorization."""
        error = PayloadValidationError("user_id", "missing required field")
        
        assert error.error_category == ErrorCategory.VALIDATION_ERROR
        assert error.error_code == ErrorCode.INVALID_PAYLOAD
        assert error.retryable is False
        assert "user_id" in error.message
    
    def test_credential_retrieval_error(self):
        """Test CredentialRetrievalError categorization."""
        error = CredentialRetrievalError("user123", "mcp456", "not found")
        
        assert error.error_category == ErrorCategory.AUTHENTICATION_ERROR
        assert error.error_code == ErrorCode.CREDENTIAL_NOT_FOUND
        assert error.retryable is True
        assert "user123" in error.message
        assert "mcp456" in error.message
    
    def test_container_creation_error(self):
        """Test ContainerCreationError categorization."""
        error = ContainerCreationError("mcp123", "user456", "docker daemon unavailable")
        
        assert error.error_category == ErrorCategory.INFRASTRUCTURE_ERROR
        assert error.error_code == ErrorCode.CONTAINER_CREATION_FAILED
        assert error.retryable is True
        assert "mcp123" in error.message
        assert "user456" in error.message


class TestErrorClassification:
    """Test error classification functions."""
    
    def test_classify_mcp_executor_error(self):
        """Test classification of MCPExecutorError instances."""
        error = PayloadValidationError("test_field", "invalid value")
        classification = classify_exception(error)
        
        assert classification["error_category"] == ErrorCategory.VALIDATION_ERROR
        assert classification["error_code"] == ErrorCode.INVALID_PAYLOAD
        assert classification["retryable"] is False
        assert "test_field" in classification["message"]
    
    def test_classify_json_decode_error(self):
        """Test classification of JSONDecodeError."""
        error = json.JSONDecodeError("Invalid JSON", "test", 0)
        classification = classify_exception(error)
        
        assert classification["error_category"] == ErrorCategory.KAFKA_ERROR
        assert classification["error_code"] == ErrorCode.MESSAGE_DECODE_FAILED
        assert classification["retryable"] is False
        assert "JSON" in classification["message"]
    
    def test_classify_connection_error(self):
        """Test classification of ConnectionError."""
        error = ConnectionError("Network unreachable")
        classification = classify_exception(error)
        
        assert classification["error_category"] == ErrorCategory.INFRASTRUCTURE_ERROR
        assert classification["error_code"] == ErrorCode.NETWORK_ERROR
        assert classification["retryable"] is True
        assert "Network" in classification["message"]
    
    def test_classify_unknown_error(self):
        """Test classification of unknown exceptions."""
        error = RuntimeError("Unknown error")
        classification = classify_exception(error)
        
        assert classification["error_category"] == ErrorCategory.SYSTEM_ERROR
        assert classification["error_code"] == ErrorCode.INTERNAL_ERROR
        assert classification["retryable"] is False
        assert "Unknown error" in classification["message"]
    
    def test_is_retryable_error(self):
        """Test retry determination."""
        retryable_error = ContainerCreationError("mcp1", "user1", "temporary failure")
        non_retryable_error = PayloadValidationError("field", "invalid")
        
        assert is_retryable_error(retryable_error) is True
        assert is_retryable_error(non_retryable_error) is False


class TestKafkaErrorFormatting:
    """Test Kafka error response formatting."""
    
    def test_format_kafka_error_response(self):
        """Test formatting error for Kafka response."""
        error = PayloadValidationError("user_id", "missing field")
        request_id = "req_123"
        correlation_id = "corr_456"
        
        kafka_response = format_kafka_error_response(
            error, request_id, correlation_id, {"test": "context"}
        )
        
        assert kafka_response["request_id"] == request_id
        assert kafka_response["correlation_id"] == correlation_id
        assert kafka_response["error_type"] == "validation_error"
        assert kafka_response["error_code"] == "invalid_payload"
        assert kafka_response["retryable"] is False
        assert kafka_response["mcp_status"] == "error"
        assert "timestamp" in kafka_response
        assert "user_id" in kafka_response["error"]
    
    def test_format_kafka_error_no_stack_trace(self):
        """Test that stack traces are not included in Kafka response."""
        error = RuntimeError("Test error")
        kafka_response = format_kafka_error_response(error, "req_123")
        
        # Convert to JSON to ensure it's serializable
        json_str = json.dumps(kafka_response)
        
        # Ensure no stack trace information is present
        assert "traceback" not in json_str.lower()
        assert "stack" not in json_str.lower()
        assert "exc_info" not in json_str.lower()
        assert kafka_response["error"] == "Unexpected error: Test error"


class TestErrorHelpers:
    """Test error creation helper functions."""
    
    def test_create_validation_error(self):
        """Test validation error creation helper."""
        error = create_validation_error("email", "invalid format", "not-an-email")
        
        assert isinstance(error, PayloadValidationError)
        assert error.field == "email"
        assert "invalid format" in error.message
        assert error.details["field"] == "email"
        assert error.details["value"] == "not-an-email"
    
    def test_create_infrastructure_error(self):
        """Test infrastructure error creation helper."""
        error = create_infrastructure_error("docker", "container_start", "daemon unavailable")
        
        assert isinstance(error, MCPExecutorError)
        assert error.error_category == ErrorCategory.INFRASTRUCTURE_ERROR
        assert "docker" in error.message
        assert "container_start" in error.message
        assert error.retryable is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
