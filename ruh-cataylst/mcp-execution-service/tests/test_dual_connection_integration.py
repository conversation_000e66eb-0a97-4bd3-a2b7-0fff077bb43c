#!/usr/bin/env python3
"""
Comprehensive Integration Test for Dual Connection Types (SSE + SSH Docker)

This script validates both SSE and SSH Docker connection types by executing
MCP tools through the complete Kafka message flow and comparing results.
"""

import asyncio
import json
import uuid
import logging
import time
import argparse
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config.config import settings
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.errors import KafkaError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


@dataclass
class ConnectionConfig:
    """Configuration for connection testing."""

    connection_type: str
    server_url: Optional[str] = None
    ssh_host: Optional[str] = None
    ssh_user: Optional[str] = None
    ssh_port: int = 22
    ssh_key_path: Optional[str] = None
    docker_image: str = "mcp-text-server"


@dataclass
class TestResult:
    """Test execution result."""

    connection_type: str
    request_id: str
    success: bool
    response_time: float
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    kafka_response: Optional[Dict[str, Any]] = None


@dataclass
class TestCase:
    """MCP tool test case definition."""

    name: str
    tool_name: str
    tool_parameters: Dict[str, Any]
    expected_fields: List[str]
    timeout_seconds: int = 30


class DualConnectionTester:
    """Main test class for dual connection validation."""

    def __init__(self, sse_config: ConnectionConfig, ssh_config: ConnectionConfig):
        self.sse_config = sse_config
        self.ssh_config = ssh_config
        self.test_results: List[TestResult] = []

    async def send_mcp_request(self, payload: Dict[str, Any]) -> str:
        """Send MCP request to Kafka topic."""
        producer = None
        try:
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            )
            await producer.start()

            request_topic = settings.kafka_consumer_topic
            await producer.send_and_wait(request_topic, value=payload)

            request_id = payload.get("request_id")
            logger.info(
                f"📤 Sent {payload.get('connection_type', 'unknown')} request: {request_id}"
            )
            return request_id

        except Exception as e:
            logger.error(f"❌ Failed to send request: {e}")
            raise
        finally:
            if producer:
                await producer.stop()

    async def consume_response(
        self, request_id: str, timeout_seconds: int = 30
    ) -> Optional[Dict[str, Any]]:
        """Consume response from Kafka results topic."""
        consumer = None
        try:
            consumer = AIOKafkaConsumer(
                settings.kafka_results_topic,
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=f"dual-test-consumer-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await consumer.start()

            start_time = time.time()
            while time.time() - start_time < timeout_seconds:
                try:
                    messages = await consumer.getmany(timeout_ms=1000)

                    for tp, msgs in messages.items():
                        for msg in msgs:
                            try:
                                response = json.loads(msg.value.decode("utf-8"))
                                if response.get("request_id") == request_id:
                                    logger.info(
                                        f"📥 Received response for: {request_id}"
                                    )
                                    return response
                            except json.JSONDecodeError:
                                continue

                except Exception as e:
                    logger.warning(f"Error consuming messages: {e}")

                await asyncio.sleep(0.1)

            logger.warning(f"⏰ Timeout waiting for response: {request_id}")
            return None

        finally:
            if consumer:
                await consumer.stop()

    def create_test_payload(
        self, config: ConnectionConfig, test_case: TestCase
    ) -> Dict[str, Any]:
        """Create test payload for the given connection configuration."""
        request_id = str(uuid.uuid4())

        payload = {
            "request_id": request_id,
            "tool_name": test_case.tool_name,
            "tool_parameters": test_case.tool_parameters,
            "retries": 2,
            "connection_type": config.connection_type,  # For logging purposes
        }

        if config.connection_type == "SSE":
            payload["server_script_path"] = config.server_url
        elif config.connection_type == "SSH_DOCKER":
            payload["ssh_host"] = config.ssh_host
            payload["ssh_user"] = config.ssh_user
            payload["ssh_port"] = config.ssh_port
            if config.ssh_key_path:
                payload["ssh_key_path"] = config.ssh_key_path
            payload["docker_image"] = config.docker_image

        return payload

    async def execute_test_case(
        self, config: ConnectionConfig, test_case: TestCase
    ) -> TestResult:
        """Execute a single test case for the given connection type."""
        logger.info(f"🧪 Testing {config.connection_type}: {test_case.name}")

        start_time = time.time()

        try:
            # Create and send request
            payload = self.create_test_payload(config, test_case)
            request_id = payload["request_id"]

            await self.send_mcp_request(payload)

            # Wait for response
            response = await self.consume_response(
                request_id, test_case.timeout_seconds
            )

            end_time = time.time()
            response_time = end_time - start_time

            if response:
                # Validate response
                success = self._validate_response(response, test_case)

                return TestResult(
                    connection_type=config.connection_type,
                    request_id=request_id,
                    success=success,
                    response_time=response_time,
                    result_data=response.get("result"),
                    kafka_response=response,
                )
            else:
                return TestResult(
                    connection_type=config.connection_type,
                    request_id=request_id,
                    success=False,
                    response_time=response_time,
                    error_message="No response received within timeout",
                )

        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time

            return TestResult(
                connection_type=config.connection_type,
                request_id=str(uuid.uuid4()),
                success=False,
                response_time=response_time,
                error_message=str(e),
            )

    def _validate_response(self, response: Dict[str, Any], test_case: TestCase) -> bool:
        """Validate response structure and content."""
        try:
            # Check basic response structure
            if "mcp_status" not in response:
                logger.error("❌ Missing 'mcp_status' in response")
                return False

            status = response.get("mcp_status")
            if status == "error":
                logger.warning(
                    f"⚠️ MCP returned error: {response.get('error', 'Unknown error')}"
                )
                return False

            if status != "success":
                logger.error(f"❌ Invalid mcp_status: {status}")
                return False

            # Check for result data
            if "result" not in response:
                logger.error("❌ Missing 'result' in successful response")
                return False

            # Validate expected fields if specified
            result_data = response.get("result")
            if (
                test_case.expected_fields
                and isinstance(result_data, list)
                and result_data
            ):
                first_result = (
                    result_data[0] if isinstance(result_data[0], dict) else {}
                )
                for field in test_case.expected_fields:
                    if field not in first_result:
                        logger.warning(
                            f"⚠️ Expected field '{field}' not found in result"
                        )

            logger.info("✅ Response validation passed")
            return True

        except Exception as e:
            logger.error(f"❌ Response validation failed: {e}")
            return False

    async def run_comparative_test(
        self, test_case: TestCase
    ) -> Tuple[TestResult, TestResult]:
        """Run the same test case on both connection types."""
        logger.info(f"\n{'='*60}")
        logger.info(f"🔄 COMPARATIVE TEST: {test_case.name}")
        logger.info(f"{'='*60}")

        # Execute on both connection types
        sse_result = await self.execute_test_case(self.sse_config, test_case)
        ssh_result = await self.execute_test_case(self.ssh_config, test_case)

        # Store results
        self.test_results.extend([sse_result, ssh_result])

        # Compare results
        self._compare_results(sse_result, ssh_result, test_case.name)

        return sse_result, ssh_result

    def _compare_results(
        self, sse_result: TestResult, ssh_result: TestResult, test_name: str
    ):
        """Compare results from both connection types."""
        logger.info(f"\n📊 COMPARISON RESULTS for {test_name}:")
        logger.info(f"{'─'*50}")

        # Success comparison
        logger.info(
            f"SSE Success:        {'✅' if sse_result.success else '❌'} ({sse_result.success})"
        )
        logger.info(
            f"SSH Docker Success: {'✅' if ssh_result.success else '❌'} ({ssh_result.success})"
        )

        # Performance comparison
        logger.info(f"SSE Response Time:        {sse_result.response_time:.2f}s")
        logger.info(f"SSH Docker Response Time: {ssh_result.response_time:.2f}s")

        if sse_result.response_time > 0 and ssh_result.response_time > 0:
            ratio = ssh_result.response_time / sse_result.response_time
            if ratio > 1.2:
                logger.info(f"⚠️ SSH Docker is {ratio:.1f}x slower than SSE")
            elif ratio < 0.8:
                logger.info(f"⚡ SSH Docker is {1/ratio:.1f}x faster than SSE")
            else:
                logger.info("⚖️ Response times are comparable")

        # Error comparison
        if sse_result.error_message:
            logger.error(f"SSE Error: {sse_result.error_message}")
        if ssh_result.error_message:
            logger.error(f"SSH Docker Error: {ssh_result.error_message}")

        # Functional equivalence check
        if sse_result.success and ssh_result.success:
            equivalent = self._check_functional_equivalence(sse_result, ssh_result)
            logger.info(f"Functional Equivalence: {'✅' if equivalent else '❌'}")

        logger.info(f"{'─'*50}")

    def _check_functional_equivalence(
        self, sse_result: TestResult, ssh_result: TestResult
    ) -> bool:
        """Check if both results are functionally equivalent."""
        try:
            # Compare result structure
            sse_data = sse_result.result_data
            ssh_data = ssh_result.result_data

            if type(sse_data) != type(ssh_data):
                logger.warning("⚠️ Result data types differ")
                return False

            # For list results, compare lengths
            if isinstance(sse_data, list) and isinstance(ssh_data, list):
                if len(sse_data) != len(ssh_data):
                    logger.warning(
                        f"⚠️ Result list lengths differ: SSE={len(sse_data)}, SSH={len(ssh_data)}"
                    )
                    return False

            # Basic equivalence check (could be enhanced for specific tool types)
            return True

        except Exception as e:
            logger.warning(f"⚠️ Could not compare functional equivalence: {e}")
            return False

    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate comprehensive test summary report."""
        sse_results = [r for r in self.test_results if r.connection_type == "SSE"]
        ssh_results = [
            r for r in self.test_results if r.connection_type == "SSH_DOCKER"
        ]

        sse_success_rate = (
            sum(1 for r in sse_results if r.success) / len(sse_results)
            if sse_results
            else 0
        )
        ssh_success_rate = (
            sum(1 for r in ssh_results if r.success) / len(ssh_results)
            if ssh_results
            else 0
        )

        sse_avg_time = (
            sum(r.response_time for r in sse_results) / len(sse_results)
            if sse_results
            else 0
        )
        ssh_avg_time = (
            sum(r.response_time for r in ssh_results) / len(ssh_results)
            if ssh_results
            else 0
        )

        return {
            "test_summary": {
                "total_tests": len(self.test_results),
                "sse_tests": len(sse_results),
                "ssh_tests": len(ssh_results),
                "timestamp": datetime.now().isoformat(),
            },
            "success_rates": {
                "sse_success_rate": sse_success_rate,
                "ssh_success_rate": ssh_success_rate,
                "overall_success": (sse_success_rate + ssh_success_rate) / 2,
            },
            "performance": {
                "sse_avg_response_time": sse_avg_time,
                "ssh_avg_response_time": ssh_avg_time,
                "performance_ratio": (
                    ssh_avg_time / sse_avg_time if sse_avg_time > 0 else 0
                ),
            },
            "detailed_results": [asdict(result) for result in self.test_results],
        }

    def print_final_summary(self):
        """Print comprehensive final summary."""
        report = self.generate_summary_report()

        logger.info(f"\n{'='*80}")
        logger.info("🏁 DUAL CONNECTION INTEGRATION TEST SUMMARY")
        logger.info(f"{'='*80}")

        # Test counts
        summary = report["test_summary"]
        logger.info(f"Total Tests Executed: {summary['total_tests']}")
        logger.info(f"SSE Tests: {summary['sse_tests']}")
        logger.info(f"SSH Docker Tests: {summary['ssh_tests']}")

        # Success rates
        success = report["success_rates"]
        logger.info(f"\n📈 SUCCESS RATES:")
        logger.info(f"SSE Success Rate:        {success['sse_success_rate']:.1%}")
        logger.info(f"SSH Docker Success Rate: {success['ssh_success_rate']:.1%}")
        logger.info(f"Overall Success Rate:    {success['overall_success']:.1%}")

        # Performance
        perf = report["performance"]
        logger.info(f"\n⚡ PERFORMANCE METRICS:")
        logger.info(
            f"SSE Avg Response Time:        {perf['sse_avg_response_time']:.2f}s"
        )
        logger.info(
            f"SSH Docker Avg Response Time: {perf['ssh_avg_response_time']:.2f}s"
        )
        if perf["performance_ratio"] > 0:
            logger.info(
                f"SSH/SSE Performance Ratio:    {perf['performance_ratio']:.2f}x"
            )

        # Final verdict
        logger.info(f"\n🎯 FINAL VERDICT:")
        if success["overall_success"] >= 0.8:
            logger.info("✅ DUAL CONNECTION INTEGRATION TEST PASSED!")
            logger.info("Both SSE and SSH Docker connections are working correctly.")
        else:
            logger.error("❌ DUAL CONNECTION INTEGRATION TEST FAILED!")
            logger.error("One or both connection types have significant issues.")

        logger.info(f"{'='*80}")


# Test case definitions
DEFAULT_TEST_CASES = [
    TestCase(
        name="Basic Echo Test",
        tool_name="echo_tool",
        tool_parameters={"message": "Hello from dual connection test!"},
        expected_fields=["response", "message"],
        timeout_seconds=20,
    ),
    TestCase(
        name="Text Processing Test",
        tool_name="text_processor",
        tool_parameters={
            "text": "This is a test string for processing",
            "operation": "uppercase",
        },
        expected_fields=["processed_text", "operation"],
        timeout_seconds=25,
    ),
    TestCase(
        name="JSON Data Test",
        tool_name="json_processor",
        tool_parameters={
            "data": {"key1": "value1", "key2": "value2"},
            "transform": "keys_to_uppercase",
        },
        expected_fields=["transformed_data"],
        timeout_seconds=30,
    ),
]


async def run_dual_connection_tests(
    sse_url: str,
    ssh_host: str,
    ssh_user: str,
    ssh_port: int = 22,
    ssh_key_path: Optional[str] = None,
    docker_image: str = "mcp-text-server",
    test_cases: Optional[List[TestCase]] = None,
    output_file: Optional[str] = None,
) -> bool:
    """Run comprehensive dual connection integration tests."""

    logger.info("🚀 Starting Dual Connection Integration Tests")
    logger.info(f"SSE URL: {sse_url}")
    logger.info(f"SSH Target: {ssh_user}@{ssh_host}:{ssh_port}")
    logger.info(f"Docker Image: {docker_image}")

    # Create connection configurations
    sse_config = ConnectionConfig(connection_type="SSE", server_url=sse_url)

    ssh_config = ConnectionConfig(
        connection_type="SSH_DOCKER",
        ssh_host=ssh_host,
        ssh_user=ssh_user,
        ssh_port=ssh_port,
        ssh_key_path=ssh_key_path,
        docker_image=docker_image,
    )

    # Initialize tester
    tester = DualConnectionTester(sse_config, ssh_config)

    # Use default test cases if none provided
    if test_cases is None:
        test_cases = DEFAULT_TEST_CASES

    try:
        # Run all test cases
        for test_case in test_cases:
            await tester.run_comparative_test(test_case)
            await asyncio.sleep(1)  # Brief pause between tests

        # Generate and display summary
        tester.print_final_summary()

        # Save detailed report if requested
        if output_file:
            report = tester.generate_summary_report()
            with open(output_file, "w") as f:
                json.dump(report, f, indent=2)
            logger.info(f"📄 Detailed report saved to: {output_file}")

        # Determine overall success
        report = tester.generate_summary_report()
        overall_success = report["success_rates"]["overall_success"]

        return overall_success >= 0.8

    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}", exc_info=True)
        return False


def create_cli_parser() -> argparse.ArgumentParser:
    """Create command-line interface parser."""
    parser = argparse.ArgumentParser(
        description="Dual Connection Integration Test for MCP Execution Service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic test with default settings
  python test_dual_connection_integration.py \\
    --sse-url http://localhost:8080/sse \\
    --ssh-host server.example.com \\
    --ssh-user ubuntu

  # Test with SSH key authentication
  python test_dual_connection_integration.py \\
    --sse-url https://api.example.com/sse \\
    --ssh-host ************* \\
    --ssh-user deploy \\
    --ssh-key ~/.ssh/id_rsa \\
    --ssh-port 2222

  # Test with custom Docker image and output file
  python test_dual_connection_integration.py \\
    --sse-url http://localhost:8080/sse \\
    --ssh-host server.com \\
    --ssh-user testuser \\
    --docker-image custom-mcp-server \\
    --output results.json

  # Quick test mode (fewer test cases)
  python test_dual_connection_integration.py \\
    --sse-url http://localhost:8080/sse \\
    --ssh-host server.com \\
    --ssh-user ubuntu \\
    --quick

  # Verbose logging
  python test_dual_connection_integration.py \\
    --sse-url http://localhost:8080/sse \\
    --ssh-host server.com \\
    --ssh-user ubuntu \\
    --verbose
        """,
    )

    # Required arguments
    parser.add_argument(
        "--sse-url",
        required=True,
        help="SSE server URL (e.g., http://localhost:8080/sse)",
    )
    parser.add_argument(
        "--ssh-host", required=True, help="SSH server hostname or IP address"
    )
    parser.add_argument("--ssh-user", required=True, help="SSH username")

    # Optional SSH arguments
    parser.add_argument(
        "--ssh-port", type=int, default=22, help="SSH port (default: 22)"
    )
    parser.add_argument("--ssh-key", help="Path to SSH private key file")
    parser.add_argument(
        "--docker-image",
        default="mcp-text-server",
        help="Docker image name for SSH Docker connection (default: mcp-text-server)",
    )

    # Test configuration
    parser.add_argument(
        "--quick", action="store_true", help="Run quick test mode (fewer test cases)"
    )
    parser.add_argument("--output", help="Output file for detailed JSON report")
    parser.add_argument(
        "--timeout",
        type=int,
        default=30,
        help="Default timeout for test cases in seconds (default: 30)",
    )

    # Logging configuration
    parser.add_argument(
        "--verbose", action="store_true", help="Enable verbose logging (DEBUG level)"
    )
    parser.add_argument(
        "--quiet",
        action="store_true",
        help="Reduce logging output (WARNING level only)",
    )

    return parser


def configure_logging(verbose: bool = False, quiet: bool = False):
    """Configure logging based on CLI arguments."""
    if quiet:
        level = logging.WARNING
    elif verbose:
        level = logging.DEBUG
    else:
        level = logging.INFO

    # Reconfigure logging
    logging.getLogger().setLevel(level)
    for handler in logging.getLogger().handlers:
        handler.setLevel(level)


def validate_arguments(args) -> bool:
    """Validate command-line arguments."""
    # Validate SSH key path if provided
    if args.ssh_key and not Path(args.ssh_key).exists():
        logger.error(f"❌ SSH key file not found: {args.ssh_key}")
        return False

    # Validate URLs
    if not (args.sse_url.startswith("http://") or args.sse_url.startswith("https://")):
        logger.error(f"❌ Invalid SSE URL format: {args.sse_url}")
        return False

    # Validate port range
    if not (1 <= args.ssh_port <= 65535):
        logger.error(f"❌ Invalid SSH port: {args.ssh_port}")
        return False

    return True


async def main():
    """Main entry point for the dual connection integration test."""
    parser = create_cli_parser()
    args = parser.parse_args()

    # Configure logging
    configure_logging(args.verbose, args.quiet)

    # Validate arguments
    if not validate_arguments(args):
        sys.exit(1)

    logger.info("🔧 Dual Connection Integration Test")
    logger.info("=" * 60)
    logger.info("This test validates both SSE and SSH Docker connections")
    logger.info("by executing MCP tools through the complete Kafka pipeline.")
    logger.info("=" * 60)

    # Prepare test cases
    test_cases = DEFAULT_TEST_CASES
    if args.quick:
        test_cases = DEFAULT_TEST_CASES[:1]  # Only run first test case
        logger.info("🏃 Running in quick mode (limited test cases)")

    # Update timeouts if specified
    if args.timeout != 30:
        for test_case in test_cases:
            test_case.timeout_seconds = args.timeout

    try:
        # Run the tests
        success = await run_dual_connection_tests(
            sse_url=args.sse_url,
            ssh_host=args.ssh_host,
            ssh_user=args.ssh_user,
            ssh_port=args.ssh_port,
            ssh_key_path=args.ssh_key,
            docker_image=args.docker_image,
            test_cases=test_cases,
            output_file=args.output,
        )

        # Exit with appropriate code
        exit_code = 0 if success else 1
        logger.info(f"🏁 Test completed with exit code: {exit_code}")
        sys.exit(exit_code)

    except KeyboardInterrupt:
        logger.info("⏹️ Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
