#!/usr/bin/env python3
"""
Test script to verify SSH environment variable functionality in MCPClient.

This script tests that the MCPClient correctly reads SSH connection parameters
from environment variables and handles various scenarios.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core_.client import MCPClient


def test_ssh_env_vars():
    """Test SSH environment variable functionality."""
    print("🧪 Testing SSH Environment Variable Functionality")
    print("=" * 60)
    
    # Save original environment variables
    original_env = {}
    ssh_env_vars = ["SSH_HOST", "SSH_USER", "SSH_PORT", "SSH_KEY_PATH"]
    for var in ssh_env_vars:
        original_env[var] = os.environ.get(var)
    
    try:
        # Test 1: No environment variables, no constructor parameters
        print("\n📋 Test 1: No SSH parameters provided")
        try:
            client = MCPClient()
            print("❌ Expected ValueError but client was created")
        except ValueError as e:
            print(f"✅ Correctly raised ValueError: {e}")
        
        # Test 2: Environment variables only
        print("\n📋 Test 2: SSH parameters from environment variables")
        os.environ["SSH_HOST"] = "test-server.com"
        os.environ["SSH_USER"] = "test-user"
        os.environ["SSH_PORT"] = "2222"
        os.environ["SSH_KEY_PATH"] = "/path/to/test/key"
        
        try:
            client = MCPClient()
            print("✅ Client created successfully with environment variables")
            print(f"   SSH Host: {client.ssh_host}")
            print(f"   SSH User: {client.ssh_user}")
            print(f"   SSH Port: {client.ssh_port}")
            print(f"   SSH Key Path: {client.ssh_key_path}")
            print(f"   Connection Type: {client.connection_type}")
            
            # Verify values
            assert client.ssh_host == "test-server.com"
            assert client.ssh_user == "test-user"
            assert client.ssh_port == 2222
            assert client.ssh_key_path == "/path/to/test/key"
            assert client.connection_type == "ssh_docker"
            print("✅ All environment variable values correct")
            
        except Exception as e:
            print(f"❌ Failed to create client with env vars: {e}")
        
        # Test 3: Constructor parameters override environment variables
        print("\n📋 Test 3: Constructor parameters override environment variables")
        try:
            client = MCPClient(
                ssh_host="override-server.com",
                ssh_user="override-user",
                ssh_port=3333,
                ssh_key_path="/path/to/override/key"
            )
            print("✅ Client created with constructor parameter overrides")
            print(f"   SSH Host: {client.ssh_host}")
            print(f"   SSH User: {client.ssh_user}")
            print(f"   SSH Port: {client.ssh_port}")
            print(f"   SSH Key Path: {client.ssh_key_path}")
            
            # Verify overrides worked
            assert client.ssh_host == "override-server.com"
            assert client.ssh_user == "override-user"
            assert client.ssh_port == 3333
            assert client.ssh_key_path == "/path/to/override/key"
            print("✅ Constructor parameters correctly override environment variables")
            
        except Exception as e:
            print(f"❌ Failed to create client with overrides: {e}")
        
        # Test 4: Partial environment variables with constructor completion
        print("\n📋 Test 4: Partial environment variables with constructor completion")
        # Clear some env vars
        del os.environ["SSH_PORT"]
        del os.environ["SSH_KEY_PATH"]
        
        try:
            client = MCPClient(ssh_port=4444)
            print("✅ Client created with partial env vars and constructor completion")
            print(f"   SSH Host: {client.ssh_host} (from env)")
            print(f"   SSH User: {client.ssh_user} (from env)")
            print(f"   SSH Port: {client.ssh_port} (from constructor)")
            print(f"   SSH Key Path: {client.ssh_key_path} (default None)")
            
            # Verify mixed sources
            assert client.ssh_host == "test-server.com"  # from env
            assert client.ssh_user == "test-user"  # from env
            assert client.ssh_port == 4444  # from constructor
            assert client.ssh_key_path is None  # default
            print("✅ Mixed environment and constructor parameters work correctly")
            
        except Exception as e:
            print(f"❌ Failed with partial env vars: {e}")
        
        # Test 5: Invalid SSH_PORT environment variable
        print("\n📋 Test 5: Invalid SSH_PORT environment variable")
        os.environ["SSH_PORT"] = "invalid-port"
        
        try:
            client = MCPClient()
            print("❌ Expected ValueError for invalid SSH_PORT but client was created")
        except ValueError as e:
            if "SSH_PORT environment variable must be an integer" in str(e):
                print(f"✅ Correctly raised ValueError for invalid SSH_PORT: {e}")
            else:
                print(f"❌ Unexpected ValueError: {e}")
        except Exception as e:
            print(f"❌ Unexpected exception: {e}")
        
        # Test 6: Default SSH_PORT when not provided
        print("\n📋 Test 6: Default SSH_PORT when not provided")
        del os.environ["SSH_PORT"]
        
        try:
            client = MCPClient()
            print("✅ Client created with default SSH_PORT")
            print(f"   SSH Port: {client.ssh_port} (should be 22)")
            
            assert client.ssh_port == 22
            print("✅ Default SSH_PORT (22) correctly applied")
            
        except Exception as e:
            print(f"❌ Failed with default SSH_PORT: {e}")
        
        # Test 7: SSE connection still works (backward compatibility)
        print("\n📋 Test 7: SSE connection backward compatibility")
        try:
            client = MCPClient(server_url="http://localhost:8080/sse")
            print("✅ SSE client created successfully")
            print(f"   Server URL: {client.server_url}")
            print(f"   Connection Type: {client.connection_type}")
            
            assert client.server_url == "http://localhost:8080/sse"
            assert client.connection_type == "sse"
            print("✅ SSE connection parameters correct")
            
        except Exception as e:
            print(f"❌ Failed to create SSE client: {e}")
        
        print("\n🎉 All tests completed!")
        
    finally:
        # Restore original environment variables
        for var in ssh_env_vars:
            if original_env[var] is not None:
                os.environ[var] = original_env[var]
            elif var in os.environ:
                del os.environ[var]


def test_environment_variable_precedence():
    """Test the precedence order: constructor > environment > defaults."""
    print("\n🔍 Testing Environment Variable Precedence")
    print("=" * 60)
    
    # Save original environment
    original_env = {
        "SSH_HOST": os.environ.get("SSH_HOST"),
        "SSH_USER": os.environ.get("SSH_USER"),
        "SSH_PORT": os.environ.get("SSH_PORT"),
        "SSH_KEY_PATH": os.environ.get("SSH_KEY_PATH"),
    }
    
    try:
        # Set environment variables
        os.environ["SSH_HOST"] = "env-host.com"
        os.environ["SSH_USER"] = "env-user"
        os.environ["SSH_PORT"] = "1111"
        os.environ["SSH_KEY_PATH"] = "/env/key/path"
        
        # Test precedence: constructor > environment > defaults
        client = MCPClient(
            ssh_host="constructor-host.com",  # Should override env
            ssh_user="constructor-user",      # Should override env
            # ssh_port not provided - should use env
            # ssh_key_path not provided - should use env
        )
        
        print("✅ Client created with mixed precedence")
        print(f"   SSH Host: {client.ssh_host} (constructor override)")
        print(f"   SSH User: {client.ssh_user} (constructor override)")
        print(f"   SSH Port: {client.ssh_port} (from environment)")
        print(f"   SSH Key Path: {client.ssh_key_path} (from environment)")
        
        # Verify precedence
        assert client.ssh_host == "constructor-host.com"  # constructor wins
        assert client.ssh_user == "constructor-user"      # constructor wins
        assert client.ssh_port == 1111                    # environment used
        assert client.ssh_key_path == "/env/key/path"     # environment used
        
        print("✅ Precedence order working correctly: constructor > environment > defaults")
        
    finally:
        # Restore original environment
        for var, value in original_env.items():
            if value is not None:
                os.environ[var] = value
            elif var in os.environ:
                del os.environ[var]


if __name__ == "__main__":
    print("🚀 SSH Environment Variable Test Suite")
    print("   Testing MCPClient SSH parameter handling")
    print("")
    
    try:
        test_ssh_env_vars()
        test_environment_variable_precedence()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("   SSH environment variable functionality is working correctly")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
