#!/usr/bin/env python3
"""
Test script for the improved MCP client with better container handling.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.core_.client import MCPClient
from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_mcp_client_with_running_container():
    """Test MCP client with a running container."""
    logger.info("=== Testing Improved MCP Client ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        logger.info("Initializing SSH key...")
        initialize_global_ssh_key(settings.ssh_key_content)
    else:
        logger.error("No SSH key content found in settings")
        return False
    
    # Test with a specific container that should be running
    # We'll use the container name as the docker_image parameter
    test_containers = [
        "ff5d4995-d431-4566-a843-59fee0521b15_91a237fd-0225-4e02-9e9f-805eff073b07",
        "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07",
    ]
    
    for container_name in test_containers:
        logger.info(f"\n--- Testing with container: {container_name} ---")
        
        try:
            # Create MCP client with the container name
            client = MCPClient(
                docker_image=container_name,
                connection_type="ssh_docker",
                container_command=None  # Let it auto-detect
            )
            
            logger.info("Attempting to connect to MCP server...")
            
            async with client:
                logger.info("✅ Successfully connected to MCP server!")
                
                # Test basic MCP operations
                logger.info("Testing list_tools...")
                tools = await client.list_tools()
                logger.info(f"Available tools: {[tool.name for tool in tools]}")
                
                logger.info("Testing list_resources...")
                resources = await client.list_resources()
                logger.info(f"Available resources: {len(resources)}")
                
                logger.info("Testing list_prompts...")
                prompts = await client.list_prompts()
                logger.info(f"Available prompts: {len(prompts)}")
                
                logger.info("✅ All MCP operations completed successfully!")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to connect with container {container_name}: {e}")
            continue
    
    logger.error("❌ Failed to connect with any available container")
    return False


async def main():
    """Main test function."""
    logger.info("Starting Improved MCP Client Test")
    
    success = await test_mcp_client_with_running_container()
    
    if success:
        logger.info("🎉 Test completed successfully!")
    else:
        logger.error("💥 Test failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
