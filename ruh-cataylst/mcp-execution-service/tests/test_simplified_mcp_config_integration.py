"""
Task 2.3: Test MCP Config Integration - Simplified Priority-Based Approach

Tests for the simplified MCP config fetching and priority-based execution selection.
Tests the new implementation in mcp_executor.py that replaced the complex execution router.
"""

import pytest
from unittest.mock import AsyncMock, patch

from app.core_.mcp_executor import MCPExecutor


class TestSimplifiedMCPConfigIntegration:
    """Test cases for simplified MCP config integration with priority-based selection."""

    @pytest.fixture
    def mock_producer(self):
        """Create a mock Kafka producer."""
        producer = AsyncMock()
        producer.send = AsyncMock()
        return producer

    @pytest.fixture
    def executor(self, mock_producer):
        """Create a test executor instance."""
        return MCPExecutor(mock_producer)

    @pytest.mark.asyncio
    async def test_fetch_mcp_config_success(self, executor):
        """Test successful MCP config fetching."""
        mock_response_data = {
            "success": True,
            "message": "MCP Config google-calendar-mcp retrieved successfully",
            "mcp": {
                "id": "c1a81b18-a294-477a-b69d-0120529b3964",
                "name": "google-calendar-mcp",
                "description": "Google Calendar related API operations",
                "config": [
                    {
                        "url": "https://google-calendar-mcp-dev-624209391722.us-central1.run.app/mcp",
                        "type": "streamable-http",
                    }
                ],
            },
        }

        # Mock the fetch_mcp_config method directly
        with patch.object(
            executor, "fetch_mcp_config", return_value=mock_response_data
        ) as mock_fetch:
            result = await executor.fetch_mcp_config(
                "c1a81b18-a294-477a-b69d-0120529b3964"
            )

            assert result == mock_response_data
            assert result["mcp"]["config"][0]["type"] == "streamable-http"
            assert (
                result["mcp"]["config"][0]["url"]
                == "https://google-calendar-mcp-dev-624209391722.us-central1.run.app/mcp"
            )
            mock_fetch.assert_called_once_with("c1a81b18-a294-477a-b69d-0120529b3964")

    @pytest.mark.asyncio
    async def test_fetch_mcp_config_404_not_found(self, executor):
        """Test MCP config not found (404 error)."""
        # Mock the fetch_mcp_config method to raise ValueError for 404
        with patch.object(
            executor,
            "fetch_mcp_config",
            side_effect=ValueError(
                "MCP configuration not found for ID: nonexistent-mcp-id"
            ),
        ):
            with pytest.raises(ValueError, match="MCP configuration not found"):
                await executor.fetch_mcp_config("nonexistent-mcp-id")

    @pytest.mark.asyncio
    async def test_fetch_mcp_config_api_timeout(self, executor):
        """Test MCP config API timeout/failure."""
        # Mock the fetch_mcp_config method to raise Exception for timeout
        with patch.object(
            executor,
            "fetch_mcp_config",
            side_effect=Exception("Error fetching MCP config for test-mcp-id: timeout"),
        ):
            with pytest.raises(Exception, match="Error fetching MCP config"):
                await executor.fetch_mcp_config("test-mcp-id")

    @pytest.mark.asyncio
    async def test_priority_selection_streamable_http_highest(self, executor):
        """Test priority-based selection: streamable-http gets highest priority."""
        mock_config = {
            "mcp": {
                "config": [
                    {"url": "https://example.com/sse", "type": "sse"},
                    {
                        "url": "https://example.com/streamable",
                        "type": "streamable-http",
                    },
                    {"type": "stdio"},
                ]
            }
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):
            with patch.object(
                executor, "_execute_url_tool", return_value=["success"]
            ) as mock_url_exec:

                result = await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

                # Should select streamable-http and use URL execution
                mock_url_exec.assert_called_once()
                _, kwargs = mock_url_exec.call_args
                assert kwargs["server_url"] == "https://example.com/streamable"
                assert result == ["success"]

    @pytest.mark.asyncio
    async def test_priority_selection_stdio_second(self, executor):
        """Test priority-based selection: stdio gets second priority."""
        mock_config = {
            "mcp": {
                "config": [
                    {"url": "https://example.com/sse", "type": "sse"},
                    {"type": "stdio"},
                ]
            }
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):
            with patch.object(
                executor, "_execute_container_tool", return_value=["container_success"]
            ) as mock_container_exec:

                result = await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

                # Should select stdio and use container execution
                mock_container_exec.assert_called_once()
                assert result == ["container_success"]

    @pytest.mark.asyncio
    async def test_priority_selection_sse_lowest(self, executor):
        """Test priority-based selection: sse gets lowest priority."""
        mock_config = {
            "mcp": {"config": [{"url": "https://example.com/sse", "type": "sse"}]}
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):
            with patch.object(
                executor, "_execute_url_tool", return_value=["sse_success"]
            ) as mock_url_exec:

                result = await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

                # Should select sse and use URL execution
                mock_url_exec.assert_called_once()
                _, kwargs = mock_url_exec.call_args
                assert kwargs["server_url"] == "https://example.com/sse"
                assert result == ["sse_success"]

    @pytest.mark.asyncio
    async def test_multiple_config_types_priority_order(self, executor):
        """Test with multiple config types - should select highest priority."""
        mock_config = {
            "mcp": {
                "config": [
                    {"url": "https://example.com/sse", "type": "sse"},
                    {"type": "stdio"},
                    {
                        "url": "https://example.com/streamable",
                        "type": "streamable-http",
                    },
                    {"url": "https://example.com/other", "type": "other"},
                ]
            }
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):
            with patch.object(
                executor, "_execute_url_tool", return_value=["streamable_success"]
            ) as mock_url_exec:

                result = await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

                # Should select streamable-http (highest priority) despite other options
                mock_url_exec.assert_called_once()
                _, kwargs = mock_url_exec.call_args
                assert kwargs["server_url"] == "https://example.com/streamable"
                assert result == ["streamable_success"]

    @pytest.mark.asyncio
    async def test_invalid_mcp_config_no_supported_types(self, executor):
        """Test with invalid MCP config - no supported config types."""
        mock_config = {
            "mcp": {
                "config": [
                    {"url": "https://example.com/unsupported", "type": "unsupported"},
                    {"type": "unknown"},
                ]
            }
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):

            with pytest.raises(ValueError, match="No supported config type found"):
                await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

    @pytest.mark.asyncio
    async def test_empty_config_array(self, executor):
        """Test with empty config array."""
        mock_config = {"mcp": {"config": []}}

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):

            with pytest.raises(ValueError, match="No supported config type found"):
                await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

    @pytest.mark.asyncio
    async def test_malformed_config_structure(self, executor):
        """Test with malformed config structure."""
        mock_config = {"mcp": {"config": "not_an_array"}}  # Should be array

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):

            with pytest.raises(Exception):  # Should fail during iteration
                await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

    @pytest.mark.asyncio
    async def test_url_fetching_after_type_decision(self, executor):
        """Test that URL is only fetched after execution type is determined."""
        mock_config = {
            "mcp": {
                "config": [
                    {"url": "https://example.com/sse", "type": "sse"},
                    {
                        "url": "https://example.com/streamable",
                        "type": "streamable-http",
                    },
                ]
            }
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):
            with patch.object(
                executor, "_execute_url_tool", return_value=["success"]
            ) as mock_url_exec:

                await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

                # Verify URL was extracted from the selected config (streamable-http)
                mock_url_exec.assert_called_once()
                _, kwargs = mock_url_exec.call_args
                assert kwargs["server_url"] == "https://example.com/streamable"
                # Should NOT use the sse URL
                assert kwargs["server_url"] != "https://example.com/sse"

    @pytest.mark.asyncio
    async def test_config_with_missing_url_field(self, executor):
        """Test config with missing URL field for URL-based types."""
        mock_config = {
            "mcp": {"config": [{"type": "streamable-http"}]}  # Missing URL field
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):

            with pytest.raises(KeyError):  # Should fail when trying to access URL
                await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

    @pytest.mark.asyncio
    async def test_stdio_config_no_url_needed(self, executor):
        """Test that stdio config doesn't need URL field."""
        mock_config = {
            "mcp": {"config": [{"type": "stdio"}]}  # No URL field needed for stdio
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):
            with patch.object(
                executor, "_execute_container_tool", return_value=["container_success"]
            ) as mock_container_exec:

                result = await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

                # Should work fine without URL field
                mock_container_exec.assert_called_once()
                assert result == ["container_success"]

    @pytest.mark.asyncio
    async def test_api_error_handling_gracefully(self, executor):
        """Test that API errors are handled gracefully."""
        # Mock the fetch_mcp_config method to raise ValueError for 500 error
        with patch.object(
            executor,
            "fetch_mcp_config",
            side_effect=ValueError(
                "MCP config API returned status 500: Internal Server Error"
            ),
        ):
            with pytest.raises(ValueError, match="MCP config API returned status 500"):
                await executor.fetch_mcp_config("test-mcp-id")

    @pytest.mark.asyncio
    async def test_real_google_calendar_config_structure(self, executor):
        """Test with real Google Calendar MCP config structure."""
        real_config = {
            "success": True,
            "message": "MCP Config google-calendar-mcp retrieved successfully",
            "mcp": {
                "id": "c1a81b18-a294-477a-b69d-0120529b3964",
                "name": "google-calendar-mcp",
                "logo": None,
                "description": "Google Calendar related API operations",
                "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
                "user_ids": None,
                "owner_type": "user",
                "config": [
                    {
                        "url": "https://google-calendar-mcp-dev-624209391722.us-central1.run.app/mcp",
                        "type": "streamable-http",
                    }
                ],
                "git_url": None,
                "git_branch": None,
                "deployment_status": "pending",
                "visibility": "private",
                "tags": None,
                "status": "active",
                "created_at": "2024-12-15T10:30:00Z",
            },
        }

        with patch.object(executor, "fetch_mcp_config", return_value=real_config):
            with patch.object(
                executor, "_execute_url_tool", return_value=["calendar_success"]
            ) as mock_url_exec:

                result = await executor.execute_tool(
                    mcp_id="c1a81b18-a294-477a-b69d-0120529b3964",
                    user_id="91a237fd-0225-4e02-9e9f-805eff073b07",
                    tool_name="list_calendars",
                    tool_parameters={},
                )

                # Should correctly parse real config structure
                mock_url_exec.assert_called_once()
                _, kwargs = mock_url_exec.call_args
                assert (
                    kwargs["server_url"]
                    == "https://google-calendar-mcp-dev-624209391722.us-central1.run.app/mcp"
                )
                assert result == ["calendar_success"]


class TestMCPConfigIntegrationPerformance:
    """Performance tests for MCP config integration."""

    @pytest.fixture
    def mock_producer(self):
        """Create a mock Kafka producer."""
        producer = AsyncMock()
        producer.send = AsyncMock()
        return producer

    @pytest.fixture
    def executor(self, mock_producer):
        """Create a test executor instance."""
        return MCPExecutor(mock_producer)

    @pytest.mark.asyncio
    async def test_config_fetch_performance(self, executor):
        """Test that config fetching completes within reasonable time."""
        import time

        mock_config = {
            "mcp": {
                "config": [{"url": "https://example.com", "type": "streamable-http"}]
            }
        }

        with patch.object(executor, "fetch_mcp_config", return_value=mock_config):
            with patch.object(executor, "_execute_url_tool", return_value=["success"]):

                start_time = time.time()

                await executor.execute_tool(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    tool_name="test_tool",
                    tool_parameters={},
                )

                end_time = time.time()
                execution_time = end_time - start_time

                # Should complete within 1 second (very generous for unit test)
                assert (
                    execution_time < 1.0
                ), f"Execution took {execution_time:.2f} seconds"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
