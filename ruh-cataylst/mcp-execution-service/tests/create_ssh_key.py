#!/usr/bin/env python3
"""
Create SSH key file with proper permissions.
"""

import os
import sys
import subprocess
import base64

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.config.config import settings


def create_ssh_key_with_permissions():
    """Create SSH key file with proper Windows permissions."""
    
    if not settings.ssh_key_content:
        print("❌ No SSH key content found")
        return False
    
    print(f"SSH key content length: {len(settings.ssh_key_content)} characters")
    
    # Decode SSH key content
    try:
        decoded_content = base64.b64decode(settings.ssh_key_content).decode("utf-8")
        print("✅ SSH key content decoded successfully")
    except Exception as e:
        print(f"❌ Failed to decode SSH key: {e}")
        return False
    
    # Create SSH key file
    ssh_key_path = os.path.join(os.getcwd(), "mcp_ssh_key.pem")
    
    try:
        with open(ssh_key_path, "w") as f:
            f.write(decoded_content)
        print(f"✅ SSH key file created: {ssh_key_path}")
    except Exception as e:
        print(f"❌ Failed to create SSH key file: {e}")
        return False
    
    # Set Windows permissions using icacls
    try:
        username = os.environ.get("USERNAME", "Administrator")
        print(f"Setting permissions for user: {username}")
        
        # Remove inheritance and grant full control only to current user
        cmd1 = ["icacls", ssh_key_path, "/inheritance:r"]
        result1 = subprocess.run(cmd1, capture_output=True, text=True)
        print(f"Remove inheritance result: {result1.returncode}")
        if result1.stderr:
            print(f"STDERR: {result1.stderr}")
        
        cmd2 = ["icacls", ssh_key_path, f"/grant:r", f"{username}:F"]
        result2 = subprocess.run(cmd2, capture_output=True, text=True)
        print(f"Grant permissions result: {result2.returncode}")
        if result2.stderr:
            print(f"STDERR: {result2.stderr}")
        
        # Check final permissions
        cmd3 = ["icacls", ssh_key_path]
        result3 = subprocess.run(cmd3, capture_output=True, text=True)
        print(f"Final permissions:\n{result3.stdout}")
        
        print("✅ SSH key permissions set successfully")
        
    except Exception as e:
        print(f"❌ Failed to set permissions: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = create_ssh_key_with_permissions()
    if success:
        print("✅ SSH key created successfully with proper permissions")
    else:
        print("❌ Failed to create SSH key")
