# test_sse_connection.py
"""
Comprehensive test script for validating SSE (Server-Sent Events) connection functionality
in the MCP execution service. Tests the SSE connection path through the MCPClient class
and validates end-to-end functionality through Kafka messaging.
"""

import asyncio
import json
import uuid
import logging
import time
import pytest
from typing import List, Dict, Any, Optional
from unittest.mock import AsyncMock, patch, MagicMock

from app.config.config import settings
from app.core_.client import MC<PERSON>lient
from app.core_.mcp_executor import MCPExecutor
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.errors import KafkaError

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] %(message)s",
)
logger = logging.getLogger(__name__)


class SSEConnectionTester:
    """Test class for SSE connection functionality."""

    def __init__(self):
        self.test_results = []
        self.mock_producer = AsyncMock()

    async def setup_test_environment(self):
        """Setup test environment and mock dependencies."""
        logger.info("Setting up SSE connection test environment...")

    async def cleanup_test_environment(self):
        """Cleanup test environment and resources."""
        logger.info("Cleaning up SSE connection test environment...")

    # --- Kafka Helper Functions ---

    async def send_sse_request(self, payload: dict) -> str:
        """Send a single SSE request payload to Kafka topic."""
        producer = None
        try:
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            )
            await producer.start()
            logger.info(f"Kafka producer started for SSE request.")

            request_topic = settings.kafka_consumer_topic
            logger.info(f"Sending SSE payload to topic '{request_topic}'")
            logger.debug(f"Payload: {payload}")

            await producer.send_and_wait(request_topic, value=payload)
            request_id = payload.get("request_id")
            logger.info(f"SSE request sent successfully (request_id: {request_id})")
            return request_id

        except KafkaError as e:
            logger.error(f"Kafka error sending SSE request: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"Unexpected error sending SSE request: {e}", exc_info=True)
            raise
        finally:
            if producer:
                await producer.stop()
                logger.debug("Kafka producer stopped.")

    async def consume_sse_results(
        self, request_ids: List[str], timeout_seconds: int = 30
    ) -> Dict[str, Any]:
        """Consume and validate SSE results from Kafka."""
        consumer = None
        results = {}

        try:
            consumer = AIOKafkaConsumer(
                settings.kafka_results_topic,
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=f"sse-test-consumer-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await consumer.start()
            logger.info(
                f"Started consumer for SSE results topic '{settings.kafka_results_topic}'"
            )

            received_responses = set()
            start_time = time.time()

            while time.time() - start_time < timeout_seconds and len(
                received_responses
            ) < len(request_ids):

                try:
                    messages = await consumer.getmany(timeout_ms=1000)

                    for tp, msgs in messages.items():
                        for msg in msgs:
                            try:
                                value = json.loads(msg.value.decode("utf-8"))
                                msg_request_id = value.get("request_id")

                                if msg_request_id in request_ids:
                                    logger.info(
                                        f"Received SSE result for request_id: {msg_request_id}"
                                    )
                                    logger.debug(f"SSE Result: {value}")

                                    results[msg_request_id] = value
                                    received_responses.add(msg_request_id)

                                    # Validate SSE-specific response structure
                                    self._validate_sse_response(value)

                            except json.JSONDecodeError:
                                logger.warning(
                                    f"Failed to decode SSE message: {msg.value}"
                                )
                                continue

                except Exception as e:
                    logger.error(f"Error consuming SSE messages: {e}", exc_info=True)

                await asyncio.sleep(0.1)

            if len(received_responses) < len(request_ids):
                logger.warning(
                    f"SSE test timeout. Received {len(received_responses)}/{len(request_ids)} responses."
                )

            return results

        finally:
            if consumer:
                await consumer.stop()
                logger.debug("SSE results consumer stopped.")

    def _validate_sse_response(self, response: Dict[str, Any]) -> bool:
        """Validate SSE response structure and content."""
        required_fields = ["request_id", "mcp_status"]

        for field in required_fields:
            if field not in response:
                raise ValueError(f"Missing required field '{field}' in SSE response")

        # Check for success or error status
        status = response.get("mcp_status")
        if status not in ["success", "error"]:
            raise ValueError(f"Invalid mcp_status '{status}' in SSE response")

        if status == "success" and "result" not in response:
            raise ValueError("Missing 'result' field in successful SSE response")

        if status == "error" and "error" not in response:
            raise ValueError("Missing 'error' field in error SSE response")

        logger.debug(f"SSE response validation passed for status: {status}")
        return True

    # --- Test Cases ---

    async def test_single_sse_connection(self) -> bool:
        """Test single SSE connection and tool execution."""
        logger.info("🧪 Testing single SSE connection...")

        try:
            request_id = str(uuid.uuid4())
            payload = {
                "request_id": request_id,
                "server_script_path": "http://localhost:8080/sse",  # Mock SSE server
                "tool_name": "test_tool",
                "tool_parameters": {"test_param": "test_value", "operation": "echo"},
                "retries": 2,
            }

            # Send request
            sent_id = await self.send_sse_request(payload)
            assert sent_id == request_id, "Request ID mismatch"

            # Wait for and validate response
            results = await self.consume_sse_results([request_id], timeout_seconds=15)

            if request_id in results:
                logger.info("✅ Single SSE connection test PASSED")
                return True
            else:
                logger.error(
                    "❌ Single SSE connection test FAILED - No response received"
                )
                return False

        except Exception as e:
            logger.error(
                f"❌ Single SSE connection test FAILED with error: {e}", exc_info=True
            )
            return False

    async def test_concurrent_sse_connections(self, num_requests: int = 3) -> bool:
        """Test multiple concurrent SSE connections."""
        logger.info(f"🧪 Testing {num_requests} concurrent SSE connections...")

        try:
            request_ids = []
            tasks = []

            # Create concurrent requests
            for i in range(num_requests):
                request_id = str(uuid.uuid4())
                request_ids.append(request_id)

                payload = {
                    "request_id": request_id,
                    "server_script_path": "http://localhost:8080/sse",
                    "tool_name": "concurrent_test_tool",
                    "tool_parameters": {
                        "test_id": i,
                        "operation": "concurrent_echo",
                        "data": f"test_data_{i}",
                    },
                    "retries": 2,
                }

                # Create async task for each request
                task = asyncio.create_task(self.send_sse_request(payload))
                tasks.append(task)

            # Wait for all requests to be sent
            await asyncio.gather(*tasks)
            logger.info(f"All {num_requests} SSE requests sent")

            # Wait for responses
            results = await self.consume_sse_results(request_ids, timeout_seconds=30)

            success_count = len(results)
            if success_count == num_requests:
                logger.info(
                    f"✅ Concurrent SSE connections test PASSED ({success_count}/{num_requests})"
                )
                return True
            else:
                logger.error(
                    f"❌ Concurrent SSE connections test FAILED ({success_count}/{num_requests})"
                )
                return False

        except Exception as e:
            logger.error(
                f"❌ Concurrent SSE connections test FAILED with error: {e}",
                exc_info=True,
            )
            return False

    async def test_sse_error_handling(self) -> bool:
        """Test SSE connection error handling scenarios."""
        logger.info("🧪 Testing SSE error handling...")

        try:
            # Test with invalid SSE URL
            request_id = str(uuid.uuid4())
            payload = {
                "request_id": request_id,
                "server_script_path": "http://invalid-sse-server:9999/sse",
                "tool_name": "error_test_tool",
                "tool_parameters": {"test": "error_scenario"},
                "retries": 1,  # Reduce retries for faster test
            }

            await self.send_sse_request(payload)
            results = await self.consume_sse_results([request_id], timeout_seconds=20)

            if request_id in results:
                result = results[request_id]
                if result.get("mcp_status") == "error":
                    logger.info(
                        "✅ SSE error handling test PASSED - Error properly handled"
                    )
                    return True
                else:
                    logger.error(
                        "❌ SSE error handling test FAILED - Expected error status"
                    )
                    return False
            else:
                logger.error("❌ SSE error handling test FAILED - No response received")
                return False

        except Exception as e:
            logger.error(
                f"❌ SSE error handling test FAILED with error: {e}", exc_info=True
            )
            return False

    async def test_sse_client_direct(self) -> bool:
        """Test SSE client functionality directly (unit test)."""
        logger.info("🧪 Testing SSE client directly...")

        try:
            # Test SSE client creation
            client = MCPClient(server_url="http://localhost:8080/sse")

            # Verify client configuration
            assert client.server_url == "http://localhost:8080/sse"
            assert client.ssh_host is None
            assert client.ssh_user is None

            logger.info("✅ SSE client direct test PASSED - Client created correctly")
            return True

        except Exception as e:
            logger.error(
                f"❌ SSE client direct test FAILED with error: {e}", exc_info=True
            )
            return False

    async def test_sse_vs_ssh_client_selection(self) -> bool:
        """Test that SSE client is correctly selected over SSH when server_url is provided."""
        logger.info("🧪 Testing SSE vs SSH client selection...")

        try:
            # Create client with both SSE and SSH parameters - SSE should take precedence
            client = MCPClient(
                server_url="http://localhost:8080/sse",
                ssh_host="test.example.com",
                ssh_user="testuser",
            )

            # Verify SSE takes precedence
            assert client.server_url == "http://localhost:8080/sse"
            assert client.ssh_host == "test.example.com"  # Should still be set
            assert client.ssh_user == "testuser"  # Should still be set

            logger.info("✅ SSE vs SSH client selection test PASSED")
            return True

        except Exception as e:
            logger.error(
                f"❌ SSE vs SSH client selection test FAILED with error: {e}",
                exc_info=True,
            )
            return False

    async def test_sse_connection_validation(self) -> bool:
        """Test SSE connection URL validation."""
        logger.info("🧪 Testing SSE connection validation...")

        try:
            # Test valid HTTP URL
            client1 = MCPClient(server_url="http://localhost:8080/sse")
            assert client1.server_url == "http://localhost:8080/sse"

            # Test valid HTTPS URL
            client2 = MCPClient(server_url="https://api.example.com/sse")
            assert client2.server_url == "https://api.example.com/sse"

            # Test invalid URL scheme
            try:
                MCPClient(server_url="ftp://invalid.com/sse")
                logger.error("❌ Should have raised ValueError for invalid scheme")
                return False
            except ValueError as e:
                logger.debug(f"Correctly caught invalid scheme error: {e}")

            # Test invalid URL format
            try:
                MCPClient(server_url="not-a-url")
                logger.error("❌ Should have raised ValueError for invalid URL")
                return False
            except ValueError as e:
                logger.debug(f"Correctly caught invalid URL error: {e}")

            logger.info("✅ SSE connection validation test PASSED")
            return True

        except Exception as e:
            logger.error(
                f"❌ SSE connection validation test FAILED with error: {e}",
                exc_info=True,
            )
            return False

    async def test_mcp_executor_sse_integration(self) -> bool:
        """Test MCPExecutor integration with SSE client."""
        logger.info("🧪 Testing MCPExecutor SSE integration...")

        try:
            # Create mock producer for MCPExecutor
            mock_producer = AsyncMock()

            # Create MCPExecutor instance
            executor = MCPExecutor(producer=mock_producer)

            # Test that executor can handle SSE parameters correctly
            # Note: This is a unit test, so we'll mock the actual execution
            with patch.object(executor, "_execute_tool") as mock_execute:
                mock_execute.return_value = [{"test": "result"}]

                result = await executor.execute_tool(
                    server_script_path="http://localhost:8080/sse",
                    tool_name="test_tool",
                    tool_parameters={"param": "value"},
                    retries=1,
                )

                # Verify the mock was called with correct parameters
                mock_execute.assert_called_once()
                call_args = mock_execute.call_args

                assert (
                    call_args.kwargs["server_script_path"]
                    == "http://localhost:8080/sse"
                )
                assert call_args.kwargs["tool_name"] == "test_tool"
                assert call_args.kwargs["tool_parameters"] == {"param": "value"}

                # Verify SSH parameters are None (SSE mode)
                assert call_args.kwargs["ssh_host"] is None
                assert call_args.kwargs["ssh_user"] is None

                logger.info("✅ MCPExecutor SSE integration test PASSED")
                return True

        except Exception as e:
            logger.error(
                f"❌ MCPExecutor SSE integration test FAILED with error: {e}",
                exc_info=True,
            )
            return False

    async def test_sse_retry_mechanism(self) -> bool:
        """Test SSE connection retry mechanism."""
        logger.info("🧪 Testing SSE retry mechanism...")

        try:
            request_id = str(uuid.uuid4())
            payload = {
                "request_id": request_id,
                "server_script_path": "http://localhost:8080/sse",
                "tool_name": "retry_test_tool",
                "tool_parameters": {"simulate": "retry_scenario"},
                "retries": 3,  # Test with multiple retries
            }

            # Send request that should trigger retries
            await self.send_sse_request(payload)
            results = await self.consume_sse_results([request_id], timeout_seconds=25)

            if request_id in results:
                # Check if the result indicates retry attempts were made
                result = results[request_id]
                logger.debug(f"Retry test result: {result}")

                logger.info("✅ SSE retry mechanism test PASSED")
                return True
            else:
                logger.error(
                    "❌ SSE retry mechanism test FAILED - No response received"
                )
                return False

        except Exception as e:
            logger.error(
                f"❌ SSE retry mechanism test FAILED with error: {e}", exc_info=True
            )
            return False

    async def test_sse_large_payload(self) -> bool:
        """Test SSE connection with large payload."""
        logger.info("🧪 Testing SSE with large payload...")

        try:
            request_id = str(uuid.uuid4())

            # Create a large payload to test handling
            large_data = "x" * 10000  # 10KB of data
            payload = {
                "request_id": request_id,
                "server_script_path": "http://localhost:8080/sse",
                "tool_name": "large_payload_test",
                "tool_parameters": {
                    "large_data": large_data,
                    "operation": "process_large_data",
                },
                "retries": 2,
            }

            await self.send_sse_request(payload)
            results = await self.consume_sse_results([request_id], timeout_seconds=20)

            if request_id in results:
                logger.info("✅ SSE large payload test PASSED")
                return True
            else:
                logger.error("❌ SSE large payload test FAILED - No response received")
                return False

        except Exception as e:
            logger.error(
                f"❌ SSE large payload test FAILED with error: {e}", exc_info=True
            )
            return False

    # --- Main Test Runner ---

    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all SSE connection tests."""
        logger.info("🚀 Starting comprehensive SSE connection tests...")

        await self.setup_test_environment()

        test_results = {}

        try:
            # Run individual tests
            test_results["single_sse_connection"] = (
                await self.test_single_sse_connection()
            )
            test_results["concurrent_sse_connections"] = (
                await self.test_concurrent_sse_connections(3)
            )
            test_results["sse_error_handling"] = await self.test_sse_error_handling()
            test_results["sse_client_direct"] = await self.test_sse_client_direct()
            test_results["sse_vs_ssh_selection"] = (
                await self.test_sse_vs_ssh_client_selection()
            )
            test_results["sse_connection_validation"] = (
                await self.test_sse_connection_validation()
            )
            test_results["mcp_executor_sse_integration"] = (
                await self.test_mcp_executor_sse_integration()
            )
            test_results["sse_retry_mechanism"] = await self.test_sse_retry_mechanism()
            test_results["sse_large_payload"] = await self.test_sse_large_payload()

            # Summary
            passed_tests = sum(1 for result in test_results.values() if result)
            total_tests = len(test_results)

            logger.info(f"\n{'='*60}")
            logger.info(f"SSE CONNECTION TEST SUMMARY")
            logger.info(f"{'='*60}")

            for test_name, result in test_results.items():
                status = "✅ PASSED" if result else "❌ FAILED"
                logger.info(f"{test_name}: {status}")

            logger.info(f"{'='*60}")
            logger.info(f"OVERALL: {passed_tests}/{total_tests} tests passed")

            if passed_tests == total_tests:
                logger.info("🎉 ALL SSE CONNECTION TESTS PASSED!")
            else:
                logger.error(
                    f"💥 {total_tests - passed_tests} SSE CONNECTION TESTS FAILED!"
                )

            logger.info(f"{'='*60}")

        finally:
            await self.cleanup_test_environment()

        return test_results


# --- Pytest Integration ---


@pytest.mark.asyncio
async def test_sse_functionality():
    """Pytest entry point for SSE connection tests."""
    tester = SSEConnectionTester()
    results = await tester.run_all_tests()

    # Assert all tests passed for pytest
    failed_tests = [name for name, result in results.items() if not result]
    assert not failed_tests, f"SSE tests failed: {failed_tests}"


# --- Main Entry Point ---


async def main():
    """Main entry point for running SSE connection tests."""
    tester = SSEConnectionTester()
    results = await tester.run_all_tests()

    # Exit with appropriate code
    all_passed = all(results.values())
    exit_code = 0 if all_passed else 1

    logger.info(f"Exiting with code: {exit_code}")
    return exit_code


if __name__ == "__main__":
    logger.info("Starting SSE connection validation tests...")
    exit_code = asyncio.run(main())
    exit(exit_code)
