#!/usr/bin/env python3
"""
Quick SSE validation script.
This script performs a minimal validation of SSE functionality
without requiring full Kafka setup - useful for development and debugging.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core_.client import MCPClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_sse_client_creation():
    """Test basic SSE client creation and configuration."""
    logger.info("🧪 Testing SSE client creation...")
    
    try:
        # Test valid SSE URL
        client = MCPClient(server_url="http://localhost:8080/sse")
        
        # Verify configuration
        assert client.server_url == "http://localhost:8080/sse"
        assert client.ssh_host is None
        assert client.ssh_user is None
        assert client._sse_context is None  # Should be None before connection
        assert client._stdio_context is None
        
        logger.info("✅ SSE client creation test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ SSE client creation test FAILED: {e}")
        return False


async def test_sse_url_validation():
    """Test SSE URL validation."""
    logger.info("🧪 Testing SSE URL validation...")
    
    try:
        # Test valid URLs
        valid_urls = [
            "http://localhost:8080/sse",
            "https://api.example.com/sse",
            "http://127.0.0.1:3000/events",
            "https://secure-server.com:8443/stream"
        ]
        
        for url in valid_urls:
            client = MCPClient(server_url=url)
            assert client.server_url == url
            logger.debug(f"✓ Valid URL accepted: {url}")
        
        # Test invalid URLs
        invalid_urls = [
            "ftp://invalid.com/sse",
            "ws://websocket.com/sse",
            "not-a-url",
            "file:///local/path"
        ]
        
        for url in invalid_urls:
            try:
                MCPClient(server_url=url)
                logger.error(f"❌ Invalid URL should have been rejected: {url}")
                return False
            except ValueError:
                logger.debug(f"✓ Invalid URL correctly rejected: {url}")
        
        logger.info("✅ SSE URL validation test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ SSE URL validation test FAILED: {e}")
        return False


async def test_sse_vs_ssh_precedence():
    """Test that SSE takes precedence over SSH when both are provided."""
    logger.info("🧪 Testing SSE vs SSH precedence...")
    
    try:
        # Create client with both SSE and SSH parameters
        client = MCPClient(
            server_url="http://localhost:8080/sse",
            ssh_host="test.example.com",
            ssh_user="testuser",
            ssh_port=2222,
            ssh_key_path="/path/to/key",
            docker_image="test-image"
        )
        
        # Verify SSE parameters are set
        assert client.server_url == "http://localhost:8080/sse"
        
        # Verify SSH parameters are also stored (but won't be used)
        assert client.ssh_host == "test.example.com"
        assert client.ssh_user == "testuser"
        assert client.ssh_port == 2222
        assert client.ssh_key_path == "/path/to/key"
        assert client.docker_image == "test-image"
        
        logger.info("✅ SSE vs SSH precedence test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ SSE vs SSH precedence test FAILED: {e}")
        return False


async def test_ssh_only_client():
    """Test SSH-only client creation (should work when server_url is None)."""
    logger.info("🧪 Testing SSH-only client creation...")
    
    try:
        # Create SSH-only client
        client = MCPClient(
            ssh_host="test.example.com",
            ssh_user="testuser",
            ssh_port=22,
            ssh_key_path="/path/to/key",
            docker_image="mcp-server"
        )
        
        # Verify SSH configuration
        assert client.server_url is None
        assert client.ssh_host == "test.example.com"
        assert client.ssh_user == "testuser"
        assert client.ssh_port == 22
        assert client.ssh_key_path == "/path/to/key"
        assert client.docker_image == "mcp-server"
        
        logger.info("✅ SSH-only client creation test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ SSH-only client creation test FAILED: {e}")
        return False


async def test_invalid_client_creation():
    """Test that invalid client creation raises appropriate errors."""
    logger.info("🧪 Testing invalid client creation...")
    
    try:
        # Test with no parameters (should fail)
        try:
            MCPClient()
            logger.error("❌ Should have raised ValueError for no parameters")
            return False
        except ValueError:
            logger.debug("✓ Correctly rejected client with no parameters")
        
        # Test with incomplete SSH parameters (should fail)
        try:
            MCPClient(ssh_host="test.com")  # Missing ssh_user
            logger.error("❌ Should have raised ValueError for incomplete SSH params")
            return False
        except ValueError:
            logger.debug("✓ Correctly rejected incomplete SSH parameters")
        
        try:
            MCPClient(ssh_user="testuser")  # Missing ssh_host
            logger.error("❌ Should have raised ValueError for incomplete SSH params")
            return False
        except ValueError:
            logger.debug("✓ Correctly rejected incomplete SSH parameters")
        
        logger.info("✅ Invalid client creation test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Invalid client creation test FAILED: {e}")
        return False


async def run_quick_validation():
    """Run all quick validation tests."""
    logger.info("🚀 Starting Quick SSE Validation")
    logger.info("=" * 50)
    
    tests = [
        ("SSE Client Creation", test_sse_client_creation),
        ("SSE URL Validation", test_sse_url_validation),
        ("SSE vs SSH Precedence", test_sse_vs_ssh_precedence),
        ("SSH-Only Client", test_ssh_only_client),
        ("Invalid Client Creation", test_invalid_client_creation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\nRunning: {test_name}")
        try:
            results[test_name] = await test_func()
        except Exception as e:
            logger.error(f"Test '{test_name}' failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("QUICK VALIDATION SUMMARY")
    logger.info("=" * 50)
    
    passed_count = 0
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if passed:
            passed_count += 1
    
    total_count = len(results)
    logger.info("=" * 50)
    logger.info(f"RESULT: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        logger.info("🎉 ALL QUICK VALIDATION TESTS PASSED!")
        logger.info("The SSE client implementation is working correctly.")
        return 0
    else:
        logger.error(f"💥 {total_count - passed_count} tests failed!")
        logger.error("There may be issues with the SSE client implementation.")
        return 1


def main():
    """Main entry point."""
    logger.info("Quick SSE Validation Script")
    logger.info("This script performs basic validation of SSE client functionality")
    logger.info("without requiring Kafka or external services.\n")
    
    try:
        exit_code = asyncio.run(run_quick_validation())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Validation interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
