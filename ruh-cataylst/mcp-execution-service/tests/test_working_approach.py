#!/usr/bin/env python3
"""
Test script to verify the working SSH MCP approach.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.core_.client import MC<PERSON>lient
from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_working_approach():
    """Test the working SSH MCP approach."""
    logger.info("=== Testing Working SSH MCP Approach ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        logger.info("Initializing SSH key...")
        initialize_global_ssh_key(settings.ssh_key_content)
    else:
        logger.error("No SSH key content found in settings")
        return False
    
    # Use the container that should work
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    try:
        # Create MCP client using the simplified approach
        client = MCPClient(
            docker_image=container_name,
            connection_type="ssh_docker",
            container_command=None,  # Let it auto-detect
            use_fallback_ssh=False,  # Use the simple approach directly
        )
        
        logger.info("Testing simplified SSH approach...")
        
        async with client:
            logger.info("✅ Successfully connected with simplified approach!")
            
            # Test basic operations
            logger.info("Testing list_tools...")
            tools = await client.list_tools()
            logger.info(f"Available tools: {[tool.name for tool in tools]}")
            
            if tools:
                # Test calling a tool
                logger.info("Testing tool call...")
                result = await client.call_tool("fetch", {
                    "url": "https://httpbin.org/json"
                })
                logger.info(f"Tool call result: {result}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Simplified approach failed: {e}")
        return False


async def test_command_detection():
    """Test the command detection logic."""
    logger.info("\n=== Testing Command Detection ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    try:
        # Create client to test command detection
        client = MCPClient(
            docker_image=container_name,
            connection_type="ssh_docker",
            container_command=None,
            use_fallback_ssh=False,
        )
        
        # Test command detection
        logger.info("Testing container command detection...")
        detected_command = await client._get_container_command()
        logger.info(f"Detected command: {detected_command}")
        
        # Test SSH command building
        logger.info("Testing SSH command building...")
        client.container_command = detected_command
        ssh_command = client._build_simple_ssh_command()
        logger.info(f"SSH command: {' '.join(ssh_command)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Command detection failed: {e}")
        return False


async def compare_approaches():
    """Compare the old complex approach vs new simple approach."""
    logger.info("\n=== Comparing Approaches ===")
    
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    try:
        client = MCPClient(
            docker_image=container_name,
            connection_type="ssh_docker",
            container_command="node dist/index.js",  # Explicit command
            use_fallback_ssh=False,
        )
        
        # Test old complex command
        old_command = client._build_ssh_command()
        logger.info(f"Old complex command: {' '.join(old_command)}")
        
        # Test new simple command
        simple_command = client._build_simple_ssh_command()
        logger.info(f"New simple command: {' '.join(simple_command)}")
        
        # Compare
        logger.info("\n📊 Comparison:")
        logger.info(f"Old command length: {len(old_command)} parts")
        logger.info(f"Simple command length: {len(simple_command)} parts")
        logger.info(f"Old has SSH options: {'-o' in old_command}")
        logger.info(f"Simple has SSH options: {'-o' in simple_command}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Comparison failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info("Starting Working Approach Tests")
    
    # Test 1: Command detection
    success1 = await test_command_detection()
    
    # Test 2: Approach comparison
    success2 = await compare_approaches()
    
    # Test 3: Full working approach
    success3 = await test_working_approach()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY:")
    logger.info("="*60)
    logger.info(f"Command detection: {'✅ PASS' if success1 else '❌ FAIL'}")
    logger.info(f"Approach comparison: {'✅ PASS' if success2 else '❌ FAIL'}")
    logger.info(f"Working approach: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success3:
        logger.info("🎉 Working approach is successful!")
        logger.info("🚀 The simplified SSH MCP client is working!")
    else:
        logger.error("💥 Working approach needs more fixes!")
        
    if success1 and success2:
        logger.info("✅ Basic components are working correctly")


if __name__ == "__main__":
    asyncio.run(main())
