#!/usr/bin/env python3
"""
Kafka Smart Routing Integration Test

Tests the complete flow:
1. Send Kafka request with mcp_id and user_id (no server_script_path)
2. Verify smart routing fetches MCP config
3. Verify correct execution method is chosen
4. Verify tool execution works end-to-end
5. Verify response is received via Kafka

Usage:
    poetry run python tests/test_kafka_smart_routing_integration.py
"""

import asyncio
import json
import logging
import sys
import time
import uuid
from pathlib import Path
from typing import Dict, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class KafkaSmartRoutingTester:
    """Test class for Kafka-based smart routing integration."""

    def __init__(self, timeout: int = 60):
        self.timeout = timeout
        self.test_results = []

    async def send_smart_routing_request(
        self,
        mcp_id: str,
        user_id: str,
        tool_name: str,
        tool_parameters: Dict[str, Any],
        retries: int = 3,
    ) -> str:
        """Send smart routing request via Kafka (no server_script_path)."""
        request_id = str(uuid.uuid4())

        # Create payload with ONLY mcp_id and user_id - no server_script_path
        payload = {
            "request_id": request_id,
            "mcp_id": mcp_id,
            "user_id": user_id,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "retries": retries,
        }

        logger.info(f"📤 Sending smart routing request")
        logger.info(f"   Request ID: {request_id}")
        logger.info(f"   MCP ID: {mcp_id}")
        logger.info(f"   User ID: {user_id}")
        logger.info(f"   Tool: {tool_name}")
        logger.info(f"   Parameters: {tool_parameters}")
        logger.info(f"   NO server_script_path - using smart routing")

        producer = None
        try:
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
                max_request_size=*********,
            )
            await producer.start()

            # Send the request
            headers = [
                ("request_id", request_id.encode("utf-8")),
                ("reply-topic", settings.kafka_results_topic.encode("utf-8")),
            ]

            await producer.send(
                settings.kafka_consumer_topic,
                value=payload,
                headers=headers,
            )

            logger.info(
                f"✅ Request sent to Kafka topic: {settings.kafka_consumer_topic}"
            )
            return request_id

        except Exception as e:
            logger.error(f"❌ Failed to send request to Kafka: {e}")
            raise
        finally:
            if producer:
                await producer.stop()

    async def wait_for_response(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Wait for response from Kafka results topic."""
        consumer = None
        try:
            consumer = AIOKafkaConsumer(
                settings.kafka_results_topic,
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=f"smart-routing-test-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
                session_timeout_ms=30000,
                max_poll_interval_ms=60000,
                value_deserializer=lambda m: json.loads(m.decode("utf-8")),
                auto_commit_interval_ms=10000,
            )
            await consumer.start()

            logger.info(f"📥 Waiting for response (timeout: {self.timeout}s)...")
            start_time = time.time()

            while time.time() - start_time < self.timeout:
                try:
                    messages = await consumer.getmany(timeout_ms=1000)

                    for tp, msgs in messages.items():
                        for msg in msgs:
                            try:
                                response = msg.value
                                if (
                                    isinstance(response, dict)
                                    and response.get("request_id") == request_id
                                ):
                                    logger.info(
                                        f"✅ Received response for request: {request_id}"
                                    )
                                    return response
                                elif isinstance(response, str):
                                    # Try to parse as JSON
                                    response_dict = json.loads(response)
                                    if response_dict.get("request_id") == request_id:
                                        logger.info(
                                            f"✅ Received response for request: {request_id}"
                                        )
                                        return response_dict
                            except (json.JSONDecodeError, AttributeError):
                                continue

                except Exception as e:
                    logger.warning(f"Error consuming messages: {e}")

                await asyncio.sleep(0.1)

            logger.warning(f"⏰ Timeout waiting for response after {self.timeout}s")
            return None

        finally:
            if consumer:
                await consumer.stop()

    async def test_smart_routing_execution(
        self, mcp_id: str, user_id: str, tool_name: str, tool_parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Test complete smart routing execution."""
        logger.info(f"🧪 Testing smart routing execution")

        start_time = time.time()

        try:
            # Send request
            request_id = await self.send_smart_routing_request(
                mcp_id=mcp_id,
                user_id=user_id,
                tool_name=tool_name,
                tool_parameters=tool_parameters,
            )

            # Wait for response
            response = await self.wait_for_response(request_id)

            end_time = time.time()
            response_time = end_time - start_time

            if response:
                success = response.get("mcp_status") == "success"

                result = {
                    "test": "smart_routing_execution",
                    "request_id": request_id,
                    "mcp_id": mcp_id,
                    "user_id": user_id,
                    "tool_name": tool_name,
                    "success": success,
                    "response_time": response_time,
                    "response": response,
                    "mcp_status": response.get("mcp_status"),
                    "result_data": response.get("result"),
                    "error": response.get("error") if not success else None,
                }

                if success:
                    logger.info(
                        f"✅ Smart routing execution successful ({response_time:.2f}s)"
                    )
                    logger.info(
                        f"   Result: {response.get('result', 'No result data')}"
                    )
                else:
                    logger.error(
                        f"❌ Smart routing execution failed: {response.get('error')}"
                    )

                return result
            else:
                result = {
                    "test": "smart_routing_execution",
                    "request_id": request_id,
                    "mcp_id": mcp_id,
                    "user_id": user_id,
                    "tool_name": tool_name,
                    "success": False,
                    "response_time": response_time,
                    "response": None,
                    "mcp_status": "timeout",
                    "result_data": None,
                    "error": "No response received within timeout",
                }

                logger.error(f"❌ No response received within {self.timeout}s")
                return result

        except Exception as e:
            result = {
                "test": "smart_routing_execution",
                "request_id": None,
                "mcp_id": mcp_id,
                "user_id": user_id,
                "tool_name": tool_name,
                "success": False,
                "response_time": time.time() - start_time,
                "response": None,
                "mcp_status": "error",
                "result_data": None,
                "error": str(e),
            }

            logger.error(f"❌ Smart routing execution failed with exception: {e}")
            return result

    async def test_legacy_vs_smart_routing(
        self,
        mcp_id: str,
        user_id: str,
        tool_name: str,
        tool_parameters: Dict[str, Any],
        server_script_path: str,
    ) -> Dict[str, Any]:
        """Test comparison between legacy and smart routing."""
        logger.info(f"🧪 Testing legacy vs smart routing comparison")

        # Test smart routing (no server_script_path)
        logger.info("Testing smart routing...")
        smart_result = await self.test_smart_routing_execution(
            mcp_id=mcp_id,
            user_id=user_id,
            tool_name=tool_name,
            tool_parameters=tool_parameters,
        )

        await asyncio.sleep(2)  # Brief pause between tests

        # Test legacy routing (with server_script_path)
        logger.info("Testing legacy routing...")
        legacy_result = await self.test_legacy_routing_execution(
            server_script_path=server_script_path,
            tool_name=tool_name,
            tool_parameters=tool_parameters,
        )

        # Compare results
        comparison = {
            "test": "legacy_vs_smart_routing",
            "smart_routing": smart_result,
            "legacy_routing": legacy_result,
            "comparison": {
                "both_successful": smart_result["success"] and legacy_result["success"],
                "smart_faster": smart_result["response_time"]
                < legacy_result["response_time"],
                "response_time_difference": abs(
                    smart_result["response_time"] - legacy_result["response_time"]
                ),
                "results_match": self._compare_results(
                    smart_result.get("result_data"), legacy_result.get("result_data")
                ),
            },
        }

        logger.info(f"📊 Comparison results:")
        logger.info(
            f"   Both successful: {comparison['comparison']['both_successful']}"
        )
        logger.info(
            f"   Smart routing faster: {comparison['comparison']['smart_faster']}"
        )
        logger.info(
            f"   Time difference: {comparison['comparison']['response_time_difference']:.2f}s"
        )

        return comparison

    async def test_legacy_routing_execution(
        self, server_script_path: str, tool_name: str, tool_parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Test legacy routing execution (with server_script_path)."""
        request_id = str(uuid.uuid4())

        payload = {
            "request_id": request_id,
            "server_script_path": server_script_path,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "retries": 3,
        }

        start_time = time.time()

        try:
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            )
            await producer.start()

            await producer.send(settings.kafka_consumer_topic, value=payload)
            await producer.stop()

            response = await self.wait_for_response(request_id)
            end_time = time.time()

            if response:
                success = response.get("mcp_status") == "success"
                return {
                    "test": "legacy_routing_execution",
                    "request_id": request_id,
                    "success": success,
                    "response_time": end_time - start_time,
                    "response": response,
                    "result_data": response.get("result"),
                    "error": response.get("error") if not success else None,
                }
            else:
                return {
                    "test": "legacy_routing_execution",
                    "request_id": request_id,
                    "success": False,
                    "response_time": end_time - start_time,
                    "response": None,
                    "result_data": None,
                    "error": "No response received",
                }

        except Exception as e:
            return {
                "test": "legacy_routing_execution",
                "request_id": request_id,
                "success": False,
                "response_time": time.time() - start_time,
                "response": None,
                "result_data": None,
                "error": str(e),
            }

    def _compare_results(self, result1: Any, result2: Any) -> bool:
        """Compare two results for similarity."""
        if result1 is None and result2 is None:
            return True
        if result1 is None or result2 is None:
            return False

        # Simple comparison - could be enhanced
        return str(result1) == str(result2)

    async def run_comprehensive_tests(
        self,
        mcp_id: str,
        user_id: str,
        tool_name: str,
        tool_parameters: Dict[str, Any],
        server_script_path: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Run comprehensive Kafka smart routing tests."""
        logger.info("🚀 Starting comprehensive Kafka smart routing tests")
        logger.info("=" * 70)

        all_results = {
            "mcp_id": mcp_id,
            "user_id": user_id,
            "tool_name": tool_name,
            "timestamp": time.time(),
            "tests": {},
        }

        # Test 1: Smart Routing Execution
        logger.info("\n⚡ Test 1: Smart Routing Execution")
        all_results["tests"]["smart_routing"] = await self.test_smart_routing_execution(
            mcp_id=mcp_id,
            user_id=user_id,
            tool_name=tool_name,
            tool_parameters=tool_parameters,
        )

        # Test 2: Legacy vs Smart Routing (if server_script_path provided)
        if server_script_path:
            logger.info("\n📊 Test 2: Legacy vs Smart Routing Comparison")
            all_results["tests"]["comparison"] = (
                await self.test_legacy_vs_smart_routing(
                    mcp_id=mcp_id,
                    user_id=user_id,
                    tool_name=tool_name,
                    tool_parameters=tool_parameters,
                    server_script_path=server_script_path,
                )
            )

        # Calculate overall success
        successful_tests = sum(
            1
            for test in all_results["tests"].values()
            if test.get("success", False)
            or ("comparison" in test and test["comparison"]["both_successful"])
        )
        total_tests = len(all_results["tests"])
        all_results["overall_success"] = successful_tests > 0
        all_results["success_rate"] = (
            successful_tests / total_tests if total_tests > 0 else 0
        )

        return all_results

    def print_test_summary(self, results: Dict[str, Any]):
        """Print comprehensive test summary."""
        print("\n" + "=" * 80)
        print("📊 KAFKA SMART ROUTING INTEGRATION TEST SUMMARY")
        print("=" * 80)

        print(f"MCP ID: {results['mcp_id']}")
        print(f"User ID: {results['user_id']}")
        print(f"Tool: {results['tool_name']}")
        print(f"Overall Success: {'✅' if results['overall_success'] else '❌'}")

        print(f"\n📋 Test Results:")
        for test_name, test_result in results["tests"].items():
            if test_name == "smart_routing":
                status = "✅" if test_result.get("success", False) else "❌"
                print(
                    f"   {status} Smart Routing: {test_result.get('error', 'Success')}"
                )
                if test_result.get("success"):
                    print(
                        f"      → Response time: {test_result.get('response_time', 0):.2f}s"
                    )
                    print(f"      → MCP status: {test_result.get('mcp_status')}")

            elif test_name == "comparison":
                comparison = test_result.get("comparison", {})
                status = "✅" if comparison.get("both_successful", False) else "❌"
                print(f"   {status} Legacy vs Smart Comparison:")
                print(
                    f"      → Both successful: {comparison.get('both_successful', False)}"
                )
                print(f"      → Smart faster: {comparison.get('smart_faster', False)}")
                print(
                    f"      → Time difference: {comparison.get('response_time_difference', 0):.2f}s"
                )

        print("=" * 80)


async def main():
    """Main test execution."""
    # Test parameters
    mcp_id = "********-f358-4595-9993-33abd9afee06"
    user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"
    tool_name = "git_directory_structure"
    tool_parameters = {
        "repo_url": "https://github.com/modelcontextprotocol/python-sdk.git"
    }
    server_script_path = "http://localhost:5001/mcp"  # Optional for comparison

    print("🧪 Kafka Smart Routing Integration Tests")
    print("   Testing end-to-end smart routing via Kafka")
    print("")

    tester = KafkaSmartRoutingTester(timeout=60)

    try:
        results = await tester.run_comprehensive_tests(
            mcp_id=mcp_id,
            user_id=user_id,
            tool_name=tool_name,
            tool_parameters=tool_parameters,
            server_script_path=server_script_path,
        )

        tester.print_test_summary(results)

        # Save results to file
        results_file = Path(__file__).parent / "kafka_smart_routing_test_results.json"
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 Test results saved to: {results_file}")

        return 0 if results["overall_success"] else 1

    except KeyboardInterrupt:
        logger.info("⏹️ Tests cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Tests cancelled")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
