#!/usr/bin/env python3
"""
Debug script for command detection.
"""

import subprocess
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_docker_inspect():
    """Test docker inspect command directly."""
    logger.info("=== Testing Docker Inspect Command ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    key_file = "E:\\RapidInnovation\\Automation Projects\\Ruh\\ruh_catalyst\\mcp-executor-service\\mcp_ssh_key.pem"
    
    # Test 1: Check if container exists
    logger.info("1. Testing container existence...")
    exists_cmd = [
        "ssh",
        "-i",
        key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker ps --filter name={container_name} --format '{{{{.Names}}}}'"
    ]
    
    try:
        result = subprocess.run(exists_cmd, capture_output=True, text=True, timeout=10)
        logger.info(f"Container existence check - Return code: {result.returncode}")
        logger.info(f"Container existence check - Stdout: {result.stdout}")
        logger.info(f"Container existence check - Stderr: {result.stderr}")
    except Exception as e:
        logger.error(f"Container existence check failed: {e}")
    
    # Test 2: Get container config
    logger.info("\n2. Testing container config...")
    config_cmd = [
        "ssh",
        "-i",
        key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker inspect {container_name} --format='{{{{json .Config}}}}'"
    ]
    
    try:
        result = subprocess.run(config_cmd, capture_output=True, text=True, timeout=10)
        logger.info(f"Config check - Return code: {result.returncode}")
        logger.info(f"Config check - Stdout: {result.stdout}")
        logger.info(f"Config check - Stderr: {result.stderr}")
    except Exception as e:
        logger.error(f"Config check failed: {e}")
    
    # Test 3: Get just Cmd
    logger.info("\n3. Testing Cmd only...")
    cmd_cmd = [
        "ssh",
        "-i",
        key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker inspect {container_name} --format='{{{{json .Config.Cmd}}}}'"
    ]
    
    try:
        result = subprocess.run(cmd_cmd, capture_output=True, text=True, timeout=10)
        logger.info(f"Cmd check - Return code: {result.returncode}")
        logger.info(f"Cmd check - Stdout: {result.stdout}")
        logger.info(f"Cmd check - Stderr: {result.stderr}")
        
        if result.returncode == 0:
            import json
            cmd_json = result.stdout.strip().strip("'\"")
            logger.info(f"Cleaned Cmd JSON: {cmd_json}")
            
            if cmd_json and cmd_json != "null":
                try:
                    cmd_list = json.loads(cmd_json)
                    logger.info(f"Parsed Cmd list: {cmd_list}")
                    if cmd_list:
                        command = " ".join(cmd_list)
                        logger.info(f"✅ Final command: {command}")
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error: {e}")
    except Exception as e:
        logger.error(f"Cmd check failed: {e}")
    
    # Test 4: Get Entrypoint + Cmd
    logger.info("\n4. Testing Entrypoint + Cmd...")
    both_cmd = [
        "ssh",
        "-i",
        key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker inspect {container_name} --format='{{{{json .Config.Entrypoint}}}} {{{{json .Config.Cmd}}}}'"
    ]
    
    try:
        result = subprocess.run(both_cmd, capture_output=True, text=True, timeout=10)
        logger.info(f"Both check - Return code: {result.returncode}")
        logger.info(f"Both check - Stdout: {result.stdout}")
        logger.info(f"Both check - Stderr: {result.stderr}")
        
        if result.returncode == 0:
            import json
            parts = result.stdout.strip().split(" ", 1)
            entrypoint_json = parts[0].strip("'\"")
            cmd_json = parts[1].strip("'\"") if len(parts) > 1 else "null"
            
            logger.info(f"Entrypoint JSON: {entrypoint_json}")
            logger.info(f"Cmd JSON: {cmd_json}")
            
            command_parts = []
            
            # Parse entrypoint
            if entrypoint_json and entrypoint_json != "null":
                try:
                    entrypoint = json.loads(entrypoint_json)
                    if entrypoint:
                        command_parts.extend(entrypoint)
                        logger.info(f"Entrypoint parts: {entrypoint}")
                except json.JSONDecodeError as e:
                    logger.error(f"Entrypoint JSON decode error: {e}")
            
            # Parse cmd
            if cmd_json and cmd_json != "null":
                try:
                    cmd = json.loads(cmd_json)
                    if cmd:
                        command_parts.extend(cmd)
                        logger.info(f"Cmd parts: {cmd}")
                except json.JSONDecodeError as e:
                    logger.error(f"Cmd JSON decode error: {e}")
            
            if command_parts:
                final_command = " ".join(command_parts)
                logger.info(f"✅ Final combined command: {final_command}")
            else:
                logger.warning("❌ No command parts found")
                
    except Exception as e:
        logger.error(f"Both check failed: {e}")


if __name__ == "__main__":
    test_docker_inspect()
