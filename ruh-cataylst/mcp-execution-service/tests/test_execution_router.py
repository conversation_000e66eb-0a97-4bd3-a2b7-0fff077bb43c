"""
Unit tests for Execution Router.
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.execution_router import ExecutionRouter, ExecutionStrategy
from app.services.mcp_config_client import MCPConfigurationResult


class TestExecutionRouter:
    """Test cases for ExecutionRouter."""
    
    @pytest.fixture
    def router(self):
        """Create a test router instance."""
        return ExecutionRouter()
    
    @pytest.mark.asyncio
    async def test_determine_execution_method_container(self, router):
        """Test execution method determination for container."""
        mock_config_result = MCPConfigurationResult(
            execution_method="container",
            config={"image_name": "test:latest", "type": "stdio"},
            mcp_config={
                "urls": [
                    {"image_name": "test:latest", "type": "stdio"},
                    {"url": "http://example.com", "type": "sse"}
                ]
            }
        )
        
        with patch.object(router.mcp_config_client, 'get_execution_config', return_value=mock_config_result):
            strategy = await router.determine_execution_method("test-mcp")
            
            assert strategy.method == "container"
            assert strategy.config["image_name"] == "test:latest"
            assert strategy.fallback_available is True
            assert strategy.fallback_config["url"] == "http://example.com"
    
    @pytest.mark.asyncio
    async def test_determine_execution_method_url(self, router):
        """Test execution method determination for URL."""
        mock_config_result = MCPConfigurationResult(
            execution_method="url",
            config={"url": "http://example.com", "type": "sse"},
            mcp_config={
                "urls": [{"url": "http://example.com", "type": "sse"}]
            }
        )
        
        with patch.object(router.mcp_config_client, 'get_execution_config', return_value=mock_config_result):
            strategy = await router.determine_execution_method("test-mcp")
            
            assert strategy.method == "url"
            assert strategy.config["url"] == "http://example.com"
            assert strategy.fallback_available is False
            assert strategy.fallback_config is None
    
    @pytest.mark.asyncio
    async def test_determine_execution_method_error(self, router):
        """Test execution method determination with error."""
        with patch.object(router.mcp_config_client, 'get_execution_config', side_effect=Exception("API Error")):
            with pytest.raises(Exception, match="Failed to determine execution method"):
                await router.determine_execution_method("test-mcp")
    
    def test_check_fallback_options_container_with_url(self, router):
        """Test fallback detection for container with URL available."""
        urls = [
            {"image_name": "test:latest", "type": "stdio"},
            {"url": "http://example.com", "type": "sse"}
        ]
        
        fallback_available, fallback_config = router._check_fallback_options(urls, "container")
        
        assert fallback_available is True
        assert fallback_config["url"] == "http://example.com"
    
    def test_check_fallback_options_container_no_url(self, router):
        """Test fallback detection for container without URL."""
        urls = [
            {"image_name": "test:latest", "type": "stdio"}
        ]
        
        fallback_available, fallback_config = router._check_fallback_options(urls, "container")
        
        assert fallback_available is False
        assert fallback_config is None
    
    def test_check_fallback_options_url_method(self, router):
        """Test fallback detection for URL method (no fallback needed)."""
        urls = [
            {"url": "http://example.com", "type": "sse"}
        ]
        
        fallback_available, fallback_config = router._check_fallback_options(urls, "url")
        
        assert fallback_available is False
        assert fallback_config is None
    
    @pytest.mark.asyncio
    async def test_get_fallback_strategy_available(self, router):
        """Test getting fallback strategy when available."""
        original_strategy = ExecutionStrategy(
            method="container",
            config={"image_name": "test:latest", "type": "stdio"},
            mcp_config={"urls": []},
            fallback_available=True,
            fallback_config={"url": "http://example.com", "type": "sse"}
        )
        
        fallback_strategy = await router.get_fallback_strategy(original_strategy)
        
        assert fallback_strategy is not None
        assert fallback_strategy.method == "url"
        assert fallback_strategy.config["url"] == "http://example.com"
        assert fallback_strategy.fallback_available is False
    
    @pytest.mark.asyncio
    async def test_get_fallback_strategy_not_available(self, router):
        """Test getting fallback strategy when not available."""
        original_strategy = ExecutionStrategy(
            method="url",
            config={"url": "http://example.com", "type": "sse"},
            mcp_config={"urls": []},
            fallback_available=False,
            fallback_config=None
        )
        
        fallback_strategy = await router.get_fallback_strategy(original_strategy)
        
        assert fallback_strategy is None
    
    def test_extract_execution_parameters_container(self, router):
        """Test parameter extraction for container execution."""
        strategy = ExecutionStrategy(
            method="container",
            config={"image_name": "test:latest", "type": "stdio"},
            mcp_config={"urls": []}
        )
        
        params = router.extract_execution_parameters(strategy)
        
        assert params["execution_method"] == "container"
        assert params["image_name"] == "test:latest"
        assert params["container_type"] == "stdio"
        assert params["server_script_path"] is None
    
    def test_extract_execution_parameters_url(self, router):
        """Test parameter extraction for URL execution."""
        strategy = ExecutionStrategy(
            method="url",
            config={"url": "http://example.com", "type": "sse"},
            mcp_config={"urls": []}
        )
        
        params = router.extract_execution_parameters(strategy)
        
        assert params["execution_method"] == "url"
        assert params["server_script_path"] == "http://example.com"
        assert params["connection_type"] == "sse"
        assert params["image_name"] is None
    
    def test_extract_execution_parameters_defaults(self, router):
        """Test parameter extraction with default values."""
        strategy = ExecutionStrategy(
            method="container",
            config={"image_name": "test:latest"},  # No type specified
            mcp_config={"urls": []}
        )
        
        params = router.extract_execution_parameters(strategy)
        
        assert params["container_type"] == "stdio"  # Default value
        
        strategy_url = ExecutionStrategy(
            method="url",
            config={"url": "http://example.com"},  # No type specified
            mcp_config={"urls": []}
        )
        
        params_url = router.extract_execution_parameters(strategy_url)
        
        assert params_url["connection_type"] == "sse"  # Default value
    
    def test_clear_cache(self, router):
        """Test cache clearing."""
        with patch.object(router.mcp_config_client, 'clear_cache') as mock_clear:
            router.clear_cache()
            mock_clear.assert_called_once()


class TestExecutionStrategy:
    """Test cases for ExecutionStrategy."""
    
    def test_execution_strategy_creation(self):
        """Test ExecutionStrategy creation."""
        strategy = ExecutionStrategy(
            method="container",
            config={"image_name": "test:latest"},
            mcp_config={"urls": []},
            fallback_available=True,
            fallback_config={"url": "http://example.com"}
        )
        
        assert strategy.method == "container"
        assert strategy.config["image_name"] == "test:latest"
        assert strategy.fallback_available is True
        assert strategy.fallback_config["url"] == "http://example.com"
    
    def test_execution_strategy_defaults(self):
        """Test ExecutionStrategy with default values."""
        strategy = ExecutionStrategy(
            method="url",
            config={"url": "http://example.com"},
            mcp_config={"urls": []}
        )
        
        assert strategy.method == "url"
        assert strategy.fallback_available is False
        assert strategy.fallback_config is None


if __name__ == "__main__":
    pytest.main([__file__])
