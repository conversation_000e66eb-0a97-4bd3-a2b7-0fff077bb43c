#!/usr/bin/env python3
"""
Test SSH connection manually to debug connection issues.
"""

import subprocess
import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key, get_global_ssh_manager


def test_ssh_connection():
    """Test SSH connection manually."""
    print("Testing SSH connection manually...")
    
    # Initialize SSH key
    if not settings.ssh_key_content:
        print("❌ No SSH key content found")
        return
    
    print("Initializing SSH key...")
    initialize_global_ssh_key(settings.ssh_key_content)
    
    manager = get_global_ssh_manager()
    ssh_key_path = manager.get_ssh_key_path()
    
    if not ssh_key_path or not os.path.exists(ssh_key_path):
        print("❌ SSH key file not found")
        return
    
    print(f"✅ SSH key file: {ssh_key_path}")
    
    # Test basic SSH connection
    print("\n1. Testing basic SSH connection...")
    basic_ssh_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        "echo 'SSH connection successful'"
    ]
    
    print(f"Command: {' '.join(basic_ssh_cmd)}")
    
    try:
        result = subprocess.run(basic_ssh_cmd, capture_output=True, text=True, timeout=30)
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Basic SSH connection successful")
        else:
            print("❌ Basic SSH connection failed")
            return
    except subprocess.TimeoutExpired:
        print("❌ SSH connection timed out")
        return
    except Exception as e:
        print(f"❌ SSH connection error: {e}")
        return
    
    # Test Docker command
    print("\n2. Testing Docker command...")
    docker_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        "docker ps"
    ]
    
    print(f"Command: {' '.join(docker_cmd)}")
    
    try:
        result = subprocess.run(docker_cmd, capture_output=True, text=True, timeout=30)
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Docker command successful")
        else:
            print("❌ Docker command failed")
    except subprocess.TimeoutExpired:
        print("❌ Docker command timed out")
    except Exception as e:
        print(f"❌ Docker command error: {e}")
    
    # Test specific container
    print("\n3. Testing specific container...")
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    container_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_id} echo 'Container test'"
    ]
    
    print(f"Command: {' '.join(container_cmd)}")
    
    try:
        result = subprocess.run(container_cmd, capture_output=True, text=True, timeout=30)
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Container command successful")
        else:
            print("❌ Container command failed")
            print("This might be why the MCP connection is failing")
    except subprocess.TimeoutExpired:
        print("❌ Container command timed out")
    except Exception as e:
        print(f"❌ Container command error: {e}")
    
    # Test MCP command
    print("\n4. Testing MCP command...")
    mcp_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_id} python -m mcp_fetch"
    ]
    
    print(f"Command: {' '.join(mcp_cmd)}")
    
    try:
        # Use Popen for interactive testing
        process = subprocess.Popen(
            mcp_cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send a simple MCP message
        test_message = '{"jsonrpc": "2.0", "method": "initialize", "id": 1, "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test-client", "version": "1.0.0"}}}\n'
        
        try:
            stdout, stderr = process.communicate(input=test_message, timeout=10)
            print(f"Return code: {process.returncode}")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            
            if process.returncode == 0:
                print("✅ MCP command executed")
            else:
                print("❌ MCP command failed")
        except subprocess.TimeoutExpired:
            process.kill()
            print("❌ MCP command timed out")
            
    except Exception as e:
        print(f"❌ MCP command error: {e}")


if __name__ == "__main__":
    test_ssh_connection()
