#!/usr/bin/env python3
"""
Simple test for direct SSH streams.
"""

import asyncio
import logging
import sys
import subprocess
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key
from app.core_.direct_ssh_streams import (
    DirectSSHReadStream,
    DirectSSHWriteStream,
    DirectMCPSession,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_simple_direct():
    """Test direct SSH streams."""
    logger.info("=== Testing Simple Direct SSH Streams ===")

    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)

    container_name = (
        "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    )
    key_file = "E:\\RapidInnovation\\Automation Projects\\Ruh\\ruh_catalyst\\mcp-executor-service\\mcp_ssh_key.pem"

    # Build SSH command
    ssh_command = [
        "ssh",
        "-i",
        key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_name} node dist/index.js",
    ]

    logger.info(f"SSH command: {' '.join(ssh_command)}")

    try:
        # Start SSH process
        process = subprocess.Popen(
            ssh_command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0,
        )

        logger.info("SSH process started")

        # Wait a moment
        await asyncio.sleep(1)

        # Check if process is running
        if process.poll() is not None:
            stderr_output = process.stderr.read() if process.stderr else ""
            logger.error(f"SSH process terminated: {stderr_output}")
            return False

        # Check for any stderr output
        if process.stderr:
            try:
                # Non-blocking read of stderr
                import select
                import os

                if hasattr(select, "select"):  # Unix-like systems
                    ready, _, _ = select.select([process.stderr], [], [], 0)
                    if ready:
                        stderr_data = process.stderr.read()
                        if stderr_data:
                            logger.warning(f"SSH stderr: {stderr_data}")
                else:
                    # Windows - just try to read without blocking
                    pass
            except:
                pass

        logger.info("SSH process is running")

        # Create streams
        read_stream = DirectSSHReadStream(process, logger)
        write_stream = DirectSSHWriteStream(process, logger)

        logger.info("Streams created")

        # Create session
        session = DirectMCPSession(read_stream, write_stream)

        logger.info("Session created")

        # Test initialization with timeout
        try:
            logger.info("Starting initialization...")
            init_task = asyncio.create_task(session.initialize())
            result = await asyncio.wait_for(init_task, timeout=10.0)
            logger.info(f"Initialization result: {result}")
            return True

        except asyncio.TimeoutError:
            logger.error("Initialization timed out")
            return False

    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback

        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

    finally:
        if "process" in locals():
            process.terminate()
            process.wait()


async def main():
    """Main test function."""
    logger.info("Starting Simple Direct SSH Test")

    success = await test_simple_direct()

    logger.info("\n" + "=" * 60)
    logger.info("SIMPLE DIRECT SSH TEST SUMMARY:")
    logger.info("=" * 60)

    if success:
        logger.info("🎉 Simple direct SSH test works!")
    else:
        logger.error("💥 Simple direct SSH test failed")


if __name__ == "__main__":
    asyncio.run(main())
