#!/usr/bin/env python3
"""
Test script to debug MCP STDIO session issues.
"""

import asyncio
import logging
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key
from mcp import StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.session import ClientSession

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_raw_stdio_connection():
    """Test raw STDIO connection to MCP server."""
    logger.info("=== Testing Raw STDIO Connection ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        logger.info("Initializing SSH key...")
        initialize_global_ssh_key(settings.ssh_key_content)
    else:
        logger.error("No SSH key content found in settings")
        return False
    
    # Use the working container
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    command = "node dist/index.js"
    
    # Build SSH command
    ssh_command = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", "E:\\RapidInnovation\\Automation Projects\\Ruh\\ruh_catalyst\\mcp-executor-service\\mcp_ssh_key.pem",
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_id} {command}"
    ]
    
    logger.info(f"SSH command: {' '.join(ssh_command)}")
    
    try:
        # Create server parameters
        server_params = StdioServerParameters(
            command=ssh_command[0],  # "ssh"
            args=ssh_command[1:],    # All SSH arguments and remote command
            env=None,
        )
        
        logger.info("Creating STDIO client...")
        stdio_context = stdio_client(server_params)
        
        logger.info("Entering STDIO context...")
        streams = await stdio_context.__aenter__()
        
        logger.info("STDIO streams established successfully")
        logger.info(f"Read stream: {streams[0]}")
        logger.info(f"Write stream: {streams[1]}")
        
        # Create session
        logger.info("Creating MCP session...")
        session = ClientSession(streams[0], streams[1])
        
        logger.info("Entering session context...")
        await session.__aenter__()
        
        logger.info("Initializing session...")
        await session.initialize()
        
        logger.info("✅ Session initialized successfully!")
        
        # Test basic operations
        logger.info("Testing list_tools...")
        tools = await session.list_tools()
        logger.info(f"Tools: {[tool.name for tool in tools]}")
        
        logger.info("Testing list_resources...")
        resources = await session.list_resources()
        logger.info(f"Resources: {len(resources)}")
        
        # Clean up
        await session.__aexit__(None, None, None)
        await stdio_context.__aexit__(None, None, None)
        
        logger.info("✅ Test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Try to clean up
        try:
            if 'session' in locals():
                await session.__aexit__(None, None, None)
            if 'stdio_context' in locals():
                await stdio_context.__aexit__(None, None, None)
        except:
            pass
        
        return False


async def test_session_timing():
    """Test if timing is an issue with session initialization."""
    logger.info("=== Testing Session Timing ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    command = "node dist/index.js"
    
    ssh_command = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", "E:\\RapidInnovation\\Automation Projects\\Ruh\\ruh_catalyst\\mcp-executor-service\\mcp_ssh_key.pem",
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_id} {command}"
    ]
    
    try:
        server_params = StdioServerParameters(
            command=ssh_command[0],
            args=ssh_command[1:],
            env=None,
        )
        
        stdio_context = stdio_client(server_params)
        streams = await stdio_context.__aenter__()
        
        logger.info("Streams established, waiting 2 seconds before session creation...")
        await asyncio.sleep(2)
        
        session = ClientSession(streams[0], streams[1])
        await session.__aenter__()
        
        logger.info("Session created, waiting 2 seconds before initialization...")
        await asyncio.sleep(2)
        
        await session.initialize()
        
        logger.info("✅ Session with timing delays successful!")
        
        # Clean up
        await session.__aexit__(None, None, None)
        await stdio_context.__aexit__(None, None, None)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Timing test failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info("Starting STDIO Session Debug Tests")
    
    # Test 1: Raw STDIO connection
    success1 = await test_raw_stdio_connection()
    
    if not success1:
        # Test 2: Try with timing delays
        logger.info("\nTrying with timing delays...")
        success2 = await test_session_timing()
        
        if success2:
            logger.info("🎉 Timing delays helped!")
        else:
            logger.error("💥 Both tests failed!")
    else:
        logger.info("🎉 Raw connection worked!")


if __name__ == "__main__":
    asyncio.run(main())
