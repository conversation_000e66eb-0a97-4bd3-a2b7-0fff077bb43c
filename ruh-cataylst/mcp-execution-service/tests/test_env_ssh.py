#!/usr/bin/env python3
"""
Test SSH key manager with actual environment variables.
"""

import logging
from app.config.config import settings
from app.services.ssh_manager import (
    initialize_global_ssh_key,
    get_global_ssh_manager,
    cleanup_global_ssh_key,
)
from app.core_.client import MCPClient

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_env_ssh_config():
    """Test SSH configuration from environment variables."""
    logger.info("🧪 Testing SSH configuration from environment variables...")

    # Check if SSH configuration is available
    logger.info(f"SSH Host: {settings.ssh_host}")
    logger.info(f"SSH User: {settings.ssh_user}")
    logger.info(f"SSH Port: {settings.ssh_port}")
    logger.info(
        f"SSH Key Content available: {'Yes' if settings.ssh_key_content else 'No'}"
    )

    if settings.ssh_key_content:
        logger.info(
            f"SSH Key Content length: {len(settings.ssh_key_content)} characters"
        )
        logger.info(f"SSH Key Content preview: {settings.ssh_key_content[:50]}...")

        # Test global SSH key initialization
        logger.info("Testing global SSH key initialization...")
        initialize_global_ssh_key(settings.ssh_key_content)

        # Get SSH key path
        manager = get_global_ssh_manager()
        key_path = manager.get_ssh_key_path()

        if key_path:
            logger.info(f"✅ Global SSH key created: {key_path}")

            # Test SSH command construction
            if settings.ssh_host and settings.ssh_user:
                logger.info("Testing SSH command construction...")
                # MCPClient reads SSH config from settings automatically
                client = MCPClient(
                    connection_type="ssh_docker",
                    docker_image="test-container",
                    container_command="echo 'test'",
                )

                ssh_command = client._build_ssh_command()
                logger.info(f"Generated SSH command: {' '.join(ssh_command)}")

                # Check if SSH key is in the command
                if key_path in " ".join(ssh_command):
                    logger.info("✅ SSH key found in command")
                else:
                    logger.error("❌ SSH key not found in command")
            else:
                logger.warning("SSH host or user not configured, skipping command test")
        else:
            logger.error("❌ Failed to create global SSH key")

        # Clean up
        cleanup_global_ssh_key()
        logger.info("✅ Test completed")
    else:
        logger.warning("No SSH key content found in environment variables")


def main():
    """Run the test."""
    logger.info("🚀 Starting Environment SSH Test...")

    try:
        test_env_ssh_config()
        logger.info("🎉 Environment SSH test completed!")
        return 0
    except Exception as e:
        logger.error(f"💥 Test failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    import sys

    exit_code = main()
    sys.exit(exit_code)
