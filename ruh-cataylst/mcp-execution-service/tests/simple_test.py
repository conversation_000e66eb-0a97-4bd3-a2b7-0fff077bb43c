#!/usr/bin/env python3
"""
Simple test for MCP smart routing components.
"""
import asyncio
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_url_parsing():
    """Test URL parsing logic."""
    from app.services.mcp_config_client import MCPConfigClient
    
    client = MCPConfigClient()
    
    # Test case 1: Image + stdio (highest priority)
    urls1 = [
        {"url": "http://example.com/sse", "type": "sse"},
        {"image_name": "my-mcp:latest", "type": "stdio"}
    ]
    
    result1 = client.parse_urls(urls1)
    print(f"Test 1 - Expected: container, Got: {result1.execution_method}")
    print(f"Config: {result1.config}")
    
    # Test case 2: URL only
    urls2 = [
        {"url": "http://example.com/sse", "type": "sse"}
    ]
    
    result2 = client.parse_urls(urls2)
    print(f"Test 2 - Expected: url, Got: {result2.execution_method}")
    print(f"Config: {result2.config}")

def test_execution_router():
    """Test execution router components."""
    from app.services.execution_router import ExecutionRouter, ExecutionStrategy
    
    router = ExecutionRouter()
    
    # Test fallback logic
    test_urls = [
        {"image_name": "my-mcp:latest", "type": "stdio"},
        {"url": "http://example.com/sse", "type": "sse"}
    ]
    
    fallback_available, fallback_config = router._check_fallback_options(test_urls, "container")
    print(f"Fallback test - Available: {fallback_available}, Config: {fallback_config}")

def main():
    """Main test function."""
    print("🚀 Running simple MCP smart routing tests...")
    
    try:
        print("\n1. Testing URL parsing...")
        test_url_parsing()
        
        print("\n2. Testing execution router...")
        test_execution_router()
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
