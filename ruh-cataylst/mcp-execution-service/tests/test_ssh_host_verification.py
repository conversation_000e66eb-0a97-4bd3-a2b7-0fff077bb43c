#!/usr/bin/env python3
"""
Test script for SSH host key verification functionality.

This script tests the new SSH host key verification setup to prevent
"Host key verification failed" errors.
"""

import asyncio
import os
import tempfile
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ssh_host_verification():
    """Test SSH host key verification setup."""
    
    print("🔧 Testing SSH Host Key Verification Setup")
    print("=" * 50)
    
    # Import the SSH manager
    try:
        from app.services.ssh_manager import GlobalSSHKeyManager, get_global_ssh_manager
        from app.config.config import settings
        print("✅ Successfully imported SSH manager")
    except ImportError as e:
        print(f"❌ Failed to import SSH manager: {e}")
        return False
    
    # Test SSH connection details
    ssh_host = getattr(settings, 'ssh_host', '************')
    ssh_user = getattr(settings, 'ssh_user', 'ubuntu')
    ssh_key_content = getattr(settings, 'ssh_key_content', None)
    
    if not ssh_key_content:
        print("❌ No SSH key content found in settings")
        return False
    
    print(f"📡 SSH Host: {ssh_host}")
    print(f"👤 SSH User: {ssh_user}")
    print(f"🔑 SSH Key: {'Present' if ssh_key_content else 'Missing'}")
    
    try:
        # Initialize global SSH manager
        manager = get_global_ssh_manager()
        
        # Initialize SSH key
        print("\n🔑 Initializing SSH key...")
        manager.initialize_ssh_key(ssh_key_content, raise_on_error=True)
        
        ssh_key_path = manager.get_ssh_key_path()
        if ssh_key_path and os.path.exists(ssh_key_path):
            print(f"✅ SSH key initialized: {ssh_key_path}")
            
            # Check file permissions
            stat_info = os.stat(ssh_key_path)
            permissions = oct(stat_info.st_mode)[-3:]
            print(f"🔒 SSH key permissions: {permissions}")
        else:
            print("❌ SSH key initialization failed")
            return False
        
        # Set SSH connection details
        print("\n📋 Setting SSH connection details...")
        manager.set_ssh_connection_details(ssh_host, ssh_user)
        print("✅ SSH connection details set")
        
        # Test host key verification setup
        print("\n🔐 Setting up host key verification...")
        await manager.setup_host_key_verification(ssh_host, ssh_user)
        print("✅ Host key verification setup completed")
        
        # Test basic SSH connection
        print("\n🌐 Testing basic SSH connection...")
        test_ssh_command = [
            "ssh",
            "-i", ssh_key_path,
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "ConnectTimeout=10",
            "-o", "IdentitiesOnly=yes",
            f"{ssh_user}@{ssh_host}",
            "echo 'SSH connection test successful'"
        ]
        
        print(f"🔧 Running: {' '.join(test_ssh_command)}")
        
        result = subprocess.run(
            test_ssh_command, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ SSH connection test successful!")
            print(f"📤 Output: {result.stdout.strip()}")
        else:
            print("❌ SSH connection test failed")
            print(f"📥 Error: {result.stderr.strip()}")
            
            # Analyze the error
            if "Host key verification failed" in result.stderr:
                print("💡 Host key verification failed - this is what we're trying to fix")
            elif "Permission denied" in result.stderr:
                print("💡 Permission denied - check SSH key and server config")
            elif "Connection refused" in result.stderr:
                print("💡 Connection refused - check if SSH server is accessible")
            
            return False
        
        # Test Docker container listing
        print("\n🐳 Testing Docker container access...")
        docker_test_command = [
            "ssh",
            "-i", ssh_key_path,
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "ConnectTimeout=10",
            "-o", "IdentitiesOnly=yes",
            f"{ssh_user}@{ssh_host}",
            "docker ps --format 'table {{.Names}}\\t{{.Status}}'"
        ]
        
        print(f"🔧 Running: {' '.join(docker_test_command)}")
        
        result = subprocess.run(
            docker_test_command, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ Docker container access successful!")
            print("📋 Running containers:")
            print(result.stdout)
        else:
            print("❌ Docker container access failed")
            print(f"📥 Error: {result.stderr.strip()}")
        
        print("\n🎉 SSH host key verification test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        logger.exception("Test failed")
        return False
    
    finally:
        # Cleanup
        try:
            if 'manager' in locals():
                manager.cleanup_ssh_key()
                print("🧹 Cleaned up SSH key")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")


async def test_convenience_functions():
    """Test the convenience functions for SSH host verification."""
    
    print("\n🔧 Testing Convenience Functions")
    print("=" * 40)
    
    try:
        from app.services.ssh_manager import (
            setup_global_ssh_host_verification_async,
            get_global_ssh_manager
        )
        from app.config.config import settings
        
        ssh_host = getattr(settings, 'ssh_host', '************')
        ssh_user = getattr(settings, 'ssh_user', 'ubuntu')
        ssh_key_content = getattr(settings, 'ssh_key_content', None)
        
        if not ssh_key_content:
            print("❌ No SSH key content found in settings")
            return False
        
        # Initialize SSH key first
        manager = get_global_ssh_manager()
        manager.initialize_ssh_key(ssh_key_content, raise_on_error=True)
        
        # Test the convenience function
        print("🔧 Testing setup_global_ssh_host_verification_async...")
        await setup_global_ssh_host_verification_async(ssh_host, ssh_user)
        print("✅ Convenience function test successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ Convenience function test failed: {e}")
        logger.exception("Convenience function test failed")
        return False


if __name__ == "__main__":
    async def main():
        print("🚀 Starting SSH Host Key Verification Tests")
        print("=" * 60)
        
        # Test 1: Basic host verification setup
        test1_result = await test_ssh_host_verification()
        
        # Test 2: Convenience functions
        test2_result = await test_convenience_functions()
        
        print("\n📊 Test Results Summary")
        print("=" * 30)
        print(f"✅ Host Verification Setup: {'PASS' if test1_result else 'FAIL'}")
        print(f"✅ Convenience Functions: {'PASS' if test2_result else 'FAIL'}")
        
        if test1_result and test2_result:
            print("\n🎉 All tests passed! SSH host key verification is working correctly.")
            return True
        else:
            print("\n❌ Some tests failed. Please check the errors above.")
            return False
    
    # Run the tests
    success = asyncio.run(main())
    exit(0 if success else 1)
