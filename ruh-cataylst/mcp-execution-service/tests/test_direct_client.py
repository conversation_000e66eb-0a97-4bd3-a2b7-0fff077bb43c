#!/usr/bin/env python3
"""
Test the updated MCP client with direct SSH approach.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.core_.client import MCPClient
from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_direct_client():
    """Test the updated MCP client with direct SSH approach."""
    logger.info("=== Testing Updated MCP Client with Direct SSH ===")

    # Initialize SSH key
    if settings.ssh_key_content:
        logger.info("Initializing SSH key...")
        initialize_global_ssh_key(settings.ssh_key_content)
    else:
        logger.error("No SSH key content found in settings")
        return False

    # Use the container that should work
    container_name = (
        "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    )

    try:
        # Create MCP client using the direct SSH approach
        client = MCPClient(
            docker_image=container_name,
            connection_type="ssh_docker",
            container_command=None,  # Let it auto-detect
            use_fallback_ssh=False,  # Use the direct approach
        )

        logger.info("Testing direct SSH MCP client...")

        async with client:
            logger.info("✅ Successfully connected with direct SSH approach!")

            # Test basic operations
            logger.info("Testing list_tools...")
            tools = await client.list_tools()
            logger.info(
                f"Available tools: {[tool['name'] if isinstance(tool, dict) else tool.name for tool in tools]}"
            )

            if tools:
                # Test calling a tool
                logger.info("Testing tool call...")
                result = await client.call_tool(
                    "fetch", {"url": "https://httpbin.org/json"}
                )
                logger.info(f"Tool call result type: {type(result)}")
                logger.info(f"Tool call result: {result}")
                logger.info(f"Tool call successful!")

            return True

    except Exception as e:
        logger.error(f"❌ Direct SSH approach failed: {e}")
        import traceback

        logger.error(f"Full traceback: {traceback.format_exc()}")
        return False


async def main():
    """Main test function."""
    logger.info("Starting Direct SSH MCP Client Test")

    success = await test_direct_client()

    logger.info("\n" + "=" * 60)
    logger.info("DIRECT SSH MCP CLIENT TEST SUMMARY:")
    logger.info("=" * 60)

    if success:
        logger.info("🎉 Direct SSH MCP client works!")
        logger.info("✅ Successfully bypassed problematic MCP SDK")
        logger.info("🚀 Your implementation now works like your peers'!")
    else:
        logger.error("💥 Direct SSH MCP client failed")
        logger.error("🔍 Need to debug further")


if __name__ == "__main__":
    asyncio.run(main())
