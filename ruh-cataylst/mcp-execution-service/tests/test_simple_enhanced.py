#!/usr/bin/env python3
"""
Simple test for the enhanced MCP client.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.core_.client import MC<PERSON>lient
from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_standard_connection():
    """Test standard MCP connection without fallback."""
    logger.info("=== Testing Standard MCP Connection ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        logger.info("Initializing SSH key...")
        initialize_global_ssh_key(settings.ssh_key_content)
    else:
        logger.error("No SSH key content found in settings")
        return False
    
    # Use a container that should work with standard STDIO
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    try:
        # Create MCP client with fallback disabled to test standard connection
        client = MCPClient(
            docker_image=container_name,
            connection_type="ssh_docker",
            container_command=None,  # Let it auto-detect
            use_fallback_ssh=False,  # Disable fallback for this test
        )
        
        logger.info("Attempting standard STDIO connection...")
        
        async with client:
            logger.info("✅ Successfully connected with standard STDIO!")
            
            # Test basic operations
            logger.info("Testing list_tools...")
            tools = await client.list_tools()
            logger.info(f"Available tools: {[tool.name for tool in tools]}")
            
            return True
            
    except Exception as e:
        logger.warning(f"Standard connection failed: {e}")
        return False


async def test_enhanced_connection():
    """Test enhanced MCP connection with auto-detection."""
    logger.info("\n=== Testing Enhanced MCP Connection ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    try:
        # Create enhanced MCP client with all features
        client = MCPClient(
            docker_image=container_name,
            connection_type="ssh_docker",
            container_command=None,  # Auto-detect
            use_fallback_ssh=True,   # Enable fallback
        )
        
        logger.info("Attempting enhanced connection...")
        
        async with client:
            logger.info("✅ Successfully connected with enhanced client!")
            
            # Check which mode was used
            if hasattr(client, '_fallback_mode') and client._fallback_mode:
                logger.info("🔄 Used fallback SSH client")
            else:
                logger.info("📡 Used standard STDIO client")
            
            # Test operations
            logger.info("Testing list_tools...")
            tools = await client.list_tools()
            logger.info(f"Available tools: {[tool.name for tool in tools]}")
            
            logger.info("Testing list_resources...")
            resources = await client.list_resources()
            logger.info(f"Available resources: {len(resources)}")
            
            return True
            
    except Exception as e:
        logger.error(f"Enhanced connection failed: {e}")
        return False


async def test_container_detection():
    """Test container command detection."""
    logger.info("\n=== Testing Container Command Detection ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    try:
        # Create client to test command detection
        client = MCPClient(
            docker_image=container_name,
            connection_type="ssh_docker",
            container_command=None,  # Force auto-detection
            use_fallback_ssh=False,  # Keep it simple
        )
        
        # Test command detection without connecting
        logger.info("Testing container command detection...")
        detected_command = await client._get_container_command()
        logger.info(f"Detected command: {detected_command}")
        
        return True
        
    except Exception as e:
        logger.error(f"Command detection failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info("Starting Simple Enhanced MCP Client Tests")
    
    # Test 1: Container command detection
    success1 = await test_container_detection()
    
    # Test 2: Standard connection
    success2 = await test_standard_connection()
    
    # Test 3: Enhanced connection
    success3 = await test_enhanced_connection()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY:")
    logger.info("="*60)
    logger.info(f"Container command detection: {'✅ PASS' if success1 else '❌ FAIL'}")
    logger.info(f"Standard connection: {'✅ PASS' if success2 else '❌ FAIL'}")
    logger.info(f"Enhanced connection: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success1 or success2 or success3:
        logger.info("🎉 At least one test passed!")
        if success2:
            logger.info("✅ Standard STDIO connection is working")
        if success3:
            logger.info("✅ Enhanced connection with fallback is working")
    else:
        logger.error("💥 All tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
