#!/usr/bin/env python3
"""
Test script for SSH key manager functionality.
Tests both the original SecureSSHKeyManager and the new GlobalSSHKeyManager.
"""

import os
import logging

from app.services.ssh_manager import (
    SecureSSH<PERSON>eyManager,
    GlobalSSHKeyManager,
    get_global_ssh_manager,
    initialize_global_ssh_key,
    cleanup_global_ssh_key,
)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Sample SSH key content (base64 encoded dummy key for testing)
SAMPLE_SSH_KEY_B64 = """LS0tLS1CRUdJTiBPUEVOU1NIIFBSSVZBVEUgS0VZLS0tLS0KYjNCbGJuTnphQzFyWlhrdGRqRUFBQUFBQkc1dmJtVUFBQUFFYm05dVpRQUFBQUFBQUFBQkFBQUJGd0FBQUFkemMyZ3RjbgpOaEFBQUFBd0VBQVFBQUFRRUF0K2dGejVZWUNxK2ZqQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCi0tLS0tRU5EIE9QRU5TU0ggUFJJVkFURSBLRVktLS0tLQo="""


def test_secure_ssh_key_manager():
    """Test the original SecureSSHKeyManager."""
    logger.info("🧪 Testing SecureSSHKeyManager...")
    
    manager = SecureSSHKeyManager()
    
    # Test with base64 encoded content
    logger.info("Testing with base64 encoded SSH key...")
    key_path = manager.get_ssh_key_path(SAMPLE_SSH_KEY_B64)
    logger.info(f"SSH key path: {key_path}")
    
    # Verify file exists and has correct permissions
    if os.path.exists(key_path):
        stat_info = os.stat(key_path)
        permissions = oct(stat_info.st_mode)[-3:]
        logger.info(f"File permissions: {permissions}")
        
        # Read and verify content
        with open(key_path, "r") as f:
            content = f.read()
            logger.info(f"File content length: {len(content)} characters")
            logger.info(f"Content starts with: {content[:50]}...")
    else:
        logger.error(f"SSH key file not found: {key_path}")
    
    # Clean up
    manager.cleanup_temp_key_file()
    logger.info("✅ SecureSSHKeyManager test completed")


def test_global_ssh_key_manager():
    """Test the new GlobalSSHKeyManager."""
    logger.info("🧪 Testing GlobalSSHKeyManager...")
    
    # Test singleton behavior
    manager1 = get_global_ssh_manager()
    manager2 = get_global_ssh_manager()
    
    if manager1 is manager2:
        logger.info("✅ Singleton pattern working correctly")
    else:
        logger.error("❌ Singleton pattern failed")
    
    # Test initialization
    logger.info("Testing SSH key initialization...")
    initialize_global_ssh_key(SAMPLE_SSH_KEY_B64)
    
    # Test getting key path
    key_path = manager1.get_ssh_key_path()
    logger.info(f"Global SSH key path: {key_path}")
    
    if key_path and os.path.exists(key_path):
        stat_info = os.stat(key_path)
        permissions = oct(stat_info.st_mode)[-3:]
        logger.info(f"File permissions: {permissions}")
        
        # Read and verify content
        with open(key_path, "r") as f:
            content = f.read()
            logger.info(f"File content length: {len(content)} characters")
            logger.info(f"Content starts with: {content[:50]}...")
    else:
        logger.error(f"Global SSH key file not found: {key_path}")
    
    # Test multiple calls to get_ssh_key_path (should return same path)
    key_path2 = manager1.get_ssh_key_path()
    if key_path == key_path2:
        logger.info("✅ Multiple calls return same path")
    else:
        logger.error("❌ Multiple calls return different paths")
    
    # Test reinitialization (should skip)
    logger.info("Testing reinitialization (should skip)...")
    initialize_global_ssh_key(SAMPLE_SSH_KEY_B64)
    
    # Clean up
    cleanup_global_ssh_key()
    logger.info("✅ GlobalSSHKeyManager test completed")


def test_ssh_command_simulation():
    """Simulate SSH command construction."""
    logger.info("🧪 Testing SSH command simulation...")
    
    # Initialize global SSH key
    initialize_global_ssh_key(SAMPLE_SSH_KEY_B64)
    
    # Get global manager
    manager = get_global_ssh_manager()
    key_path = manager.get_ssh_key_path()
    
    if key_path:
        # Simulate SSH command construction
        ssh_cmd = [
            "ssh",
            "-o",
            "StrictHostKeyChecking=no",
            "-o",
            "UserKnownHostsFile=/dev/null",
            "-o",
            "ConnectTimeout=30",
            "-o",
            "IdentitiesOnly=yes",
            "-p",
            "22",
            "-i",
            key_path,
            "<EMAIL>",
            "echo 'Hello World'",
        ]
        
        logger.info(f"Simulated SSH command: {' '.join(ssh_cmd)}")
        logger.info("✅ SSH command simulation completed")
    else:
        logger.error("❌ No SSH key path available for command simulation")
    
    # Clean up
    cleanup_global_ssh_key()


def main():
    """Run all tests."""
    logger.info("🚀 Starting SSH Manager Tests...")
    
    try:
        test_secure_ssh_key_manager()
        print()
        test_global_ssh_key_manager()
        print()
        test_ssh_command_simulation()
        
        logger.info("🎉 All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"💥 Test failed: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
