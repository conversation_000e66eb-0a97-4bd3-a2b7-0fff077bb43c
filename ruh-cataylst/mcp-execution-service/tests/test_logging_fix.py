#!/usr/bin/env python3
"""
Simple test to verify the logging fix works correctly.
"""

import sys
import os
import logging

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ssh_manager_logging():
    """Test that SSH manager logging works without keyword argument errors."""
    try:
        from app.services.ssh_manager import GlobalSSHKeyManager
        
        print("✅ SSH manager import successful")
        
        # Create manager
        manager = GlobalSSHKeyManager()
        print("✅ SSH manager created")
        
        # Test setting connection details (this should not cause logging errors)
        manager.set_ssh_connection_details("test.example.com", "testuser")
        print("✅ SSH connection details set without logging errors")
        
        # Test that the details are stored
        if manager._ssh_host == "test.example.com" and manager._ssh_user == "testuser":
            print("✅ SSH connection details stored correctly")
        else:
            print("❌ SSH connection details not stored correctly")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SSH manager logging test failed: {e}")
        return False

def test_client_logging():
    """Test that client logging works without keyword argument errors."""
    try:
        # Test the logging format that was causing issues
        logger.info("Test message with f-string formatting: <EMAIL>")
        print("✅ Client logging test successful")
        return True
        
    except Exception as e:
        print(f"❌ Client logging test failed: {e}")
        return False

def main():
    """Run logging fix verification tests."""
    print("🔧 Testing SSH Host Key Verification Logging Fix")
    print("=" * 55)
    
    tests = [
        ("SSH Manager Logging", test_ssh_manager_logging),
        ("Client Logging", test_client_logging),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n📊 Test Results")
    print("=" * 20)
    
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 55)
    if all_passed:
        print("🎉 All logging tests passed!")
        print("💡 The logging fix should resolve the 'Logger._log() got an unexpected keyword argument' error.")
    else:
        print("❌ Some logging tests failed.")
        print("🔧 Please check the implementation and fix any issues.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
