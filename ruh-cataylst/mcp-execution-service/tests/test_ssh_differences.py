#!/usr/bin/env python3
"""
Test SSH differences between platforms.
"""

import subprocess
import logging
import sys
import asyncio
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_direct_ssh_command():
    """Test the SSH command directly without MCP SDK."""
    logger.info("=== Testing Direct SSH Command ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    key_file = "E:\\RapidInnovation\\Automation Projects\\Ruh\\ruh_catalyst\\mcp-executor-service\\mcp_ssh_key.pem"
    
    # Test 1: Simple command execution
    logger.info("1. Testing simple command execution...")
    simple_cmd = [
        "ssh",
        "-i", key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec {container_name} echo 'Hello from container'"
    ]
    
    try:
        result = subprocess.run(simple_cmd, capture_output=True, text=True, timeout=10)
        logger.info(f"Simple command - Return code: {result.returncode}")
        logger.info(f"Simple command - Stdout: {result.stdout}")
        logger.info(f"Simple command - Stderr: {result.stderr}")
    except Exception as e:
        logger.error(f"Simple command failed: {e}")
    
    # Test 2: Interactive command execution
    logger.info("\n2. Testing interactive command execution...")
    interactive_cmd = [
        "ssh",
        "-i", key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_name} node dist/index.js"
    ]
    
    try:
        # Start the process
        process = subprocess.Popen(
            interactive_cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send a simple MCP initialize message
        init_message = '''{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"roots": {"listChanged": true}, "sampling": {}}, "clientInfo": {"name": "test-client", "version": "1.0.0"}}}
'''
        
        logger.info("Sending MCP initialize message...")
        process.stdin.write(init_message)
        process.stdin.flush()
        
        # Wait for response with timeout
        try:
            stdout, stderr = process.communicate(timeout=5)
            logger.info(f"Interactive command - Return code: {process.returncode}")
            logger.info(f"Interactive command - Stdout: {stdout}")
            logger.info(f"Interactive command - Stderr: {stderr}")
        except subprocess.TimeoutExpired:
            logger.warning("Interactive command timed out")
            process.kill()
            stdout, stderr = process.communicate()
            logger.info(f"After kill - Stdout: {stdout}")
            logger.info(f"After kill - Stderr: {stderr}")
            
    except Exception as e:
        logger.error(f"Interactive command failed: {e}")
    
    # Test 3: Test with different SSH options (like your peers might use)
    logger.info("\n3. Testing with minimal SSH options...")
    minimal_cmd = [
        "ssh",
        "-i", key_file,
        "-o", "StrictHostKeyChecking=no",
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_name} node dist/index.js"
    ]
    
    try:
        process = subprocess.Popen(
            minimal_cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send MCP message
        init_message = '''{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"roots": {"listChanged": true}, "sampling": {}}, "clientInfo": {"name": "test-client", "version": "1.0.0"}}}
'''
        
        logger.info("Sending MCP message with minimal SSH options...")
        process.stdin.write(init_message)
        process.stdin.flush()
        
        try:
            stdout, stderr = process.communicate(timeout=5)
            logger.info(f"Minimal SSH - Return code: {process.returncode}")
            logger.info(f"Minimal SSH - Stdout: {stdout}")
            logger.info(f"Minimal SSH - Stderr: {stderr}")
        except subprocess.TimeoutExpired:
            logger.warning("Minimal SSH command timed out")
            process.kill()
            stdout, stderr = process.communicate()
            logger.info(f"After kill - Stdout: {stdout}")
            logger.info(f"After kill - Stderr: {stderr}")
            
    except Exception as e:
        logger.error(f"Minimal SSH command failed: {e}")


def test_ssh_client_version():
    """Test SSH client version and capabilities."""
    logger.info("\n=== Testing SSH Client Version ===")
    
    try:
        # Get SSH version
        result = subprocess.run(["ssh", "-V"], capture_output=True, text=True)
        logger.info(f"SSH version - Return code: {result.returncode}")
        logger.info(f"SSH version - Stdout: {result.stdout}")
        logger.info(f"SSH version - Stderr: {result.stderr}")
        
        # Test SSH client capabilities
        result = subprocess.run(["ssh", "-Q", "cipher"], capture_output=True, text=True)
        logger.info(f"SSH ciphers available: {len(result.stdout.splitlines())} ciphers")
        
    except Exception as e:
        logger.error(f"SSH version test failed: {e}")


def test_container_direct_access():
    """Test direct container access without SSH."""
    logger.info("\n=== Testing Container Direct Access ===")
    
    # This won't work from Windows to remote Docker, but let's see what happens
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    try:
        # Try to see if we can access Docker directly (will fail, but informative)
        result = subprocess.run(
            ["docker", "ps", "--filter", f"name={container_name}"],
            capture_output=True, text=True, timeout=5
        )
        logger.info(f"Direct Docker access - Return code: {result.returncode}")
        logger.info(f"Direct Docker access - Stdout: {result.stdout}")
        logger.info(f"Direct Docker access - Stderr: {result.stderr}")
        
    except Exception as e:
        logger.info(f"Direct Docker access failed (expected): {e}")


async def test_mcp_sdk_behavior():
    """Test MCP SDK behavior directly."""
    logger.info("\n=== Testing MCP SDK Behavior ===")
    
    try:
        from mcp import StdioServerParameters
        from mcp.client.stdio import stdio_client
        
        # Test with a simple command that should work
        test_cmd = ["echo", "test"]
        
        server_params = StdioServerParameters(
            command=test_cmd[0],
            args=test_cmd[1:],
            env=None,
        )
        
        logger.info("Testing MCP SDK with simple echo command...")
        
        async with stdio_client(server_params) as (read, write):
            logger.info("✅ MCP SDK can create STDIO client")
            
    except Exception as e:
        logger.error(f"MCP SDK test failed: {e}")


async def main():
    """Main test function."""
    logger.info("Starting SSH Differences Analysis")
    
    # Test 1: SSH client version and capabilities
    test_ssh_client_version()
    
    # Test 2: Direct SSH command testing
    test_direct_ssh_command()
    
    # Test 3: Container direct access
    test_container_direct_access()
    
    # Test 4: MCP SDK behavior
    await test_mcp_sdk_behavior()
    
    logger.info("\n" + "="*60)
    logger.info("ANALYSIS COMPLETE")
    logger.info("="*60)
    logger.info("Check the logs above to identify platform differences")


if __name__ == "__main__":
    asyncio.run(main())
