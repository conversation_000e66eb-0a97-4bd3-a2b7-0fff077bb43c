#!/usr/bin/env python3
"""
Smart Routing Test Runner

Runs all smart routing tests in sequence and provides a comprehensive report.

Usage:
    poetry run python tests/run_smart_routing_tests.py
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SmartRoutingTestRunner:
    """Test runner for all smart routing tests."""

    def __init__(self):
        self.test_dir = Path(__file__).parent
        self.results_dir = self.test_dir / "results"
        self.results_dir.mkdir(exist_ok=True)
        
        self.test_scripts = [
            {
                "name": "Execution Router Functionality",
                "script": "test_execution_router_functionality.py",
                "description": "Tests MCP config fetch, execution method determination, and routing logic"
            },
            {
                "name": "Server Type Differentiation", 
                "script": "test_server_type_differentiation.py",
                "description": "Tests SSE, STDIO, and HTTP server type detection and prioritization"
            },
            {
                "name": "Kafka Smart Routing Integration",
                "script": "test_kafka_smart_routing_integration.py", 
                "description": "Tests end-to-end smart routing via Kafka with real requests"
            }
        ]

    async def run_test_script(self, script_info: Dict[str, str]) -> Dict[str, Any]:
        """Run a single test script and capture results."""
        script_name = script_info["name"]
        script_file = script_info["script"]
        script_path = self.test_dir / script_file
        
        logger.info(f"🧪 Running {script_name}")
        logger.info(f"   Script: {script_file}")
        logger.info(f"   Description: {script_info['description']}")
        
        start_time = time.time()
        
        try:
            # Run the test script
            process = await asyncio.create_subprocess_exec(
                sys.executable, str(script_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.test_dir.parent
            )
            
            stdout, stderr = await process.communicate()
            end_time = time.time()
            
            # Decode output
            stdout_text = stdout.decode('utf-8') if stdout else ""
            stderr_text = stderr.decode('utf-8') if stderr else ""
            
            # Determine success based on exit code
            success = process.returncode == 0
            
            result = {
                "test_name": script_name,
                "script_file": script_file,
                "success": success,
                "exit_code": process.returncode,
                "execution_time": end_time - start_time,
                "stdout": stdout_text,
                "stderr": stderr_text,
                "timestamp": time.time()
            }
            
            if success:
                logger.info(f"✅ {script_name} completed successfully ({result['execution_time']:.2f}s)")
            else:
                logger.error(f"❌ {script_name} failed with exit code {process.returncode}")
                if stderr_text:
                    logger.error(f"   Error output: {stderr_text[:200]}...")
            
            return result
            
        except Exception as e:
            end_time = time.time()
            result = {
                "test_name": script_name,
                "script_file": script_file,
                "success": False,
                "exit_code": -1,
                "execution_time": end_time - start_time,
                "stdout": "",
                "stderr": str(e),
                "timestamp": time.time()
            }
            
            logger.error(f"❌ {script_name} failed with exception: {e}")
            return result

    def extract_test_results(self, script_result: Dict[str, Any]) -> Dict[str, Any]:
        """Extract structured test results from script output."""
        extracted = {
            "test_name": script_result["test_name"],
            "success": script_result["success"],
            "execution_time": script_result["execution_time"],
            "detailed_results": None,
            "summary": None
        }
        
        # Try to find and load JSON results file
        script_file = script_result["script_file"]
        if "execution_router_functionality" in script_file:
            results_file = self.test_dir / "execution_router_test_results.json"
        elif "server_type_differentiation" in script_file:
            results_file = self.test_dir / "server_type_test_results.json"
        elif "kafka_smart_routing" in script_file:
            results_file = self.test_dir / "kafka_smart_routing_test_results.json"
        else:
            results_file = None
        
        if results_file and results_file.exists():
            try:
                with open(results_file, 'r') as f:
                    extracted["detailed_results"] = json.load(f)
                    
                # Extract summary information
                if extracted["detailed_results"]:
                    extracted["summary"] = self._extract_summary(extracted["detailed_results"])
                    
            except Exception as e:
                logger.warning(f"Failed to load results file {results_file}: {e}")
        
        return extracted

    def _extract_summary(self, detailed_results: Dict[str, Any]) -> Dict[str, Any]:
        """Extract summary information from detailed results."""
        summary = {
            "overall_success": detailed_results.get("overall_success", False),
            "success_rate": detailed_results.get("success_rate", 0),
            "test_count": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "key_findings": []
        }
        
        # Count tests
        tests = detailed_results.get("tests", {})
        summary["test_count"] = len(tests)
        summary["passed_tests"] = sum(1 for test in tests.values() if test.get("success", False))
        summary["failed_tests"] = summary["test_count"] - summary["passed_tests"]
        
        # Extract key findings based on test type
        if "execution_method" in str(detailed_results):
            # Execution router test
            method_test = tests.get("method_determination", {})
            if method_test.get("success"):
                summary["key_findings"].append(
                    f"Execution method: {method_test.get('execution_method')}"
                )
                summary["key_findings"].append(
                    f"Fallback available: {method_test.get('fallback_available')}"
                )
        
        if "server_types_found" in str(detailed_results):
            # Server type test
            categorization_test = tests.get("url_categorization", {})
            if categorization_test.get("success"):
                summary["key_findings"].append(
                    f"Server types: {categorization_test.get('server_types_found', [])}"
                )
                summary["key_findings"].append(
                    f"Selected: {categorization_test.get('selected_category')}"
                )
        
        if "smart_routing" in tests:
            # Kafka integration test
            smart_test = tests.get("smart_routing", {})
            if smart_test.get("success"):
                summary["key_findings"].append(
                    f"Smart routing successful in {smart_test.get('response_time', 0):.2f}s"
                )
                summary["key_findings"].append(
                    f"MCP status: {smart_test.get('mcp_status')}"
                )
        
        return summary

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all smart routing tests."""
        logger.info("🚀 Starting Smart Routing Test Suite")
        logger.info("=" * 60)
        
        overall_start_time = time.time()
        all_results = {
            "test_suite": "Smart Routing Tests",
            "timestamp": time.time(),
            "test_results": [],
            "summary": {}
        }
        
        # Run each test script
        for i, script_info in enumerate(self.test_scripts, 1):
            logger.info(f"\n📋 Test {i}/{len(self.test_scripts)}: {script_info['name']}")
            logger.info("-" * 50)
            
            script_result = await self.run_test_script(script_info)
            extracted_result = self.extract_test_results(script_result)
            all_results["test_results"].append(extracted_result)
            
            # Brief pause between tests
            if i < len(self.test_scripts):
                await asyncio.sleep(2)
        
        overall_end_time = time.time()
        
        # Calculate overall summary
        total_tests = len(all_results["test_results"])
        successful_tests = sum(1 for test in all_results["test_results"] if test["success"])
        
        all_results["summary"] = {
            "total_execution_time": overall_end_time - overall_start_time,
            "total_test_scripts": total_tests,
            "successful_scripts": successful_tests,
            "failed_scripts": total_tests - successful_tests,
            "overall_success_rate": successful_tests / total_tests if total_tests > 0 else 0,
            "overall_success": successful_tests == total_tests
        }
        
        return all_results

    def print_comprehensive_summary(self, results: Dict[str, Any]):
        """Print comprehensive test suite summary."""
        print("\n" + "=" * 100)
        print("📊 SMART ROUTING TEST SUITE - COMPREHENSIVE SUMMARY")
        print("=" * 100)
        
        summary = results["summary"]
        print(f"Overall Success: {'✅ PASS' if summary['overall_success'] else '❌ FAIL'}")
        print(f"Success Rate: {summary['overall_success_rate']:.1%}")
        print(f"Total Execution Time: {summary['total_execution_time']:.2f}s")
        print(f"Test Scripts: {summary['successful_scripts']}/{summary['total_test_scripts']} passed")
        
        print(f"\n📋 Individual Test Script Results:")
        for test_result in results["test_results"]:
            status = "✅" if test_result["success"] else "❌"
            print(f"   {status} {test_result['test_name']} ({test_result['execution_time']:.2f}s)")
            
            if test_result["summary"]:
                summary_info = test_result["summary"]
                print(f"      → Tests: {summary_info['passed_tests']}/{summary_info['test_count']} passed")
                
                for finding in summary_info["key_findings"][:2]:  # Show top 2 findings
                    print(f"      → {finding}")
        
        print(f"\n🎯 Key Capabilities Tested:")
        print(f"   ✓ MCP configuration fetching and parsing")
        print(f"   ✓ Execution method determination (container vs URL)")
        print(f"   ✓ Server type differentiation (SSE, STDIO, HTTP)")
        print(f"   ✓ Smart routing priority logic")
        print(f"   ✓ End-to-end Kafka integration")
        print(f"   ✓ Fallback mechanism functionality")
        
        print(f"\n📁 Test Results Location:")
        print(f"   Main results: {self.results_dir}")
        print(f"   Individual test results: tests/*.json")
        
        print("=" * 100)

    async def save_results(self, results: Dict[str, Any]):
        """Save comprehensive results to file."""
        results_file = self.results_dir / f"smart_routing_test_suite_{int(time.time())}.json"
        
        try:
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"💾 Comprehensive results saved to: {results_file}")
            
            # Also save a latest.json for easy access
            latest_file = self.results_dir / "latest_test_results.json"
            with open(latest_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"Failed to save results: {e}")


async def main():
    """Main test runner execution."""
    print("🧪 Smart Routing Test Suite Runner")
    print("   Comprehensive testing of MCP smart routing functionality")
    print("")
    
    runner = SmartRoutingTestRunner()
    
    try:
        results = await runner.run_all_tests()
        runner.print_comprehensive_summary(results)
        await runner.save_results(results)
        
        return 0 if results["summary"]["overall_success"] else 1
        
    except KeyboardInterrupt:
        logger.info("⏹️ Test suite cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"💥 Test suite execution failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Test suite cancelled")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
