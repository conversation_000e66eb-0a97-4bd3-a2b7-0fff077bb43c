#!/usr/bin/env python3
"""
Analyze the connection issue in detail.
"""

import asyncio
import subprocess
import logging
import sys
import json
import time
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key, get_global_ssh_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_container_connection_limits():
    """Test if the container can handle multiple connections."""
    logger.info("=== Testing Container Connection Limits ===")
    
    initialize_global_ssh_key(settings.ssh_key_content)
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    command = "node dist/index.js"
    
    # Test multiple sequential connections
    for i in range(3):
        logger.info(f"Testing connection {i+1}...")
        
        init_message = {
            "jsonrpc": "2.0",
            "id": i+1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
                "clientInfo": {"name": f"test-client-{i+1}", "version": "1.0.0"}
            }
        }
        
        init_json = json.dumps(init_message)
        
        test_cmd = [
            "ssh",
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "ConnectTimeout=10",
            "-i", ssh_key_path,
            f"{settings.ssh_user}@{settings.ssh_host}",
            f"echo '{init_json}' | timeout 3 docker exec -i {container_id} {command}"
        ]
        
        try:
            start_time = time.time()
            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
            end_time = time.time()
            
            logger.info(f"Connection {i+1} - Exit code: {result.returncode}, Time: {end_time-start_time:.2f}s")
            
            if result.stdout:
                try:
                    response = json.loads(result.stdout.strip())
                    logger.info(f"Connection {i+1} - Got valid response: {response.get('result', {}).get('serverInfo', {}).get('name', 'unknown')}")
                except json.JSONDecodeError:
                    logger.warning(f"Connection {i+1} - Invalid JSON response")
            
            if result.stderr and "error" in result.stderr.lower():
                logger.warning(f"Connection {i+1} - Error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.warning(f"Connection {i+1} - Timed out")
        except Exception as e:
            logger.error(f"Connection {i+1} - Exception: {e}")
        
        # Small delay between connections
        await asyncio.sleep(1)


async def test_mcp_session_sequence():
    """Test the exact MCP session initialization sequence."""
    logger.info("=== Testing MCP Session Sequence ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    command = "node dist/index.js"
    
    # Step 1: Initialize
    logger.info("Step 1: Sending initialize message...")
    init_message = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
            "clientInfo": {"name": "test-sequence", "version": "1.0.0"}
        }
    }
    
    # Step 2: Send initialized notification
    logger.info("Step 2: Sending initialized notification...")
    initialized_message = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized"
    }
    
    # Combine both messages
    messages = [
        json.dumps(init_message),
        json.dumps(initialized_message)
    ]
    
    combined_input = "\n".join(messages)
    
    test_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=10",
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"echo '{combined_input}' | timeout 5 docker exec -i {container_id} {command}"
    ]
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=15)
        logger.info(f"Sequence test - Exit code: {result.returncode}")
        
        if result.stdout:
            logger.info(f"Sequence test - Response:\n{result.stdout}")
        if result.stderr:
            logger.info(f"Sequence test - Stderr:\n{result.stderr}")
            
    except Exception as e:
        logger.error(f"Sequence test failed: {e}")


async def test_interactive_session():
    """Test an interactive session to see what happens."""
    logger.info("=== Testing Interactive Session ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    command = "node dist/index.js"
    
    # Create a longer interactive session
    messages = [
        # Initialize
        json.dumps({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
                "clientInfo": {"name": "interactive-test", "version": "1.0.0"}
            }
        }),
        # Initialized notification
        json.dumps({
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }),
        # List tools
        json.dumps({
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        })
    ]
    
    # Send messages with delays
    input_script = f"""
echo '{messages[0]}'
sleep 1
echo '{messages[1]}'
sleep 1
echo '{messages[2]}'
sleep 2
"""
    
    test_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=10",
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"bash -c \"{input_script}\" | timeout 10 docker exec -i {container_id} {command}"
    ]
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=20)
        logger.info(f"Interactive test - Exit code: {result.returncode}")
        
        if result.stdout:
            logger.info(f"Interactive test - Response:\n{result.stdout}")
        if result.stderr:
            logger.info(f"Interactive test - Stderr:\n{result.stderr}")
            
    except Exception as e:
        logger.error(f"Interactive test failed: {e}")


async def main():
    """Main test function."""
    logger.info("Starting Connection Analysis Tests")
    
    if not settings.ssh_key_content:
        logger.error("No SSH key content found")
        return
    
    # Test 1: Connection limits
    await test_container_connection_limits()
    
    # Test 2: MCP session sequence
    await test_mcp_session_sequence()
    
    # Test 3: Interactive session
    await test_interactive_session()
    
    logger.info("Connection analysis completed")


if __name__ == "__main__":
    asyncio.run(main())
