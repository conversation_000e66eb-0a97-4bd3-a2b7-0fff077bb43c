#!/usr/bin/env python3
"""
Test SSH key formatting and creation.
"""

import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key, get_global_ssh_manager


def test_ssh_key_formatting():
    """Test SSH key formatting and creation."""
    print("Testing SSH key formatting...")
    
    if not settings.ssh_key_content:
        print("❌ No SSH key content found")
        return False
    
    print(f"SSH key content length: {len(settings.ssh_key_content)} characters")
    
    # Initialize SSH key with formatting
    print("Creating SSH key with proper formatting...")
    initialize_global_ssh_key(settings.ssh_key_content)
    
    # Get SSH key path
    manager = get_global_ssh_manager()
    key_path = manager.get_ssh_key_path()
    
    if not key_path or not os.path.exists(key_path):
        print("❌ SSH key file not created")
        return False
    
    print(f"✅ SSH key file created: {key_path}")
    
    # Check file content
    with open(key_path, 'r') as f:
        content = f.read()
    
    lines = content.split('\n')
    print(f"SSH key file has {len(lines)} lines")
    
    # Check formatting
    if lines[0].startswith('-----BEGIN'):
        print("✅ SSH key starts with proper header")
    else:
        print("❌ SSH key header not found")
        return False
    
    if lines[-1].startswith('-----END') or (len(lines) > 1 and lines[-2].startswith('-----END')):
        print("✅ SSH key ends with proper footer")
    else:
        print("❌ SSH key footer not found")
        return False
    
    # Check line lengths (should be 64 characters for key content)
    key_content_lines = [line for line in lines if not line.startswith('-----') and line.strip()]
    if key_content_lines:
        max_line_length = max(len(line) for line in key_content_lines)
        print(f"Maximum line length in key content: {max_line_length}")
        if max_line_length <= 64:
            print("✅ SSH key lines are properly formatted")
        else:
            print("❌ SSH key lines are too long")
    
    print(f"First few lines of SSH key:")
    for i, line in enumerate(lines[:5]):
        print(f"  {i+1}: {line}")
    
    return True


if __name__ == "__main__":
    success = test_ssh_key_formatting()
    if success:
        print("✅ SSH key formatting test completed successfully")
    else:
        print("❌ SSH key formatting test failed")
