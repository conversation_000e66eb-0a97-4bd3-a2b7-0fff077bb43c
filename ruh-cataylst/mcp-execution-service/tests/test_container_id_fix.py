#!/usr/bin/env python3
"""
Test script to verify the container ID fix.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.core_.mcp_executor import MCPExecutor
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_container_id_fix():
    """Test the container ID fix with a real MCP execution."""
    logger.info("=== Testing Container ID Fix ===")

    # Create MCP executor
    executor = MCPExecutor()

    # Test parameters - use a simple tool that should work
    test_params = {
        "tool_name": "fetch",
        "tool_parameters": {"url": "https://httpbin.org/json", "method": "GET"},
        "user_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
        "mcp_id": "35441857-f358-4595-9993-33abd9afee06",
        "retries": 1,  # Only try once for faster testing
        "correlation_id": "test-container-fix",
    }

    logger.info(f"Testing with parameters: {test_params}")

    try:
        # Execute the tool using container lifecycle
        result = await executor.execute_tool(**test_params)

        logger.info("✅ Container ID fix successful!")
        logger.info(f"Result: {result}")
        return True

    except Exception as e:
        logger.error(f"❌ Container ID fix test failed: {e}")
        return False


async def test_container_creation_only():
    """Test just the container creation part to verify the ID handling."""
    logger.info("\n=== Testing Container Creation Only ===")

    from app.services.container_client import ContainerManagementClient

    container_client = ContainerManagementClient()

    try:
        # Create a container
        success, message, container_id = await container_client.create_container(
            mcp_id="35441857-f358-4595-9993-33abd9afee06",
            user_id="91a237fd-0225-4e02-9e9f-805eff073b07",
            container_type="stdio",
            env=None,
        )

        if success:
            logger.info(f"✅ Container created successfully: {container_id}")

            # Test if we can find this container
            from app.core_.client import MCPClient
            from app.services.ssh_manager import initialize_global_ssh_key

            # Initialize SSH key
            if settings.ssh_key_content:
                initialize_global_ssh_key(settings.ssh_key_content)

            # Create MCP client with the exact container ID
            client = MCPClient(
                docker_image=container_id,  # Use exact container ID
                connection_type="ssh_docker",
                container_command=None,
                use_fallback_ssh=True,
            )

            # Test container existence check
            container_exists = await client._check_container_exists()
            logger.info(f"Container exists check: {container_exists}")

            # Clean up - delete the container
            try:
                delete_success, delete_message = (
                    await container_client.delete_container(container_id)
                )
                if delete_success:
                    logger.info(f"✅ Container cleaned up: {container_id}")
                else:
                    logger.warning(f"Failed to clean up container: {delete_message}")
            except Exception as cleanup_error:
                logger.warning(f"Error during cleanup: {cleanup_error}")

            return container_exists
        else:
            logger.error(f"❌ Container creation failed: {message}")
            return False

    except Exception as e:
        logger.error(f"❌ Container creation test failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info("Starting Container ID Fix Tests")

    # Test 1: Container creation and ID handling
    success1 = await test_container_creation_only()

    # Test 2: Full MCP execution (only if container creation works)
    success2 = False
    if success1:
        success2 = await test_container_id_fix()
    else:
        logger.warning("Skipping full execution test due to container creation failure")

    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY:")
    logger.info("=" * 60)
    logger.info(
        f"Container creation and ID handling: {'✅ PASS' if success1 else '❌ FAIL'}"
    )
    logger.info(f"Full MCP execution: {'✅ PASS' if success2 else '❌ FAIL'}")

    if success1:
        logger.info("🎉 Container ID fix is working!")
        if success2:
            logger.info("🚀 Full MCP execution is also working!")
    else:
        logger.error("💥 Container ID fix needs more work!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
