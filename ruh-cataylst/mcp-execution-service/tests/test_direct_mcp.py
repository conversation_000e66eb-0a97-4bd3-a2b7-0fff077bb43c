#!/usr/bin/env python3
"""
Test direct MCP communication without the problematic SDK.
"""

import subprocess
import json
import logging
import sys
import asyncio
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DirectMCPClient:
    """Direct MCP client that bypasses the problematic Python SDK."""

    def __init__(self, ssh_command):
        self.ssh_command = ssh_command
        self.process = None
        self.request_id = 1

    async def connect(self):
        """Start the SSH process."""
        logger.info(f"Starting SSH process: {' '.join(self.ssh_command)}")

        self.process = subprocess.Popen(
            self.ssh_command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0,
        )

        # Wait a moment for connection
        await asyncio.sleep(1)

        # Check if process is running
        if self.process.poll() is not None:
            stderr_output = self.process.stderr.read() if self.process.stderr else ""
            raise Exception(f"SSH process failed: {stderr_output}")

        logger.info("SSH process started successfully")

    def send_message(self, method, params=None):
        """Send an MCP message."""
        message = {"jsonrpc": "2.0", "id": self.request_id, "method": method}

        if params:
            message["params"] = params

        message_json = json.dumps(message) + "\n"
        logger.info(f"Sending: {message_json.strip()}")

        self.process.stdin.write(message_json)
        self.process.stdin.flush()

        self.request_id += 1

    async def read_response(self, timeout=5):
        """Read an MCP response."""
        import time

        start_time = time.time()

        while time.time() - start_time < timeout:
            # Check if data is available
            if self.process.stdout:
                try:
                    line = self.process.stdout.readline()
                    if line:
                        logger.info(f"Received: {line.strip()}")
                        return json.loads(line.strip())
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON: {e}, line: {line}")
                except Exception as e:
                    logger.error(f"Error reading response: {e}")

            await asyncio.sleep(0.1)

        raise TimeoutError("No response received within timeout")

    async def initialize(self):
        """Initialize MCP session."""
        self.send_message(
            "initialize",
            {
                "protocolVersion": "2024-11-05",
                "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
                "clientInfo": {"name": "direct-mcp-client", "version": "1.0.0"},
            },
        )

        response = await self.read_response()
        return response

    async def list_tools(self):
        """List available tools."""
        self.send_message("tools/list")
        response = await self.read_response()
        return response

    async def call_tool(self, tool_name, arguments):
        """Call a specific tool."""
        self.send_message("tools/call", {"name": tool_name, "arguments": arguments})
        response = await self.read_response()
        return response

    def close(self):
        """Close the connection."""
        if self.process:
            self.process.terminate()
            self.process.wait()


async def test_direct_mcp():
    """Test direct MCP communication."""
    logger.info("=== Testing Direct MCP Communication ===")

    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)

    container_name = (
        "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    )
    key_file = "E:\\RapidInnovation\\Automation Projects\\Ruh\\ruh_catalyst\\mcp-executor-service\\mcp_ssh_key.pem"

    # Build SSH command
    ssh_command = [
        "ssh",
        "-i",
        key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_name} node dist/index.js",
    ]

    try:
        # Create direct MCP client
        client = DirectMCPClient(ssh_command)

        # Connect
        await client.connect()

        # Initialize MCP session
        logger.info("Initializing MCP session...")
        init_response = await client.initialize()
        logger.info(f"Initialize response: {init_response}")

        # List tools
        logger.info("Listing tools...")
        tools_response = await client.list_tools()
        logger.info(f"Tools response: {tools_response}")

        # Call a tool
        if tools_response.get("result", {}).get("tools"):
            tool_name = "fetch"  # Assuming fetch tool exists
            logger.info(f"Calling tool: {tool_name}")

            tool_response = await client.call_tool(
                tool_name, {"url": "https://httpbin.org/json"}
            )
            logger.info(f"Tool response: {tool_response}")

        logger.info("✅ Direct MCP communication successful!")
        return True

    except Exception as e:
        logger.error(f"❌ Direct MCP communication failed: {e}")
        return False

    finally:
        if "client" in locals():
            client.close()


async def main():
    """Main test function."""
    logger.info("Starting Direct MCP Test")

    success = await test_direct_mcp()

    logger.info("\n" + "=" * 60)
    logger.info("DIRECT MCP TEST SUMMARY:")
    logger.info("=" * 60)

    if success:
        logger.info("🎉 Direct MCP communication works!")
        logger.info("💡 The issue is with the Python MCP SDK on Windows")
        logger.info("🔧 Solution: Implement direct MCP client")
    else:
        logger.error("💥 Direct MCP communication failed")
        logger.error("🔍 Need to investigate further")


if __name__ == "__main__":
    asyncio.run(main())
