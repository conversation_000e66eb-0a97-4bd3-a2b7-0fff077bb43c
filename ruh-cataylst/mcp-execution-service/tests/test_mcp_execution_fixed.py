#!/usr/bin/env python3
"""
Test MCP execution with the fixed container command detection.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

from app.core_.mcp_executor import MCPExecutor
from app.services.ssh_manager import initialize_global_ssh_key
from app.config.config import settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_mcp_execution():
    """Test MCP execution with container lifecycle."""
    print("🧪 Testing MCP Execution with Fixed Container Command...")
    
    # Initialize global SSH manager
    print("Initializing global SSH manager...")
    initialize_global_ssh_key(settings.ssh_key_content)
    
    # Create MCP executor
    executor = MCPExecutor(producer=None, logger=logger)
    
    # Test parameters
    mcp_id = "35441857-f358-4595-9993-33abd9afee06"
    user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"
    tool_name = "fetch"
    tool_parameters = {
        "url": "https://httpbin.org/json"
    }
    
    try:
        print(f"🔧 Executing tool: {tool_name}")
        print(f"   MCP ID: {mcp_id}")
        print(f"   User ID: {user_id}")
        print(f"   Parameters: {tool_parameters}")
        
        result = await executor.execute_tool(
            tool_name=tool_name,
            tool_parameters=tool_parameters,
            user_id=user_id,
            mcp_id=mcp_id,
            retries=1
        )
        
        print(f"✅ MCP execution successful!")
        print(f"   Result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ MCP execution failed: {e}")
        logger.error(f"MCP execution error: {e}", exc_info=True)
        return False


async def main():
    """Main test function."""
    print("🚀 MCP Execution Test with Fixed Container Command")
    print("=" * 60)
    
    success = await test_mcp_execution()
    
    print("=" * 60)
    print("📊 Test Results:")
    if success:
        print("🎉 MCP execution working correctly with dynamic container command!")
    else:
        print("❌ MCP execution still has issues - check logs for details")


if __name__ == "__main__":
    asyncio.run(main())
