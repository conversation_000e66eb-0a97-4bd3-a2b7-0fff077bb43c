#!/usr/bin/env python3
"""
Simple SSH connection test.
"""

import subprocess
import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.config.config import settings


def test_ssh_simple():
    """Test SSH connection with simple commands."""
    print("Testing SSH connection...")
    
    print(f"SSH Host: {settings.ssh_host}")
    print(f"SSH User: {settings.ssh_user}")
    print(f"SSH Port: {settings.ssh_port}")
    print(f"SSH Key available: {'Yes' if settings.ssh_key_content else 'No'}")
    
    # Check if SSH key file exists
    ssh_key_path = os.path.join(os.getcwd(), "mcp_ssh_key.pem")
    print(f"SSH Key file: {ssh_key_path}")
    print(f"SSH Key file exists: {os.path.exists(ssh_key_path)}")
    
    if not os.path.exists(ssh_key_path):
        print("❌ SSH key file not found. Creating it...")
        from app.services.ssh_manager import initialize_global_ssh_key
        initialize_global_ssh_key(settings.ssh_key_content)
        print(f"SSH Key file created: {os.path.exists(ssh_key_path)}")
    
    # Test 1: Basic connectivity with timeout
    print("\n1. Testing basic SSH connectivity...")
    basic_cmd = [
        "ssh",
        "-o", "ConnectTimeout=10",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "BatchMode=yes",  # Non-interactive
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        "echo 'Hello from SSH'"
    ]
    
    print(f"Command: {' '.join(basic_cmd)}")
    
    try:
        result = subprocess.run(basic_cmd, capture_output=True, text=True, timeout=15)
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout.strip()}")
        if result.stderr:
            print(f"STDERR: {result.stderr.strip()}")
        
        if result.returncode == 0:
            print("✅ SSH connection successful")
        else:
            print("❌ SSH connection failed")
            return False
    except subprocess.TimeoutExpired:
        print("❌ SSH connection timed out")
        return False
    except Exception as e:
        print(f"❌ SSH error: {e}")
        return False
    
    # Test 2: Check Docker availability
    print("\n2. Testing Docker availability...")
    docker_cmd = basic_cmd[:-1] + ["docker --version"]
    
    try:
        result = subprocess.run(docker_cmd, capture_output=True, text=True, timeout=15)
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout.strip()}")
        if result.stderr:
            print(f"STDERR: {result.stderr.strip()}")
        
        if result.returncode == 0:
            print("✅ Docker available")
        else:
            print("❌ Docker not available")
    except Exception as e:
        print(f"❌ Docker test error: {e}")
    
    # Test 3: List running containers
    print("\n3. Testing Docker containers...")
    containers_cmd = basic_cmd[:-1] + ["docker ps --format 'table {{.ID}}\\t{{.Image}}\\t{{.Status}}'"]
    
    try:
        result = subprocess.run(containers_cmd, capture_output=True, text=True, timeout=15)
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"Running containers:\n{result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr.strip()}")
    except Exception as e:
        print(f"❌ Container list error: {e}")
    
    return True


if __name__ == "__main__":
    test_ssh_simple()
