from typing import Dict, Optional, Callable, Literal
import logging
import time
from app.schemas.authentication_manager import (
    AuthenticationType,
    AuthenticationConfig,
    TokenValidationError,
)
from app.core_.exceptions import MCPAuthenticationError, ErrorCategory, ErrorCode


class AuthenticationManager:
    """Manages multiple authentication schemes and token lifecycle."""

    def __init__(self):
        self.auth_configs: Dict[str, AuthenticationConfig] = {}
        self.default_config: Optional[AuthenticationConfig] = None
        self.logger = logging.getLogger(f"{__name__}.AuthenticationManager")

    def add_authentication(
        self, name: str, config: AuthenticationConfig, is_default: bool = False
    ) -> None:
        """Add an authentication configuration."""
        self.auth_configs[name] = config
        if is_default or self.default_config is None:
            self.default_config = config
        self.logger.debug(
            f"Added authentication config: {name} (type: {config.auth_type.value})"
        )

    def add_bearer_token(
        self,
        token: str,
        name: str = "default",
        expires_at: Optional[float] = None,
        refresh_callback: Optional[Callable[[], str]] = None,
    ) -> None:
        """Add Bearer token authentication."""
        config = AuthenticationConfig(
            auth_type=AuthenticationType.BEARER,
            token=token,
            token_expires_at=expires_at,
            token_refresh_callback=refresh_callback,
        )
        self.add_authentication(name, config, is_default=True)

    def add_api_key(
        self,
        api_key: str,
        location: Literal["header", "query", "body"] = "header",
        key_name: str = "X-API-Key",
        name: str = "api_key",
    ) -> None:
        """Add API key authentication."""
        if location == "header":
            auth_type = AuthenticationType.API_KEY_HEADER
            config = AuthenticationConfig(
                auth_type=auth_type, api_key=api_key, api_key_name=key_name
            )
        elif location == "query":
            auth_type = AuthenticationType.API_KEY_QUERY
            config = AuthenticationConfig(
                auth_type=auth_type, api_key=api_key, query_params={key_name: api_key}
            )
        elif location == "body":
            auth_type = AuthenticationType.API_KEY_BODY
            config = AuthenticationConfig(
                auth_type=auth_type, api_key=api_key, body_params={key_name: api_key}
            )
        else:
            raise ValueError(f"Invalid location: {location}")

        self.add_authentication(name, config)

    def add_custom_auth(
        self,
        headers: Dict[str, str] = None,
        query_params: Dict[str, str] = None,
        body_params: Dict[str, str] = None,
        name: str = "custom",
    ) -> None:
        """Add custom authentication."""
        config = AuthenticationConfig(
            auth_type=AuthenticationType.CUSTOM,
            custom_headers=headers or {},
            query_params=query_params or {},
            body_params=body_params or {},
        )
        self.add_authentication(name, config)

    async def get_headers(
        self, config_name: Optional[str] = None, raise_on_error: bool = False
    ) -> Dict[str, str]:
        """Get authentication headers."""
        config = self._get_config(config_name)
        if not config:
            if raise_on_error:
                raise MCPAuthenticationError(
                    "unknown",
                    f"Authentication config not found: {config_name}",
                    {"config_name": config_name, "operation": "get_headers"},
                )
            return {}

        headers = {}

        try:
            # Handle Bearer token
            if config.auth_type == AuthenticationType.BEARER:
                token = await self._get_valid_token(config)
                if token:
                    headers["Authorization"] = f"Bearer {token}"
                    self.logger.debug("Added Bearer token to headers")
                elif raise_on_error:
                    raise MCPAuthenticationError(
                        "unknown",
                        "Bearer token not available",
                        {"config_name": config_name, "auth_type": "bearer"},
                    )

            # Handle API key in headers
            elif config.auth_type == AuthenticationType.API_KEY_HEADER:
                if config.api_key:
                    headers[config.api_key_name] = config.api_key
                    self.logger.debug(
                        f"Added API key to headers: {config.api_key_name}"
                    )
                elif raise_on_error:
                    raise MCPAuthenticationError(
                        "unknown",
                        "API key not available",
                        {"config_name": config_name, "auth_type": "api_key_header"},
                    )

            # Handle custom headers
            elif config.auth_type == AuthenticationType.CUSTOM:
                headers.update(config.custom_headers)
                self.logger.debug(
                    f"Added custom headers: {list(config.custom_headers.keys())}"
                )

        except TokenValidationError as e:
            if raise_on_error:
                raise MCPAuthenticationError(
                    "unknown",
                    f"Token validation failed: {e}",
                    {"config_name": config_name, "original_error": str(e)},
                ) from e
            self.logger.error(f"Token validation failed: {e}")

        return headers

    async def get_query_params(
        self, config_name: Optional[str] = None
    ) -> Dict[str, str]:
        """Get authentication query parameters."""
        config = self._get_config(config_name)
        if not config:
            return {}

        if config.auth_type in [
            AuthenticationType.API_KEY_QUERY,
            AuthenticationType.CUSTOM,
        ]:
            return config.query_params.copy()

        return {}

    async def get_body_params(
        self, config_name: Optional[str] = None
    ) -> Dict[str, str]:
        """Get authentication body parameters."""
        config = self._get_config(config_name)
        if not config:
            return {}

        if config.auth_type in [
            AuthenticationType.API_KEY_BODY,
            AuthenticationType.CUSTOM,
        ]:
            return config.body_params.copy()

        return {}

    def _get_config(
        self, config_name: Optional[str] = None
    ) -> Optional[AuthenticationConfig]:
        """Get authentication configuration by name or default."""
        if config_name:
            return self.auth_configs.get(config_name)
        return self.default_config

    async def _get_valid_token(self, config: AuthenticationConfig) -> Optional[str]:
        """Get a valid token, refreshing if necessary."""
        if not config.token:
            return None

        # Check if token is expired
        if config.token_expires_at and time.time() >= config.token_expires_at:
            self.logger.info("Token expired, attempting refresh")
            if config.token_refresh_callback:
                try:
                    new_token = config.token_refresh_callback()
                    config.token = new_token
                    self.logger.info("Token refreshed successfully")
                except Exception as e:
                    self.logger.error(f"Token refresh failed: {e}")
                    raise TokenValidationError(f"Failed to refresh token: {e}")
            else:
                raise TokenValidationError(
                    "Token expired and no refresh callback available"
                )

        return config.token

    def validate_token(self, token: str) -> bool:
        """Validate token format (basic validation)."""
        if not token or not isinstance(token, str):
            return False

        # Basic token validation - can be enhanced
        if len(token.strip()) < 10:  # Minimum reasonable token length
            return False

        return True

    def clear_authentication(self, name: Optional[str] = None) -> None:
        """Clear authentication configuration."""
        if name:
            if name in self.auth_configs:
                del self.auth_configs[name]
                self.logger.debug(f"Cleared authentication config: {name}")
        else:
            self.auth_configs.clear()
            self.default_config = None
            self.logger.debug("Cleared all authentication configs")

    # Convenience methods that always raise exceptions for better error propagation
    async def get_headers_with_exceptions(
        self, config_name: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Get authentication headers, raising exception on failure.

        Args:
            config_name: Optional config name to use

        Returns:
            Dictionary of authentication headers

        Raises:
            MCPAuthenticationError: If authentication fails
        """
        return await self.get_headers(config_name, raise_on_error=True)

    def validate_token_with_exceptions(self, token: str) -> bool:
        """
        Validate token format, raising exception on failure.

        Args:
            token: Token to validate

        Returns:
            True if token is valid

        Raises:
            MCPAuthenticationError: If token validation fails
        """
        if not self.validate_token(token):
            raise MCPAuthenticationError(
                "unknown",
                "Token validation failed",
                {
                    "token_length": len(token) if token else 0,
                    "operation": "token_validation",
                },
            )
        return True
