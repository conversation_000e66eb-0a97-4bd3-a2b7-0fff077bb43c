"""
Container management client for calling container lifecycle API endpoints.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, <PERSON>ple
import aiohttp

from app.config.config import settings
from app.core_.exceptions import (
    ContainerCreationError,
    ContainerExecutionError,
    ErrorCategory,
    ErrorCode,
    MCPExecutorError,
)


class ContainerManagementClient:
    """Client for interacting with container management API endpoints."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.api_base_url = settings.api_base_url
        self.timeout = settings.container_api_timeout
        self.server_auth_key = settings.server_auth_key
        self.logger.info(
            f"ContainerManagementClient initialized with server_auth_key: {self.server_auth_key[:10] if self.server_auth_key else 'None'}..."
        )

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests including authentication."""
        headers = {"Content-Type": "application/json"}

        if self.server_auth_key:
            headers["X-Server-Auth-Key"] = self.server_auth_key
            self.logger.info(
                f"Added X-Server-Auth-Key header: {self.server_auth_key[:10]}..."
            )
        else:
            self.logger.error(
                "No server_auth_key configured - requests will fail authentication"
            )

        return headers

    async def create_container(
        self,
        mcp_id: str,
        user_id: str,
        container_type: str = "stdio",
        env: Optional[Dict[str, str]] = None,
        raise_on_error: bool = False,
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Create a new container via API.

        Args:
            mcp_id: MCP server identifier
            user_id: User identifier
            container_type: Type of container connection
            env: Optional environment variables
            raise_on_error: If True, raise ContainerCreationError on failure

        Returns:
            Tuple of (success, message, container_id)

        Raises:
            ContainerCreationError: If raise_on_error=True and creation fails
        """
        url = f"{self.api_base_url}/api/v1/mcps/containers/create"

        payload = {"mcp_id": mcp_id, "user_id": user_id, "type": container_type}

        try:
            headers = self._get_headers()
            self.logger.info(f"Creating container via API: {url}")
            self.logger.info(f"Create container payload: {payload}")
            self.logger.info(f"Request headers: {headers}")

            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    response_text = await response.text()

                    if response.status in [200, 201]:
                        response_data = await response.json()

                        if response_data.get("success"):
                            container_id = response_data.get("container_id")
                            message = response_data.get(
                                "message", "Container created successfully"
                            )

                            self.logger.info(
                                f"Successfully created container: {container_id}"
                            )
                            return True, message, container_id
                        else:
                            error_msg = response_data.get(
                                "message", "Unknown error from container API"
                            )
                            self.logger.error(
                                f"Container API returned error: {error_msg}"
                            )
                            if raise_on_error:
                                raise ContainerCreationError(
                                    mcp_id,
                                    user_id,
                                    error_msg,
                                    {
                                        "container_type": container_type,
                                        "api_response": response_data,
                                    },
                                )
                            return False, error_msg, None
                    else:
                        error_msg = f"Container API returned status {response.status}: {response_text}"
                        self.logger.error(error_msg)
                        if raise_on_error:
                            raise ContainerCreationError(
                                mcp_id,
                                user_id,
                                error_msg,
                                {
                                    "container_type": container_type,
                                    "status_code": response.status,
                                },
                            )
                        return False, error_msg, None

        except asyncio.TimeoutError as timeout_error:
            error_msg = f"Timeout creating container after {self.timeout} seconds"
            self.logger.error(error_msg)
            if raise_on_error:
                raise ContainerCreationError(
                    mcp_id,
                    user_id,
                    error_msg,
                    {"container_type": container_type, "timeout": self.timeout},
                ) from timeout_error
            return False, error_msg, None

        except Exception as e:
            error_msg = f"Error creating container: {str(e)}"
            self.logger.error(error_msg)
            if raise_on_error:
                raise ContainerCreationError(
                    mcp_id,
                    user_id,
                    error_msg,
                    {
                        "container_type": container_type,
                        "exception_type": type(e).__name__,
                    },
                ) from e
            return False, error_msg, None

    async def stop_container(
        self, container_id: str, raise_on_error: bool = False
    ) -> Tuple[bool, str]:
        """
        Stop a container via API.

        Args:
            container_id: Container identifier
            raise_on_error: If True, raise ContainerExecutionError on failure

        Returns:
            Tuple of (success, message)

        Raises:
            ContainerExecutionError: If raise_on_error=True and stop fails
        """
        url = f"{self.api_base_url}/api/v1/mcps/containers/{container_id}/stop"

        try:
            self.logger.info(f"Stopping container via API: {container_id}")

            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                async with session.post(url, headers=self._get_headers()) as response:
                    response_text = await response.text()

                    if response.status == 200:
                        response_data = await response.json()
                        success = response_data.get("success", False)
                        message = response_data.get("message", "Container stopped")

                        if success:
                            self.logger.info(
                                f"Successfully stopped container: {container_id}"
                            )
                        else:
                            self.logger.error(
                                f"Failed to stop container {container_id}: {message}"
                            )
                            if raise_on_error:
                                raise ContainerExecutionError(
                                    container_id,
                                    message,
                                    {
                                        "operation": "stop",
                                        "api_response": response_data,
                                    },
                                )

                        return success, message
                    else:
                        error_msg = f"Container API returned status {response.status}: {response_text}"
                        self.logger.error(error_msg)
                        if raise_on_error:
                            raise ContainerExecutionError(
                                container_id,
                                error_msg,
                                {"operation": "stop", "status_code": response.status},
                            )
                        return False, error_msg

        except asyncio.TimeoutError as timeout_error:
            error_msg = f"Timeout stopping container {container_id} after {self.timeout} seconds"
            self.logger.error(error_msg)
            if raise_on_error:
                raise ContainerExecutionError(
                    container_id,
                    error_msg,
                    {"operation": "stop", "timeout": self.timeout},
                ) from timeout_error
            return False, error_msg

        except Exception as e:
            error_msg = f"Error stopping container {container_id}: {str(e)}"
            self.logger.error(error_msg)
            if raise_on_error:
                raise ContainerExecutionError(
                    container_id,
                    error_msg,
                    {"operation": "stop", "exception_type": type(e).__name__},
                ) from e
            return False, error_msg

    # Convenience methods that always raise exceptions for better error propagation
    async def create_container_with_exceptions(
        self,
        mcp_id: str,
        user_id: str,
        container_type: str = "stdio",
        env: Optional[Dict[str, str]] = None,
    ) -> str:
        """
        Create a container and return container_id, raising exception on failure.

        Args:
            mcp_id: MCP server identifier
            user_id: User identifier
            container_type: Type of container connection
            env: Optional environment variables

        Returns:
            container_id: The created container ID

        Raises:
            ContainerCreationError: If creation fails
        """
        success, message, container_id = await self.create_container(
            mcp_id, user_id, container_type, env, raise_on_error=True
        )
        if not success or not container_id:
            # This should not happen since raise_on_error=True, but just in case
            raise ContainerCreationError(
                mcp_id,
                user_id,
                message or "Unknown error",
                {"container_type": container_type},
            )
        return container_id

    async def stop_container_with_exceptions(self, container_id: str) -> None:
        """
        Stop a container, raising exception on failure.

        Args:
            container_id: Container identifier

        Raises:
            ContainerExecutionError: If stop fails
        """
        success, message = await self.stop_container(container_id, raise_on_error=True)
        if not success:
            # This should not happen since raise_on_error=True, but just in case
            raise ContainerExecutionError(
                container_id, message or "Unknown error", {"operation": "stop"}
            )

    async def get_container_status(
        self, container_id: str
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Get container status via API.

        Args:
            container_id: Container identifier

        Returns:
            Tuple of (success, message, status_details)
        """
        url = f"{self.api_base_url}/api/v1/mcps/containers/{container_id}/status"

        try:
            self.logger.info(f"Getting container status via API: {container_id}")

            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                async with session.get(url, headers=self._get_headers()) as response:
                    response_text = await response.text()

                    if response.status == 200:
                        response_data = await response.json()
                        success = response_data.get("success", False)
                        message = response_data.get("message", "Status retrieved")
                        details = response_data.get("details", {})

                        if success:
                            self.logger.info(
                                f"Successfully retrieved status for container: {container_id}"
                            )
                        else:
                            self.logger.error(
                                f"Failed to get status for container {container_id}: {message}"
                            )

                        return success, message, details
                    else:
                        error_msg = f"Container API returned status {response.status}: {response_text}"
                        self.logger.error(error_msg)
                        return False, error_msg, None

        except asyncio.TimeoutError:
            error_msg = f"Timeout getting status for container {container_id} after {self.timeout} seconds"
            self.logger.error(error_msg)
            return False, error_msg, None

        except Exception as e:
            error_msg = f"Error getting status for container {container_id}: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None

    async def delete_container(self, container_id: str) -> Tuple[bool, str]:
        """
        Delete a container via API.

        Args:
            container_id: Container identifier

        Returns:
            Tuple of (success, message)
        """
        url = f"{self.api_base_url}/api/v1/mcps/containers/{container_id}"

        try:
            self.logger.info(f"Deleting container via API: {container_id}")

            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                async with session.delete(url, headers=self._get_headers()) as response:
                    response_text = await response.text()

                    if response.status == 200:
                        response_data = await response.json()
                        success = response_data.get("success", False)
                        message = response_data.get("message", "Container deleted")

                        if success:
                            self.logger.info(
                                f"Successfully deleted container: {container_id}"
                            )
                        else:
                            self.logger.error(
                                f"Failed to delete container {container_id}: {message}"
                            )

                        return success, message
                    else:
                        error_msg = f"Container API returned status {response.status}: {response_text}"
                        self.logger.error(error_msg)
                        return False, error_msg

        except asyncio.TimeoutError:
            error_msg = f"Timeout deleting container {container_id} after {self.timeout} seconds"
            self.logger.error(error_msg)
            return False, error_msg

        except Exception as e:
            error_msg = f"Error deleting container {container_id}: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
