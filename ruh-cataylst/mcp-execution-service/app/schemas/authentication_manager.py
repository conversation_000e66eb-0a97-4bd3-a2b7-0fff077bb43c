from typing import Dict, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from app.schemas.client import AuthenticationError


class AuthenticationType(Enum):
    """Supported authentication types."""

    NONE = "none"
    BEARER = "bearer"
    API_KEY_HEADER = "api_key_header"
    API_KEY_QUERY = "api_key_query"
    API_KEY_BODY = "api_key_body"
    CUSTOM = "custom"


class TokenValidationError(AuthenticationError):
    """Token validation errors."""

    pass


@dataclass
class AuthenticationConfig:
    """Configuration for authentication schemes."""

    auth_type: AuthenticationType = AuthenticationType.NONE
    token: Optional[str] = None
    api_key: Optional[str] = None
    api_key_name: str = "X-API-Key"
    custom_headers: Dict[str, str] = field(default_factory=dict)
    query_params: Dict[str, str] = field(default_factory=dict)
    body_params: Dict[str, str] = field(default_factory=dict)
    token_refresh_callback: Optional[Callable[[], str]] = None
    token_expires_at: Optional[float] = None
