# main.py
import asyncio
import signal
import logging
import os
from typing import Optional

from app.core_.kafka_service import KafkaMCPService
from app.services.ssh_manager import initialize_global_ssh_key, cleanup_global_ssh_key
from app.core_.exceptions import MCPExecutorError, <PERSON>rror<PERSON>ategory, ErrorCode

logger = logging.getLogger(__name__)


async def main():
    """
    Main entry point for the Kafka MCP Executor Service.
    Initializes the service, starts the Kafka consumer in the background,
    and runs indefinitely until a shutdown signal is received.
    """
    # Refresh configuration on server startup to pick up latest environment variables
    from app.config.config import refresh_settings, settings

    logger.info("🔄 Refreshing configuration on server startup...")
    refresh_settings()

    # Initialize global SSH key manager if SSH key content is available
    if settings.ssh_key_content:
        logger.info("🔑 Initializing global SSH key manager...")
        try:
            initialize_global_ssh_key(settings.ssh_key_content)
            logger.info("✅ Global SSH key manager initialized successfully")
        except Exception as e:
            logger.error(
                f"❌ Failed to initialize global SSH key manager: {e}", exc_info=True
            )
            # Send service status notification about SSH initialization failure
            try:
                # Create a temporary service instance to send status update
                temp_service = KafkaMCPService()
                await temp_service._send_service_status_update(
                    "warning",
                    f"SSH key initialization failed: {str(e)}",
                    error_details={
                        "error_type": "ssh_initialization_error",
                        "exception_type": type(e).__name__,
                        "phase": "startup",
                    },
                )
            except Exception as status_error:
                logger.warning(
                    f"Failed to send SSH initialization error status: {status_error}"
                )
            # Continue without SSH key - individual connections will handle fallback
    else:
        logger.info(
            "ℹ️ No SSH key content found in settings, skipping global SSH key initialization"
        )

    logger.info(f"Initializing MCP Executor Service in process {os.getpid()}...")

    # Initialize service with error handling
    try:
        service = KafkaMCPService()
        logger.info("✅ MCP Executor Service initialized successfully")
    except Exception as e:
        logger.critical(
            f"❌ Failed to initialize MCP Executor Service: {e}", exc_info=True
        )
        # Send critical error notification
        try:
            temp_service = KafkaMCPService()
            await temp_service._send_service_status_update(
                "failed",
                f"Service initialization failed: {str(e)}",
                error_details={
                    "error_type": "service_initialization_error",
                    "exception_type": type(e).__name__,
                    "phase": "startup",
                },
            )
        except Exception as status_error:
            logger.warning(
                f"Failed to send service initialization error status: {status_error}"
            )
        raise MCPExecutorError(
            f"Service initialization failed: {str(e)}",
            error_category=ErrorCategory.SYSTEM_ERROR,
            error_code=ErrorCode.SERVICE_UNAVAILABLE,
            details={"phase": "initialization", "process_id": os.getpid()},
            retryable=False,
        ) from e

    loop = asyncio.get_running_loop()
    shutdown_future = loop.create_future()

    def signal_handler(signame):
        """Handles SIGINT and SIGTERM signals for graceful shutdown."""
        logger.warning(f"Received signal {signame}. Initiating graceful shutdown...")
        # Send shutdown notification
        try:
            asyncio.create_task(
                service._send_service_status_update(
                    "stopping",
                    f"Graceful shutdown initiated by signal {signame}",
                    error_details={"signal": signame, "phase": "shutdown"},
                )
            )
        except Exception as e:
            logger.warning(f"Failed to send shutdown notification: {e}")

        if not shutdown_future.done():
            shutdown_future.set_result(True)

    # Setup signal handlers with error handling
    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, signal_handler, sig.name)
            logger.debug(f"✅ Signal handler registered for {sig.name}")
        except NotImplementedError:
            logger.warning(
                f"Cannot add signal handler for {sig.name} on this platform."
            )
        except Exception as e:
            logger.error(f"Failed to register signal handler for {sig.name}: {e}")

    consumer_task = None
    try:
        logger.info("Starting Kafka consumer task in the background...")

        consumer_task = asyncio.create_task(
            service.start_consumer(), name="KafkaConsumerTask"
        )
        service._consumer_task = consumer_task

        logger.info("Service is now running and listening for Kafka messages.")
        logger.info("Press Ctrl+C or send SIGTERM to initiate graceful shutdown.")

        # Wait for shutdown signal
        await shutdown_future
        logger.info("Shutdown signal received, beginning graceful shutdown...")

    except asyncio.CancelledError:
        logger.info("Main task cancelled.")
        # Send cancellation notification
        try:
            await service._send_service_status_update(
                "stopping",
                "Main task cancelled",
                error_details={"phase": "cancellation"},
            )
        except Exception as e:
            logger.warning(f"Failed to send cancellation notification: {e}")
    except Exception as e:
        logger.error(f"Unexpected error in main execution: {e}", exc_info=True)
        # Send unexpected error notification
        try:
            await service._send_service_status_update(
                "error",
                f"Unexpected main execution error: {str(e)}",
                error_details={
                    "error_type": "main_execution_error",
                    "exception_type": type(e).__name__,
                    "phase": "runtime",
                },
            )
        except Exception as status_error:
            logger.warning(
                f"Failed to send main execution error status: {status_error}"
            )
        raise
    finally:
        logger.info("Shutdown sequence started...")

        # Send shutdown start notification
        try:
            await service._send_service_status_update(
                "stopping",
                "Shutdown sequence initiated",
                error_details={"phase": "cleanup_start"},
            )
        except Exception as e:
            logger.warning(f"Failed to send shutdown start notification: {e}")

        # Remove signal handlers
        for sig in (signal.SIGINT, signal.SIGTERM):
            try:
                loop.remove_signal_handler(sig)
                logger.debug(f"✅ Signal handler removed for {sig.name}")
            except (NotImplementedError, ValueError) as e:
                logger.debug(f"Signal handler removal not needed for {sig.name}: {e}")
            except Exception as e:
                logger.warning(f"Error removing signal handler for {sig.name}: {e}")

        # Clean up consumer task
        if consumer_task and not consumer_task.done():
            logger.info("Cancelling the Kafka consumer task...")
            consumer_task.cancel()
            try:
                await consumer_task
            except asyncio.CancelledError:
                logger.info("Consumer task successfully cancelled and cleaned up.")
            except Exception as e:
                logger.error(f"Error during consumer task cleanup: {e}", exc_info=True)
                # Send consumer cleanup error notification
                try:
                    await service._send_service_status_update(
                        "warning",
                        f"Consumer task cleanup error: {str(e)}",
                        error_details={
                            "error_type": "consumer_cleanup_error",
                            "exception_type": type(e).__name__,
                            "phase": "cleanup",
                        },
                    )
                except Exception as status_error:
                    logger.warning(
                        f"Failed to send consumer cleanup error status: {status_error}"
                    )
        else:
            logger.info("Consumer task was already completed or not started.")

        # Clean up global SSH key manager
        logger.info("🔑 Cleaning up global SSH key manager...")
        try:
            cleanup_global_ssh_key()
            logger.info("✅ Global SSH key manager cleaned up successfully")
        except Exception as e:
            logger.warning(
                f"⚠️ Failed to clean up global SSH key manager: {e}", exc_info=True
            )
            # Send SSH cleanup error notification
            try:
                await service._send_service_status_update(
                    "warning",
                    f"SSH cleanup error: {str(e)}",
                    error_details={
                        "error_type": "ssh_cleanup_error",
                        "exception_type": type(e).__name__,
                        "phase": "cleanup",
                    },
                )
            except Exception as status_error:
                logger.warning(
                    f"Failed to send SSH cleanup error status: {status_error}"
                )

        # Send final shutdown notification
        try:
            await service._send_service_status_update(
                "stopped",
                "Service shutdown complete",
                error_details={"phase": "cleanup_complete"},
            )
        except Exception as e:
            logger.warning(f"Failed to send final shutdown notification: {e}")

        logger.info("Service shutdown complete.")


if __name__ == "__main__":
    print("Starting service...")
    exit_code = 0

    try:
        asyncio.run(main())
        print("Service finished successfully.")
    except KeyboardInterrupt:
        logger.info("Process interrupted by KeyboardInterrupt (Ctrl+C).")
        print("Service interrupted by user.")
    except MCPExecutorError as mcp_error:
        logger.critical(f"MCP Executor Service failed: {mcp_error}", exc_info=True)
        print(f"Service failed: {mcp_error.message}")
        exit_code = 2  # Service-specific error
    except Exception as main_err:
        logger.critical(
            f"Service failed to run with unexpected error: {main_err}", exc_info=True
        )
        print(f"Service failed with unexpected error: {main_err}")
        exit_code = 1  # General error

    if exit_code != 0:
        logger.info(f"Service exiting with code {exit_code}")
        exit(exit_code)

    print("Service finished.")
