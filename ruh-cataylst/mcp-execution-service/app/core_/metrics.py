#!/usr/bin/env python3
"""
Metrics logging module for MCP Executor Service.

This module provides structured metrics logging for monitoring and observability.
Metrics are logged in a structured format that can be easily parsed by monitoring systems.
"""

import time
import logging
from typing import Dict, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime


class ExecutionType(Enum):
    """Execution type enumeration."""
    URL = "url"
    CONTAINER = "container"
    UNKNOWN = "unknown"


class ExecutionStatus(Enum):
    """Execution status enumeration."""
    SUCCESS = "success"
    FAILURE = "failure"
    TIMEOUT = "timeout"
    RETRY = "retry"


class MetricType(Enum):
    """Metric type enumeration."""
    COUNTER = "counter"
    TIMER = "timer"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"


@dataclass
class ExecutionMetric:
    """Execution metric data structure."""
    metric_type: str
    execution_type: str
    status: str
    duration_ms: Optional[float] = None
    attempt_count: Optional[int] = None
    error_type: Optional[str] = None
    mcp_id: Optional[str] = None
    user_id: Optional[str] = None
    tool_name: Optional[str] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()


@dataclass
class ConfigMetric:
    """MCP config metric data structure."""
    metric_type: str
    operation: str  # fetch, cache_hit, cache_miss, parse_error
    mcp_id: Optional[str] = None
    duration_ms: Optional[float] = None
    cache_hit: Optional[bool] = None
    error_type: Optional[str] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()


@dataclass
class AuthenticationMetric:
    """Authentication metric data structure."""
    metric_type: str
    operation: str  # validate, refresh, cache_hit, cache_miss
    status: str  # success, failure
    user_id: Optional[str] = None
    mcp_id: Optional[str] = None
    duration_ms: Optional[float] = None
    error_type: Optional[str] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()


class MetricsLogger:
    """Structured metrics logger for MCP Executor Service."""
    
    def __init__(self, logger_name: str = "mcp_executor_metrics"):
        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(logging.INFO)
        
        # Add a handler if none exists
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def _log_metric(self, metric: Union[ExecutionMetric, ConfigMetric, AuthenticationMetric]):
        """Log a structured metric."""
        metric_dict = asdict(metric)
        self.logger.info(f"METRIC: {metric_dict}")
    
    def log_execution_start(self, execution_type: ExecutionType, mcp_id: str, 
                           user_id: str, tool_name: str) -> float:
        """Log execution start and return start time."""
        start_time = time.time()
        
        metric = ExecutionMetric(
            metric_type=MetricType.COUNTER.value,
            execution_type=execution_type.value,
            status="started",
            mcp_id=mcp_id,
            user_id=user_id,
            tool_name=tool_name
        )
        self._log_metric(metric)
        
        return start_time
    
    def log_execution_success(self, execution_type: ExecutionType, start_time: float,
                             mcp_id: str, user_id: str, tool_name: str, 
                             attempt_count: int = 1):
        """Log successful execution."""
        duration_ms = (time.time() - start_time) * 1000
        
        metric = ExecutionMetric(
            metric_type=MetricType.TIMER.value,
            execution_type=execution_type.value,
            status=ExecutionStatus.SUCCESS.value,
            duration_ms=duration_ms,
            attempt_count=attempt_count,
            mcp_id=mcp_id,
            user_id=user_id,
            tool_name=tool_name
        )
        self._log_metric(metric)
    
    def log_execution_failure(self, execution_type: ExecutionType, start_time: float,
                             error_type: str, mcp_id: str, user_id: str, 
                             tool_name: str, attempt_count: int = 1):
        """Log failed execution."""
        duration_ms = (time.time() - start_time) * 1000
        
        metric = ExecutionMetric(
            metric_type=MetricType.TIMER.value,
            execution_type=execution_type.value,
            status=ExecutionStatus.FAILURE.value,
            duration_ms=duration_ms,
            attempt_count=attempt_count,
            error_type=error_type,
            mcp_id=mcp_id,
            user_id=user_id,
            tool_name=tool_name
        )
        self._log_metric(metric)
    
    def log_execution_retry(self, execution_type: ExecutionType, attempt_count: int,
                           error_type: str, mcp_id: str, user_id: str, tool_name: str):
        """Log execution retry."""
        metric = ExecutionMetric(
            metric_type=MetricType.COUNTER.value,
            execution_type=execution_type.value,
            status=ExecutionStatus.RETRY.value,
            attempt_count=attempt_count,
            error_type=error_type,
            mcp_id=mcp_id,
            user_id=user_id,
            tool_name=tool_name
        )
        self._log_metric(metric)
    
    def log_config_fetch(self, mcp_id: str, duration_ms: float, success: bool = True,
                        error_type: Optional[str] = None):
        """Log MCP config fetch operation."""
        metric = ConfigMetric(
            metric_type=MetricType.TIMER.value,
            operation="fetch",
            mcp_id=mcp_id,
            duration_ms=duration_ms,
            error_type=error_type if not success else None
        )
        self._log_metric(metric)
    
    def log_config_cache_hit(self, mcp_id: str):
        """Log MCP config cache hit."""
        metric = ConfigMetric(
            metric_type=MetricType.COUNTER.value,
            operation="cache_hit",
            mcp_id=mcp_id,
            cache_hit=True
        )
        self._log_metric(metric)
    
    def log_config_cache_miss(self, mcp_id: str):
        """Log MCP config cache miss."""
        metric = ConfigMetric(
            metric_type=MetricType.COUNTER.value,
            operation="cache_miss",
            mcp_id=mcp_id,
            cache_hit=False
        )
        self._log_metric(metric)
    
    def log_authentication_success(self, user_id: str, mcp_id: str, 
                                  operation: str = "validate", duration_ms: Optional[float] = None):
        """Log successful authentication."""
        metric = AuthenticationMetric(
            metric_type=MetricType.COUNTER.value,
            operation=operation,
            status="success",
            user_id=user_id,
            mcp_id=mcp_id,
            duration_ms=duration_ms
        )
        self._log_metric(metric)
    
    def log_authentication_failure(self, user_id: str, mcp_id: str, error_type: str,
                                  operation: str = "validate", duration_ms: Optional[float] = None):
        """Log failed authentication."""
        metric = AuthenticationMetric(
            metric_type=MetricType.COUNTER.value,
            operation=operation,
            status="failure",
            user_id=user_id,
            mcp_id=mcp_id,
            duration_ms=duration_ms,
            error_type=error_type
        )
        self._log_metric(metric)
    
    def log_container_lifecycle(self, operation: str, container_id: str, 
                               duration_ms: float, success: bool = True,
                               error_type: Optional[str] = None):
        """Log container lifecycle operations."""
        metric = ExecutionMetric(
            metric_type=MetricType.TIMER.value,
            execution_type=ExecutionType.CONTAINER.value,
            status=ExecutionStatus.SUCCESS.value if success else ExecutionStatus.FAILURE.value,
            duration_ms=duration_ms,
            error_type=error_type if not success else None,
            tool_name=f"container_{operation}"  # create, stop, delete
        )
        self._log_metric(metric)


# Global metrics logger instance
metrics_logger = MetricsLogger()


# Convenience functions for easy access
def log_execution_start(execution_type: ExecutionType, mcp_id: str, 
                       user_id: str, tool_name: str) -> float:
    """Log execution start."""
    return metrics_logger.log_execution_start(execution_type, mcp_id, user_id, tool_name)


def log_execution_success(execution_type: ExecutionType, start_time: float,
                         mcp_id: str, user_id: str, tool_name: str, 
                         attempt_count: int = 1):
    """Log execution success."""
    metrics_logger.log_execution_success(execution_type, start_time, mcp_id, 
                                        user_id, tool_name, attempt_count)


def log_execution_failure(execution_type: ExecutionType, start_time: float,
                         error_type: str, mcp_id: str, user_id: str, 
                         tool_name: str, attempt_count: int = 1):
    """Log execution failure."""
    metrics_logger.log_execution_failure(execution_type, start_time, error_type,
                                        mcp_id, user_id, tool_name, attempt_count)
