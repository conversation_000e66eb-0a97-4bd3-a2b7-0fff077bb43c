[tool.poetry]
name = "mcp-executor"
version = "0.1.0"
description = "Service responsible for excuting MCP servers"
authors = ["PratyushNag"]

[tool.poetry.dependencies]
python = "^3.11"
aiokafka = "^0.12.0"
werkzeug = "^3.1.3"
pydantic-settings = "^2.8.1"
aiohttp = "^3.10.0"
mcp = "^1.9.2"
httpx = "^0.28.1"
anyio = "^4.9.0"
fastapi = "^0.115.12"
uvicorn = "^0.34.3"


[tool.poetry.group.dev.dependencies]
requests = "^2.32.3"
pytest = "^8.4.0"
pytest-cov = "^6.2.1"
pytest-asyncio = "^1.0.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
