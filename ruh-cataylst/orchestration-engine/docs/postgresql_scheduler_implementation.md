# PostgreSQL Database Implementation for Orchestration Engine Scheduler

## Overview

This document outlines the implementation plan for integrating a PostgreSQL database with the Orchestration Engine to support scheduler services. The database will store task scheduling information, enabling the system to trigger workflows based on time-based schedules.

## Current Architecture

The Orchestration Engine currently:

- Uses Kafka for message passing between components
- Relies on Redis for state management and caching
- Executes workflows based on direct API requests or Kafka messages
- Has no persistent storage for scheduled tasks

## Implementation Goals

1. Create a PostgreSQL database to store:
   - Scheduled workflow execution tasks
   - Execution history and status tracking

2. Implement services to:
   - Schedule workflows for future execution
   - Track and manage scheduled tasks

3. Integrate with existing Kafka-based execution flow

## Database Schema Design

### Tables

#### 1. `scheduled_tasks`

```sql
CREATE TABLE scheduled_tasks (
    id SERIAL PRIMARY KEY,
    task_id UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
    workflow_id VARCHAR(255) NOT NULL,
    payload JSONB NOT NULL,
    schedule_type VARCHAR(50) NOT NULL, -- 'one-time', 'recurring'
    scheduled_time TIMESTAMP WITH TIME ZONE,
    recurrence_pattern VARCHAR(255), -- cron expression for recurring tasks
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'cancelled'
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_executed_at TIMESTAMP WITH TIME ZONE,
    next_execution_time TIMESTAMP WITH TIME ZONE,
    execution_count INTEGER DEFAULT 0,
    max_executions INTEGER, -- NULL for infinite
    metadata JSONB
);

CREATE INDEX idx_scheduled_tasks_status ON scheduled_tasks(status);
CREATE INDEX idx_scheduled_tasks_next_execution ON scheduled_tasks(next_execution_time)
    WHERE status = 'pending';
```

## Implementation Components

### 1. Database Configuration

Add PostgreSQL configuration to the existing `config.py`:

```python
# PostgreSQL settings
db_host: str = Field(..., alias="DB_HOST")
db_port: int = Field(..., alias="DB_PORT")
db_user: str = Field(..., alias="DB_USER")
db_password: SecretStr = Field(..., alias="DB_PASSWORD")
db_name: str = Field(..., alias="DB_NAME")

@computed_field
@property
def sqlalchemy_database_uri(self) -> str:
    """Build the PostgreSQL connection string."""
    password = self.db_password.get_secret_value() if self.db_password else ""
    return f"postgresql://{self.db_user}:{password}@{self.db_host}:{self.db_port}/{self.db_name}"
```

Update `.env.example` with:

```env
# PostgreSQL settings
DB_HOST="localhost"
DB_PORT=5432
DB_USER="postgres"
DB_PASSWORD="your_password"
DB_NAME="orchestration_engine"
```

### 2. Database Models

Create SQLAlchemy models in `app/models/scheduler.py`:

```python
from sqlalchemy import Column, Integer, String, DateTime, JSON
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import uuid

Base = declarative_base()

class ScheduledTask(Base):
    __tablename__ = "scheduled_tasks"

    id = Column(Integer, primary_key=True)
    task_id = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4)
    workflow_id = Column(String(255), nullable=False)
    payload = Column(JSONB, nullable=False)
    schedule_type = Column(String(50), nullable=False)
    scheduled_time = Column(DateTime(timezone=True))
    recurrence_pattern = Column(String(255))
    status = Column(String(50), nullable=False, default="pending")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_executed_at = Column(DateTime(timezone=True))
    next_execution_time = Column(DateTime(timezone=True))
    execution_count = Column(Integer, default=0)
    max_executions = Column(Integer)
    metadata = Column(JSONB)
```

### 3. Database Session Management

Create a database session manager in `app/db/session.py`:

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.config.config import settings

engine = create_engine(settings.sqlalchemy_database_uri)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

### 4. Scheduler Service

Create a scheduler service in `app/services/scheduler_service.py`:

```python
import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from croniter import croniter

from app.db.session import get_db
from app.models.scheduler import ScheduledTask
from app.services.kafka_service import KafkaService
from app.config.config import settings

logger = logging.getLogger(__name__)

class SchedulerService:
    """Service for managing scheduled tasks and executing them at the appropriate time."""

    def __init__(self, kafka_service: KafkaService):
        self.kafka_service = kafka_service
        self.running = False
        self.poll_interval = 10  # seconds

    async def start(self):
        """Start the scheduler service."""
        self.running = True
        await self._scheduler_loop()

    async def stop(self):
        """Stop the scheduler service."""
        self.running = False

    async def _scheduler_loop(self):
        """Main scheduler loop that checks for tasks to execute."""
        while self.running:
            try:
                # Get tasks that need to be executed
                db = next(get_db())
                now = datetime.now(timezone.utc)

                # Find tasks that are due for execution
                tasks = db.query(ScheduledTask).filter(
                    ScheduledTask.status == "pending",
                    ScheduledTask.next_execution_time <= now
                ).all()

                for task in tasks:
                    # Process the task
                    await self._execute_task(db, task)

                # Update recurring tasks
                self._update_recurring_tasks(db)

                db.close()
            except Exception as e:
                logger.error(f"Error in scheduler loop: {str(e)}")

            # Wait before next poll
            await asyncio.sleep(self.poll_interval)

    async def _execute_task(self, db: Session, task: ScheduledTask):
        """Execute a scheduled task by sending it to Kafka."""
        try:
            # Update task status
            task.last_executed_at = datetime.now(timezone.utc)
            task.execution_count += 1

            # Send to Kafka
            await self.kafka_service.send_workflow_request(
                workflow_id=task.workflow_id,
                payload=task.payload
            )

            # Update task status based on recurrence
            if task.schedule_type == "one-time":
                task.status = "completed"
            elif task.max_executions and task.execution_count >= task.max_executions:
                task.status = "completed"

            db.commit()
            logger.info(f"Executed scheduled task {task.task_id} for workflow {task.workflow_id}")
        except Exception as e:
            logger.error(f"Failed to execute task {task.task_id}: {str(e)}")
            task.status = "failed"
            db.commit()

    def _update_recurring_tasks(self, db: Session):
        """Update next execution time for recurring tasks."""
        try:
            # Get all active recurring tasks
            tasks = db.query(ScheduledTask).filter(
                ScheduledTask.status == "pending",
                ScheduledTask.schedule_type == "recurring",
                ScheduledTask.recurrence_pattern.isnot(None)
            ).all()

            now = datetime.now(timezone.utc)

            for task in tasks:
                if task.last_executed_at and task.recurrence_pattern:
                    # Calculate next execution time using croniter
                    cron = croniter(task.recurrence_pattern, task.last_executed_at)
                    next_time = cron.get_next(datetime)
                    task.next_execution_time = next_time

            db.commit()
        except Exception as e:
            logger.error(f"Error updating recurring tasks: {str(e)}")
            db.rollback()
```

## Integration with Existing Kafka Flow

The new PostgreSQL database and associated scheduler service will integrate with the existing Kafka-based workflow execution system:

1. The `SchedulerService` will:
   - Monitor scheduled tasks in the database
   - When a task is due, send a workflow request to Kafka
   - The existing `KafkaWorkflowConsumer` will process these requests like any other workflow request

## Kafka Message Format for Scheduled Tasks

The message format sent to Kafka will be compatible with the existing format expected by the `KafkaWorkflowConsumer`:

```json
{
  "data": {
    "workflow_id": "123",
    "payload": {
      "user_dependent_fields": ["field1", "field2"],
      "user_payload_template": {
        "field1": "value1",
        "field2": "value2",
        "scheduled_task_id": "uuid-of-task",
        "execution_time": "2023-07-15T10:00:00Z"
      }
    }
  }
}
```

## Implementation Steps

1. **Database Setup**:
   - Add PostgreSQL configuration to settings
   - Create database migration scripts
   - Set up SQLAlchemy models

2. **Service Implementation**:
   - Implement `SchedulerService` for time-based triggers
   - Create API endpoints for managing scheduled tasks

3. **Integration**:
   - Modify `main.py` to initialize and start the scheduler service
   - Ensure Kafka message format compatibility

4. **Testing**:
   - Create unit tests for database models and services
   - Create integration tests for the complete flow

## Conclusion

This implementation will enhance the Orchestration Engine with scheduled task execution capabilities, while maintaining compatibility with the existing Kafka-based workflow execution system. The PostgreSQL database provides persistent storage for scheduling information, enabling time-based workflow automation scenarios.
