# MCP Executor Service Modernization Task List

## 🎯 Progress Status

- **Phase 1**: ✅ **COMPLETED** (3/3 tasks)
- **Phase 2**: ✅ **COMPLETED** (2/2 tasks)
- **Phase 3**: ⏳ **PENDING** (0/2 tasks)
- **Phase 4**: ⏳ **PENDING** (0/2 tasks)
- **Overall Progress**: 5/9 tasks completed (56%)

## Overview

The orchestration-engine currently contains outdated MCP execution logic that includes:

1. Hardcoded server_script_path requirements and validation
2. Unnecessary MCP config fetching for server script paths
3. Hardcoded error handling in transition handlers
4. Outdated tool execution logic that relies on server_script_path

The MCP executor service has been modernized to use smart routing with priority-based config selection (streamable-http > stdio > sse), making server_script_path redundant for execution.

**Strategy**: Remove server_script_path from execution logic while maintaining schema compatibility for frontend/workflow-service.

## Current Issues Identified

### 1. Transition Handler Issues

- **File**: `app/core_/transition_handler.py`
- **Lines**: 155-158
- **Issue**: Warning logged when server_script_path is missing, but execution continues
- **Problem**: Unnecessary warning and outdated logic

### 2. Kafka Tool Executor Issues

- **File**: `app/services/kafka_tool_executor.py`
- **Lines**: 257-259, method signature
- **Issue**: Still includes server_script_path in payload and user_id is optional
- **Problem**:
  - Perpetuates outdated execution pattern - MCP executor no longer needs server_script_path
  - user_id should be required for all MCP executions but is currently optional

### 3. Tool Execution Parameter Passing

- **File**: `app/core_/transition_handler.py`
- **Lines**: 275-289
- **Issue**: Still passes server_script_path to executors and user_id may not always be passed
- **Problem**:
  - Executors no longer need server_script_path parameter for modern MCP execution
  - user_id should be required for all tool executions but may not always be passed

### 4. Node URL Service Issues (If exists)

- **Files**: Any services that fetch URLs based on server_script_path
- **Issue**: Unnecessary URL fetching logic for MCP execution
- **Problem**: Redundant operations that slow down execution

## Task List

### Phase 1: Remove server_script_path from Execution Logic

#### Task 1.1: Update Transition Handler - Remove Warning Logic

- [x] **File**: `app/core_/transition_handler.py`
- [x] **Action**: Remove server_script_path warning and validation logic
- [x] **Lines to modify**: 155-158
- [x] **Changes**:
  - [x] Remove the warning log when server_script_path is missing
  - [x] Remove the comment about smart routing
  - [x] Remove the server_script_path variable assignment entirely

#### Task 1.2: Update Transition Handler - Remove Parameter Passing and Ensure user_id

- [x] **File**: `app/core_/transition_handler.py`
- [x] **Action**: Stop passing server_script_path to executors and ensure user_id is passed
- [x] **Lines to modify**: 275-289
- [x] **Changes**:
  - [x] Remove server_script_path parameter from all execute_tool calls
  - [x] **Ensure user_id is always passed to execute_tool calls**
  - [x] Update executor calls to use mcp_id, tool_name, tool_parameters, and user_id
  - [x] Clean up any server_script_path variable usage in execution logic
  - [x] Add user_id extraction from execution context if not already available

#### Task 1.3: Update Kafka Tool Executor - Remove server_script_path and Require user_id

- [x] **File**: `app/services/kafka_tool_executor.py`
- [x] **Action**: Remove server_script_path from payload and make user_id required
- [x] **Lines to modify**: 257-259, method signature, payload construction
- [x] **Changes**:
  - [x] Remove server_script_path parameter from execute_tool method signature
  - [x] Remove server_script_path from payload construction
  - [x] Remove all server_script_path related logic
  - [x] **Make user_id a required parameter in execute_tool method**
  - [x] **Always include user_id in payload (throw error if missing)**
  - [x] Update method signature to require user_id parameter
  - [x] **No backward compatibility needed for execution**

### Phase 2: Clean Up Error Handling and URL Fetching

#### Task 2.1: Remove Hardcoded Error Patterns

- [x] **File**: `app/core_/transition_handler.py`
- [x] **Action**: Review and remove any hardcoded server_script_path error handling
- [x] **Lines to review**: Throughout the file
- [x] **Changes**:
  - [x] Remove any exceptions thrown specifically for missing server_script_path
  - [x] Update error messages to be more generic
  - [x] Ensure MCP execution relies on mcp_id instead of server_script_path

#### Task 2.2: Remove Node URL Fetching Logic (If exists)

- [x] **Files**: `app/services/node_url_service.py` or similar URL fetching services
- [x] **Action**: Remove any URL fetching logic based on server_script_path
- [x] **Changes**:
  - [x] Remove methods that fetch URLs from server_script_path ✅ **FILE DELETED**
  - [x] Remove any caching or URL resolution logic ✅ **FILE DELETED**
  - [x] Clean up imports and dependencies ✅ **TESTS UPDATED**

### Phase 3: Update Documentation and Tests

#### Task 3.1: Update Technical Documentation

- [ ] **Files**:
  - [ ] `docs/technical_requirements.md`
  - [ ] `docs/context_llms.md`
- [ ] **Action**: Update MCP execution flow documentation
- [ ] **Changes**:
  - [ ] Update MCP execution flow to show mcp_id-based execution
  - [ ] Remove server_script_path from execution examples
  - [ ] Add notes about modern MCP execution using smart routing
  - [ ] **Keep schema examples unchanged for backward compatibility**

#### Task 3.2: Update Test Cases

- [ ] **Files**: Test files in `tests/` directory related to execution
- [ ] **Action**: Update execution tests to not use server_script_path
- [ ] **Changes**:
  - [ ] Update test cases that test tool execution logic
  - [ ] Remove server_script_path from execution test data
  - [ ] Add tests for modern MCP execution flow
  - [ ] **Keep schema validation tests unchanged**

### Phase 4: Validation and Cleanup

#### Task 4.1: Validate Execution Compatibility

- [ ] **Action**: Ensure MCP execution works with new logic
- [ ] **Tests needed**:
  - [ ] MCP execution works without server_script_path
  - [ ] MCP execution requires and uses user_id correctly
  - [ ] All execution types (MCP, Components, Agent) work correctly
  - [ ] Error handling works properly without server_script_path dependencies
  - [ ] Error handling works when user_id is missing

#### Task 4.2: Performance Testing

- [ ] **Action**: Verify performance improvement from removing redundant logic
- [ ] **Tests needed**:
  - [ ] MCP execution latency (should be faster)
  - [ ] Workflow processing speed
  - [ ] Memory usage patterns

## Implementation Priority

### High Priority (Immediate)

1. [x] Task 1.1: Remove transition handler warning logic ✅ **COMPLETED**
2. [x] Task 1.2: Remove server_script_path parameter passing ✅ **COMPLETED**
3. [x] Task 1.3: Remove server_script_path from Kafka tool executor ✅ **COMPLETED**
4. [x] Task 2.1: Clean up hardcoded error handling ✅ **COMPLETED**

### Medium Priority (Next Sprint)

1. [x] Task 2.2: Remove node URL fetching logic ✅ **COMPLETED**
2. [ ] Task 3.1: Update documentation
3. [ ] Task 4.1: Validate execution compatibility

### Low Priority (Future)

1. [ ] Task 3.2: Update test cases
2. [ ] Task 4.2: Performance testing

## Risk Assessment

### Low Risk

- Removing warning logs
- Documentation updates
- Removing server_script_path from execution logic (MCP executor already handles this)

### Medium Risk

- Updating error handling patterns
- Removing URL fetching logic

### High Risk

- None (schema and workflow converter remain unchanged for backward compatibility)

## Success Criteria

1. ✅ No server_script_path logic in execution flow
2. ✅ user_id is required for all tool executions
3. ✅ MCP execution uses modern smart routing exclusively
4. ✅ Workflow schemas remain backward compatible (frontend unaffected)
5. ✅ Clean, maintainable execution code without legacy patterns
6. ✅ Performance improvement from removing redundant logic

## Notes

- **Schema Compatibility**: Workflow schema converter and transition schema remain unchanged
- **Frontend Compatibility**: No changes needed in frontend or workflow-service
- **Execution Modernization**: Only execution logic is updated, not data structures
- **No Backward Compatibility for Execution**: Tool execution will not support server_script_path
- **MCP Executor Ready**: The MCP executor service already implements modern execution flow
