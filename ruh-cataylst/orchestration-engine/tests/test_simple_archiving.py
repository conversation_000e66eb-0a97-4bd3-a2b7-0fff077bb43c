"""
Simple test script for PostgreSQL archiving functionality.
This script tests the ability to archive data directly to PostgreSQL.

Usage:
    poetry run python test_simple_archiving.py
"""

import json
import time
import uuid
import sys
import logging
from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.core_.state_manager import WorkflowStateManager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("SimpleArchivingTest")

def test_simple_archiving():
    """
    Test the direct archiving functionality.
    
    This test:
    1. Creates a unique workflow ID
    2. Initializes a workflow state manager
    3. Directly calls the archive methods
    4. Verifies the data is archived to PostgreSQL
    """
    # Generate a unique workflow ID for this test
    workflow_id = f"test-workflow-{uuid.uuid4()}"
    correlation_id = workflow_id  # Using the same ID for simplicity
    
    logger.info(f"Starting test with workflow ID: {workflow_id}")
    
    # Initialize Redis managers
    results_redis_manager = RedisManager(
        db_index=int(settings.redis_results_db_index)
    )
    state_redis_manager = RedisManager(
        db_index=int(settings.redis_state_db_index)
    )
    
    # Initialize PostgreSQL manager
    postgres_manager = get_postgres_manager()
    
    # Initialize workflow state manager
    state_manager = WorkflowStateManager(workflow_id=workflow_id)
    state_manager.results_redis_manager = results_redis_manager
    state_manager.state_redis_manager = state_redis_manager
    state_manager.postgres_manager = postgres_manager
    
    # Create test data
    workflow_state = {
        "pending_transitions": ["transition1", "transition2"],
        "completed_transitions": [],
        "waiting_transitions": [],
        "terminated": False,
        "paused": False
    }
    
    transition_id = f"transition1-{uuid.uuid4()}"
    transition_result = {
        "status": "success",
        "result": {
            "data": "Test result data",
            "timestamp": time.time()
        }
    }
    
    # Set up the state manager with the test data
    state_manager.pending_transitions = set(workflow_state["pending_transitions"])
    state_manager.completed_transitions = set(workflow_state["completed_transitions"])
    state_manager.waiting_transitions = set(workflow_state["waiting_transitions"])
    state_manager.terminated = workflow_state["terminated"]
    state_manager.workflow_paused = workflow_state["paused"]
    
    # Store transition result in memory
    state_manager.transition_results[transition_id] = transition_result
    
    # Directly call the archive methods
    logger.info("Directly calling archive methods...")
    
    # Archive transition result
    success = state_manager.archive_transition_result(transition_id)
    if success:
        logger.info(f"Successfully archived transition result for {transition_id}")
    else:
        logger.error(f"Failed to archive transition result for {transition_id}")
        return False
    
    # Archive workflow state
    success = state_manager.archive_workflow_state()
    if success:
        logger.info(f"Successfully archived workflow state for {workflow_id}")
    else:
        logger.error(f"Failed to archive workflow state for {workflow_id}")
        return False
    
    # Verify data was archived to PostgreSQL
    logger.info("Checking if data was archived to PostgreSQL...")
    
    # Check for transition result in PostgreSQL
    pg_transition_result = postgres_manager.get_transition_result(correlation_id, transition_id)
    
    if pg_transition_result:
        logger.info("Transition result successfully archived to PostgreSQL:")
        logger.info(json.dumps(pg_transition_result, indent=2))
    else:
        logger.error("Failed to find transition result in PostgreSQL")
        return False
    
    # Check for workflow state in PostgreSQL
    pg_workflow_state = postgres_manager.get_workflow_state(correlation_id)
    
    if pg_workflow_state:
        logger.info("Workflow state successfully archived to PostgreSQL:")
        logger.info(json.dumps(pg_workflow_state, indent=2))
    else:
        logger.error("Failed to find workflow state in PostgreSQL")
        return False
    
    logger.info("Test completed successfully!")
    return True

if __name__ == "__main__":
    logger.info("Starting simple archiving test...")
    
    try:
        success = test_simple_archiving()
        if success:
            logger.info("✅ Test passed: Direct archiving to PostgreSQL works correctly")
            sys.exit(0)
        else:
            logger.error("❌ Test failed: Direct archiving to PostgreSQL did not work as expected")
            sys.exit(1)
    except Exception as e:
        logger.exception(f"❌ Test failed with exception: {e}")
        sys.exit(1)
