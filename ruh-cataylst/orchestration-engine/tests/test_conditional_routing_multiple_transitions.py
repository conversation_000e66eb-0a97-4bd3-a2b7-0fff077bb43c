"""
Test suite for conditional routing handler multiple transitions support.

This test suite validates the orchestration engine's ability to handle
multiple simultaneous transitions from conditional components.

Following TDD methodology with comprehensive test coverage.
"""

import pytest
import logging
from unittest.mock import Mock
from app.core_.conditional_routing_handler import ConditionalRoutingHandler


class TestConditionalRoutingMultipleTransitions:
    """Test class for conditional routing handler multiple transitions support."""

    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.logger = Mock(spec=logging.Logger)
        self.handler = ConditionalRoutingHandler(logger=self.logger)

    @pytest.mark.asyncio
    async def test_handle_single_transition_legacy_format(self):
        """Test handling of legacy single transition format."""
        execution_result = {
            "status": "success",
            "routing_decision": {
                "target_transition": "transition_1",
                "matched_condition": 1,
                "condition_result": True,
                "execution_time_ms": 15.5
            }
        }
        
        transition = {"id": "test_transition"}
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        assert result == ["transition_1"]
        self.logger.info.assert_called()

    @pytest.mark.asyncio
    async def test_handle_multiple_transitions_new_format(self):
        """Test handling of new multiple transitions format."""
        execution_result = {
            "status": "success",
            "routing_decision": {
                "target_transitions": ["transition_1", "transition_2", "transition_3"],
                "matched_conditions": [1, 2, 3],
                "condition_result": True,
                "execution_time_ms": 25.8,
                "evaluation_strategy": "all_matches"
            }
        }
        
        transition = {"id": "test_transition"}
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        assert result == ["transition_1", "transition_2", "transition_3"]
        self.logger.info.assert_called()

    @pytest.mark.asyncio
    async def test_handle_single_transition_new_format(self):
        """Test handling of single transition in new array format."""
        execution_result = {
            "status": "success",
            "routing_decision": {
                "target_transitions": ["transition_1"],
                "matched_conditions": [1],
                "condition_result": True,
                "execution_time_ms": 12.3,
                "evaluation_strategy": "all_matches"
            }
        }
        
        transition = {"id": "test_transition"}
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        assert result == ["transition_1"]
        self.logger.info.assert_called()

    @pytest.mark.asyncio
    async def test_handle_no_matches_new_format(self):
        """Test handling of no matches with default transition in new format."""
        execution_result = {
            "status": "success",
            "routing_decision": {
                "target_transitions": ["default_transition"],
                "matched_conditions": [],
                "condition_result": False,
                "execution_time_ms": 8.1,
                "evaluation_strategy": "all_matches"
            }
        }
        
        transition = {"id": "test_transition"}
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        assert result == ["default_transition"]
        self.logger.info.assert_called()

    @pytest.mark.asyncio
    async def test_handle_missing_routing_decision(self):
        """Test handling of execution result without routing decision."""
        execution_result = {
            "status": "success",
            "some_other_data": "value"
        }
        
        transition = {"id": "test_transition"}
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        assert result == []
        self.logger.warning.assert_called()

    @pytest.mark.asyncio
    async def test_handle_empty_target_transitions(self):
        """Test handling of empty target transitions array."""
        execution_result = {
            "status": "success",
            "routing_decision": {
                "target_transitions": [],
                "matched_conditions": [],
                "condition_result": False,
                "execution_time_ms": 5.0
            }
        }
        
        transition = {"id": "test_transition"}
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        assert result == []
        self.logger.warning.assert_called()

    @pytest.mark.asyncio
    async def test_handle_invalid_target_transitions_format(self):
        """Test handling of invalid target_transitions format (not a list)."""
        execution_result = {
            "status": "success",
            "routing_decision": {
                "target_transitions": "not_a_list",
                "matched_conditions": [1],
                "condition_result": True,
                "execution_time_ms": 10.0
            }
        }
        
        transition = {"id": "test_transition"}
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        assert result == []
        self.logger.warning.assert_called()

    @pytest.mark.asyncio
    async def test_handle_target_transitions_with_none_values(self):
        """Test handling of target_transitions array containing None values."""
        execution_result = {
            "status": "success",
            "routing_decision": {
                "target_transitions": ["transition_1", None, "transition_2", None],
                "matched_conditions": [1, 3],
                "condition_result": True,
                "execution_time_ms": 18.7
            }
        }
        
        transition = {"id": "test_transition"}
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        # None values should be filtered out
        assert result == ["transition_1", "transition_2"]
        self.logger.info.assert_called()

    @pytest.mark.asyncio
    async def test_handle_exception_during_processing(self):
        """Test error handling when exception occurs during processing."""
        execution_result = {
            "status": "success",
            "routing_decision": {
                "target_transitions": ["transition_1"]
            }
        }
        
        # Create a transition that will cause an exception when accessing 'id'
        transition = Mock()
        transition.get.side_effect = Exception("Test exception")
        
        result = await self.handler.handle_conditional_result(execution_result, transition)
        
        assert result == []
        self.logger.error.assert_called()

    def test_extract_target_transitions_new_format(self):
        """Test _extract_target_transitions with new array format."""
        routing_decision = {
            "target_transitions": ["transition_1", "transition_2"],
            "matched_conditions": [1, 2]
        }
        
        result = self.handler._extract_target_transitions(routing_decision, "test_id")
        
        assert result == ["transition_1", "transition_2"]

    def test_extract_target_transitions_legacy_format(self):
        """Test _extract_target_transitions with legacy single format."""
        routing_decision = {
            "target_transition": "transition_1",
            "matched_condition": 1
        }
        
        result = self.handler._extract_target_transitions(routing_decision, "test_id")
        
        assert result == ["transition_1"]

    def test_extract_target_transitions_no_targets(self):
        """Test _extract_target_transitions with no target information."""
        routing_decision = {
            "matched_conditions": [],
            "condition_result": False
        }
        
        result = self.handler._extract_target_transitions(routing_decision, "test_id")
        
        assert result == []

    def test_log_routing_decision_single_transition(self):
        """Test _log_routing_decision for single transition."""
        routing_decision = {
            "matched_condition": 1,
            "condition_result": True,
            "execution_time_ms": 15.5
        }
        
        self.handler._log_routing_decision(routing_decision, "test_id", ["transition_1"])
        
        self.logger.info.assert_called()
        call_args = self.logger.info.call_args[0][0]
        assert "target='transition_1'" in call_args
        assert "matched_condition=1" in call_args

    def test_log_routing_decision_multiple_transitions(self):
        """Test _log_routing_decision for multiple transitions."""
        routing_decision = {
            "matched_conditions": [1, 2, 3],
            "condition_result": True,
            "execution_time_ms": 25.8,
            "evaluation_strategy": "all_matches"
        }
        
        self.handler._log_routing_decision(
            routing_decision, 
            "test_id", 
            ["transition_1", "transition_2", "transition_3"]
        )
        
        self.logger.info.assert_called()
        call_args = self.logger.info.call_args[0][0]
        assert "targets=['transition_1', 'transition_2', 'transition_3']" in call_args
        assert "matched_conditions=[1, 2, 3]" in call_args
        assert "strategy=all_matches" in call_args
