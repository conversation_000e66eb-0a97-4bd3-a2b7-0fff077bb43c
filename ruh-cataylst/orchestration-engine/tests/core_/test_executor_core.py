import pytest
import json
from unittest.mock import AsyncMock
from jsonschema import ValidationError
from app.core_.executor_core import EnhancedWorkflowEngine
from app.core_.state_manager import WorkflowStateManager
from app.core_.workflow_utils import WorkflowUtils
from app.core_.transition_handler import <PERSON>ionHandler
from app.services.initialize_workflow import initialize_workflow_with_params


@pytest.fixture
def sample_workflow():
    """
    Provides a minimal valid workflow definition for testing.
    """

    with open("./app/shared/json_schemas/ciny.json", "r") as file:
        workflow = json.load(file)

    workflow_json_content = {
        "workflow_id": "ciny",
        "payload": {
            "user_dependent_fields": ["topic", "video_type", "keywords", "voice_id"],
            "user_payload_template": {
                "topic": "latest nvidia event updates",
                "video_type": "SHORT",
                "keywords": "30 Seconds",
                "voice_id": "test-voice-id",
            },
        },
    }

    init_workflow = initialize_workflow_with_params(workflow, workflow_json_content)
    return init_workflow


@pytest.fixture
def mock_tool_executor():
    """
    Provides a mock tool executor for testing.
    """
    return AsyncMock()


@pytest.fixture
def mock_result_callback():
    """
    Provides a mock result callback for testing.
    """
    return AsyncMock()


class TestEnhancedWorkflowEngine:
    """
    Test suite for EnhancedWorkflowEngine class.
    """

    @pytest.mark.asyncio
    async def test_initialization(
        self, sample_workflow, mock_tool_executor, mock_result_callback
    ):
        """
        Test successful initialization of EnhancedWorkflowEngine.
        """
        engine = EnhancedWorkflowEngine(
            init_workflow=sample_workflow,
            tool_executor=mock_tool_executor,
            result_callback=mock_result_callback,
            workflow_id="ciny",
        )
        assert engine.workflow_id == "ciny"
        assert engine.schema == sample_workflow
        assert isinstance(engine.workflow_utils, WorkflowUtils)
        assert isinstance(engine.state_manager, WorkflowStateManager)
        assert isinstance(engine.transition_handler, TransitionHandler)

    @pytest.mark.asyncio
    async def test_initialization_with_invalid_workflow(
        self, mock_tool_executor, mock_result_callback
    ):
        """
        Test initialization with invalid workflow structure.
        """
        invalid_workflow = {
            "nodes": [
                {
                    "id": "node1",
                    "server_script_path": "/path/to/script1.py",
                    "server_tools": [
                        {
                            "tool_id": 1,
                            "tool_name": "ToolA",
                            "endpoint": "/api/toolA",
                            "input_schema": {
                                "predefined_fields": [
                                    {
                                        "field_name": "input1",
                                        "data_type": {"type": "string"},
                                        "required": True,
                                    }
                                ]
                            },
                            "output_schema": {
                                "predefined_fields": [
                                    {
                                        "field_name": "output1",
                                        "data_type": {"type": "number"},
                                    }
                                ]
                            },
                        }
                    ],
                }
            ],
            "transitions": [
                {
                    "id": "transition1",
                    "sequence": 1,
                    "transition_type": "standard",
                    "execution_type": "MCP server",
                    "node_info": {
                        "node_id": "node1",
                        "tools_to_use": [
                            {
                                "tool_id": 1,
                                "tool_name": "ToolA",
                                "tool_params": {
                                    "items": [
                                        {
                                            "field_name": "input1",
                                            "data_type": "string",
                                            "field_value": 123,  # Invalid: Should be a string
                                        }
                                    ]
                                },
                            }
                        ],
                        "input_data": [
                            {
                                "from_transition_id": "initial",
                                "target_node_id": "node1",
                                "data_type": "string",
                            }
                        ],
                        "output_data": [
                            {
                                "to_transition_id": "transition2",
                                "target_node_id": "node2",
                                "data_type": "number",
                            }
                        ],
                    },
                    "end": False,
                },
                {
                    "id": "transition2",
                    "sequence": 2,
                    "transition_type": "standard",
                    "execution_type": "API request",
                    "node_info": {
                        "node_id": "node2",
                        "tools_to_use": [
                            {
                                "tool_id": 2,
                                "tool_name": "ToolB",
                                "tool_params": {
                                    "items": [
                                        {
                                            "field_name": "inputB",
                                            "data_type": "boolean",
                                            "field_value": "true",  # Invalid: Should be a boolean
                                        }
                                    ]
                                },
                            }
                        ],
                        "input_data": [
                            {
                                "from_transition_id": "transition1",
                                "target_node_id": "node2",
                                "data_type": "number",
                            }
                        ],
                        "output_data": [
                            {
                                "to_transition_id": None,  # Invalid: Should be a string if not the final transition and no conditional routing
                                "target_node_id": "node3",
                                "data_type": "string",
                            }
                        ],
                    },
                    "end": True,
                },
            ],
        }

        with pytest.raises(ValidationError):
            EnhancedWorkflowEngine(
                init_workflow=invalid_workflow,
                tool_executor=mock_tool_executor,
                result_callback=mock_result_callback,
            )

    @pytest.mark.asyncio
    async def test_execute_workflow(
        self, sample_workflow, mock_tool_executor, mock_result_callback
    ):
        """
        Test successful execution of a workflow.
        """
        # Arrange
        engine = EnhancedWorkflowEngine(
            init_workflow=sample_workflow,
            tool_executor=mock_tool_executor,
            result_callback=mock_result_callback,
            workflow_id="ciny",
        )

        # Mock the transition handler's _execute_transition_with_tracking method
        engine.transition_handler._execute_transition_with_tracking = AsyncMock(
            return_value=["next_transition_id"]
        )
        engine.state_manager.is_workflow_active = (
            lambda: False
        )  # Make the workflow terminate after one iteration

        # Act
        await engine.execute()

        # Assert
        engine.transition_handler._execute_transition_with_tracking.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_workflow_with_state(
        self, sample_workflow, mock_tool_executor, mock_result_callback
    ):
        """
        Test execution of a workflow with a specific state.
        """
        # Arrange
        engine = EnhancedWorkflowEngine(
            init_workflow=sample_workflow,
            tool_executor=mock_tool_executor,
            result_callback=mock_result_callback,
            workflow_id="ciny",
        )

        # Mock the transition handler's _execute_transition_with_tracking method
        engine.transition_handler._execute_transition_with_tracking = AsyncMock(
            return_value=["next_transition_id"]
        )
        engine.state_manager.is_workflow_active = (
            lambda: False
        )  # Make the workflow terminate after one iteration

        # Act
        await engine.execute(state="custom_state")

        # Assert
        engine.transition_handler._execute_transition_with_tracking.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_workflow_with_error(
        self, sample_workflow, mock_tool_executor, mock_result_callback
    ):
        """
        Test execution of a workflow when an error occurs.
        """
        # Arrange
        engine = EnhancedWorkflowEngine(
            init_workflow=sample_workflow,
            tool_executor=mock_tool_executor,
            result_callback=mock_result_callback,
            workflow_id="ciny",
        )

        # Mock the transition handler's _execute_transition_with_tracking method to raise an exception
        engine.transition_handler._execute_transition_with_tracking = AsyncMock(
            side_effect=Exception("Test error")
        )
        engine.state_manager.is_workflow_active = (
            lambda: True
        )  # Keep the workflow active

        # Act & Assert
        with pytest.raises(Exception, match="Test error"):
            await engine.execute()

    @pytest.mark.asyncio
    async def test_execute_workflow_with_no_initial_transition(
        self, sample_workflow, mock_tool_executor, mock_result_callback
    ):
        """
        Test execution of a workflow when no initial transition is found.
        """
        # Arrange
        engine = EnhancedWorkflowEngine(
            init_workflow=sample_workflow,
            tool_executor=mock_tool_executor,
            result_callback=mock_result_callback,
            workflow_id="ciny",
        )

        # Mock the transition handler's _find_initial_transition method to return None
        engine.transition_handler._find_initial_transition = lambda: None

        # Act & Assert
        with pytest.raises(Exception, match="No initial transition found"):
            await engine.execute()
