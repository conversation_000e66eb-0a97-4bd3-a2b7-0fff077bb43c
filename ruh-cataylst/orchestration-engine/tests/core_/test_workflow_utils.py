import pytest
from unittest.mock import Mock, AsyncMock, patch
from jsonschema.exceptions import ValidationError
from app.core_.workflow_utils import WorkflowUtils
from app.core_.state_manager import WorkflowStateManager


class TestWorkflowUtils:
    """
    Test suite for WorkflowUtils class.
    Covers schema validation, tool parameter formatting, and switch-case evaluation.
    """

    @pytest.fixture
    def workflow_utils(self):
        """
        Provides a basic WorkflowUtils instance for testing.
        """
        with patch("app.core_.workflow_utils.load_schema") as mock_load_schema:
            mock_load_schema.return_value = {
                "type": "object",
                "properties": {
                    "nodes": {"type": "array"},
                    "transitions": {"type": "array"},
                },
                "required": ["nodes", "transitions"],
            }
            return WorkflowUtils(workflow_id="test-workflow-1")

    def test_initialization(self, workflow_utils):
        """
        Test successful initialization of WorkflowUtils.
        """
        assert workflow_utils.logger is not None
        assert isinstance(workflow_utils.state_manager, WorkflowStateManager)
        assert workflow_utils.ENHANCED_SCHEMA is not None

    def test_validate_schema_valid(self, workflow_utils):
        """
        Test schema validation with valid workflow JSON.
        """
        valid_workflow = {"nodes": [], "transitions": []}
        try:
            workflow_utils._validate_schema(valid_workflow)
        except ValidationError:
            pytest.fail("Valid workflow failed schema validation")

    def test_validate_schema_invalid(self, workflow_utils):
        """
        Test schema validation with invalid workflow JSON.
        """
        invalid_workflow = {
            "nodes": "not_an_array",  # Should be an array
            "transitions": [],
        }
        with pytest.raises(ValidationError):
            workflow_utils._validate_schema(invalid_workflow)

    @pytest.mark.asyncio
    async def test_format_tool_parameters_no_dependencies(self, workflow_utils):
        """
        Test tool parameter formatting without dependencies.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {"type": "object"},
            "output_schema": {"type": "object"},
        }
        input_data_configs = {}
        transition_id = "trans1"
        current_tool_params = {"param1": "value1"}

        # Act
        result = await workflow_utils._format_tool_parameters(
            node_tool_info, input_data_configs, transition_id, current_tool_params
        )

        # Assert
        assert isinstance(result, dict)
        assert "param1" in result

    @pytest.mark.asyncio
    async def test_format_tool_parameters_with_dependencies(self, workflow_utils):
        """
        Test tool parameter formatting with dependencies.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {"type": "object"},
            "output_schema": {"type": "object"},
        }
        input_data_configs = [{"from_transition_id": "prev_trans"}]
        transition_id = "trans1"
        current_tool_params = {"param1": "value1"}

        workflow_utils.state_manager.get_transition_result = Mock(
            return_value={"result": "prev_result"}
        )

        # Act
        result = await workflow_utils._format_tool_parameters(
            node_tool_info, input_data_configs, transition_id, current_tool_params
        )

        # Assert
        assert isinstance(result, dict)
        workflow_utils.state_manager.get_transition_result.assert_called_once_with(
            "prev_trans"
        )

    @pytest.mark.asyncio
    async def test_format_tool_parameters_with_placeholders(self, workflow_utils):
        """
        Test tool parameter formatting with placeholders in parameters.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {
                "predefined_fields": [
                    {
                        "field_name": "param1",
                        "data_type": {"type": "string"},
                        "required": True,
                    },
                    {
                        "field_name": "param2",
                        "data_type": {"type": "string"},
                        "required": True,
                    },
                ]
            },
            "output_schema": {"type": "object"},
        }
        input_data_configs = [{"from_transition_id": "prev_trans"}]
        transition_id = "trans1"
        current_tool_params = {"param1": "${user_name}", "param2": "static_value"}

        # Mock the state manager methods
        workflow_utils.state_manager.get_transition_result = Mock(
            return_value={"user_name": "John Doe", "user_email": "<EMAIL>"}
        )

        # Mock the _process_params_for_placeholders method to return the expected values
        expected_processed_params = {"param1": "John Doe", "param2": "static_value"}
        workflow_utils._process_params_for_placeholders = Mock(
            return_value=(expected_processed_params, False)
        )

        # Act
        result = await workflow_utils._format_tool_parameters(
            node_tool_info, input_data_configs, transition_id, current_tool_params
        )

        # Assert
        assert result == expected_processed_params
        workflow_utils.state_manager.get_transition_result.assert_called_once_with(
            "prev_trans"
        )
        workflow_utils._process_params_for_placeholders.assert_called_once()

    @pytest.mark.asyncio
    async def test_format_tool_parameters_with_list_params(self, workflow_utils):
        """
        Test tool parameter formatting with list-style parameters.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {
                "predefined_fields": [
                    {
                        "field_name": "username",
                        "data_type": {"type": "string"},
                        "required": True,
                    },
                    {
                        "field_name": "email",
                        "data_type": {"type": "string"},
                        "required": True,
                    },
                    {
                        "field_name": "static",
                        "data_type": {"type": "string"},
                        "required": True,
                    },
                ]
            },
            "output_schema": {"type": "object"},
        }
        input_data_configs = [{"from_transition_id": "prev_trans"}]
        transition_id = "trans1"
        current_tool_params = [
            {
                "field_name": "username",
                "data_type": "string",
                "field_value": "${user_name}",
            },
            {
                "field_name": "email",
                "data_type": "string",
                "field_value": "${user_email}",
            },
            {
                "field_name": "static",
                "data_type": "string",
                "field_value": "static_value",
            },
        ]

        # Mock the state manager methods
        workflow_utils.state_manager.get_transition_result = Mock(
            return_value={"user_name": "John Doe", "user_email": "<EMAIL>"}
        )

        # Mock the _process_params_for_placeholders method to return the expected values
        expected_processed_params = [
            {
                "field_name": "username",
                "data_type": "string",
                "field_value": "John Doe",
            },
            {
                "field_name": "email",
                "data_type": "string",
                "field_value": "<EMAIL>",
            },
            {
                "field_name": "static",
                "data_type": "string",
                "field_value": "static_value",
            },
        ]
        workflow_utils._process_params_for_placeholders = Mock(
            return_value=(expected_processed_params, False)
        )

        # Act
        result = await workflow_utils._format_tool_parameters(
            node_tool_info, input_data_configs, transition_id, current_tool_params
        )

        # Assert
        assert result == expected_processed_params
        workflow_utils.state_manager.get_transition_result.assert_called_once_with(
            "prev_trans"
        )
        workflow_utils._process_params_for_placeholders.assert_called_once()

    @pytest.mark.asyncio
    async def test_format_tool_parameters_with_fallback(self, workflow_utils):
        """
        Test tool parameter formatting with fallback to format_schema when placeholders can't be resolved.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {"type": "object"},
            "output_schema": {"type": "object"},
        }
        input_data_configs = [{"from_transition_id": "prev_trans"}]
        transition_id = "trans1"
        current_tool_params = {"param1": "${unknown_field}", "param2": "static_value"}

        # Mock the state manager methods
        workflow_utils.state_manager.get_transition_result = Mock(
            return_value={"user_name": "John Doe", "user_email": "<EMAIL>"}
        )

        # Mock the _process_params_for_placeholders method to indicate unresolved placeholders
        workflow_utils._process_params_for_placeholders = Mock(
            return_value=(
                {"param1": "${unknown_field}", "param2": "static_value"},
                True,  # has_unresolved_placeholders = True
            )
        )

        # Mock the fallback method to return a known result
        expected_fallback_result = {
            "param1": "AI generated value",
            "param2": "static_value",
        }
        workflow_utils._fallback_to_format_schema = AsyncMock(
            return_value=expected_fallback_result
        )

        # Act
        result = await workflow_utils._format_tool_parameters(
            node_tool_info, input_data_configs, transition_id, current_tool_params
        )

        # Assert
        assert result == expected_fallback_result
        workflow_utils.state_manager.get_transition_result.assert_called_once_with(
            "prev_trans"
        )
        workflow_utils._fallback_to_format_schema.assert_called_once_with(
            node_tool_info,
            {"prev_trans": {"user_name": "John Doe", "user_email": "<EMAIL>"}},
            current_tool_params,
        )

    @pytest.mark.asyncio
    async def test_format_tool_parameters_error_handling(self, workflow_utils):
        """
        Test error handling in tool parameter formatting.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {"type": "object"},
            "output_schema": {"type": "object"},
        }
        # Use non-empty input_data_configs to ensure we get to the processing step
        input_data_configs = [{"from_transition_id": "prev_trans"}]
        transition_id = "trans1"
        current_tool_params = {"param1": "value1"}

        # Mock the get_transition_result to return a result
        workflow_utils.state_manager.get_transition_result = Mock(
            return_value={"result": "test_result"}
        )

        # Create a mock that raises an exception when processing the parameters
        workflow_utils._process_params_for_placeholders = Mock(
            side_effect=Exception("Test error")
        )

        # Mock the fallback method to return a known result
        expected_fallback_result = {"param1": "fallback value"}
        workflow_utils._fallback_to_format_schema = AsyncMock(
            return_value=expected_fallback_result
        )

        # Act
        result = await workflow_utils._format_tool_parameters(
            node_tool_info,
            input_data_configs,
            transition_id,
            current_tool_params,
        )

        # Assert
        assert result == expected_fallback_result
        workflow_utils._fallback_to_format_schema.assert_called_once()

    @pytest.mark.asyncio
    async def test_fallback_to_format_schema(self, workflow_utils):
        """
        Test the fallback to format_schema method.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {"type": "object"},
            "output_schema": {"type": "object"},
        }
        current_previous_results = {"prev_trans": {"result": "prev_result"}}
        current_tool_params = {"param1": "${unknown_field}"}

        # Mock format_schema to return a known result
        expected_result = {"param1": "AI generated value"}

        with patch(
            "app.core_.workflow_utils.format_schema",
            return_value=expected_result,
        ) as mock_format_schema:
            # Act
            result = await workflow_utils._fallback_to_format_schema(
                node_tool_info, current_previous_results, current_tool_params
            )

            # Assert
            assert result == expected_result
            mock_format_schema.assert_called_once_with(
                node_tool_info.get("input_schema", {}),
                node_tool_info.get("output_schema", {}),
                current_previous_results,
                current_tool_params,
            )

    def test_format_params_according_to_schema(self, workflow_utils):
        """
        Test formatting parameters according to the input schema.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {
                "predefined_fields": [
                    {
                        "field_name": "topic",
                        "data_type": {
                            "type": "string",
                            "description": "The topic of the video to be covered",
                        },
                        "required": True,
                    },
                    {
                        "field_name": "script_type",
                        "data_type": {
                            "type": "string",
                            "description": "The type of script",
                        },
                        "required": True,
                    },
                    {
                        "field_name": "keywords",
                        "data_type": {
                            "type": "object",
                            "description": "Keywords for the script",
                            "properties": {
                                "time": {
                                    "type": "string",
                                    "description": "Time for the script",
                                },
                                "objective": {
                                    "type": "string",
                                    "description": "Objective of the script",
                                },
                            },
                        },
                        "required": True,
                    },
                ]
            }
        }

        # Test with list-style parameters
        list_params = [
            {"field_name": "topic", "field_value": "NVIDIA latest Event"},
            {"field_name": "script_type", "field_value": "TOPIC"},
            {
                "field_name": "keywords",
                "field_value": {"time": "30seconds", "objective": "educational"},
            },
        ]

        # Act
        result = workflow_utils._format_params_according_to_schema(
            node_tool_info, list_params
        )

        # Assert
        assert isinstance(result, dict)
        assert result["topic"] == "NVIDIA latest Event"
        assert result["script_type"] == "TOPIC"
        assert isinstance(result["keywords"], dict)
        assert result["keywords"]["time"] == "30seconds"
        assert result["keywords"]["objective"] == "educational"

        # Test with dictionary-style parameters
        dict_params = {
            "topic": "NVIDIA latest Event",
            "script_type": "TOPIC",
            "keywords": {"time": "30seconds", "objective": "educational"},
        }

        # Act
        result = workflow_utils._format_params_according_to_schema(
            node_tool_info, dict_params
        )

        # Assert
        assert isinstance(result, dict)
        assert result["topic"] == "NVIDIA latest Event"
        assert result["script_type"] == "TOPIC"
        assert isinstance(result["keywords"], dict)
        assert result["keywords"]["time"] == "30seconds"
        assert result["keywords"]["objective"] == "educational"

    def test_process_nested_dict(self, workflow_utils):
        """
        Test processing nested dictionaries for placeholders.
        """
        # Arrange
        nested_dict = {
            "time": "30seconds",
            "objective": "${objective}",
            "audience": "18 to 50 ages",
            "nested": {"key1": "${nested_key}", "key2": "static_value"},
        }

        flattened_results = {"objective": "educational", "nested_key": "dynamic_value"}

        # Act
        result, has_unresolved = workflow_utils._process_nested_dict(
            nested_dict, flattened_results
        )

        # Assert
        assert has_unresolved is False
        assert result["time"] == "30seconds"
        assert result["objective"] == "educational"
        assert result["audience"] == "18 to 50 ages"
        assert result["nested"]["key1"] == "dynamic_value"
        assert result["nested"]["key2"] == "static_value"

        # Test with unresolved placeholders
        nested_dict_with_unresolved = {
            "time": "30seconds",
            "objective": "${unknown_field}",
            "audience": "18 to 50 ages",
        }

        # Act
        result, has_unresolved = workflow_utils._process_nested_dict(
            nested_dict_with_unresolved, flattened_results
        )

        # Assert
        assert has_unresolved is True
        assert result["time"] == "30seconds"
        assert result["objective"] == "${unknown_field}"
        assert result["audience"] == "18 to 50 ages"

    # ========================================
    # NULL VALUE FILTERING TESTS
    # ========================================

    def test_filter_null_empty_values_basic(self, workflow_utils):
        """
        Test basic null/empty value filtering functionality.
        """
        # Arrange
        test_params = {
            "valid_field": "valid_value",
            "null_field": None,
            "empty_string": "",
            "null_string": "null",
            "empty_dict": {},
            "empty_list": [],
            "valid_number": 42,
            "valid_boolean": False,  # Should be kept even though it's falsy
            "zero_number": 0,  # Should be kept even though it's falsy
        }

        # Act
        result = workflow_utils._filter_null_empty_values(test_params)

        # Assert
        expected = {
            "valid_field": "valid_value",
            "valid_number": 42,
            "valid_boolean": False,
            "zero_number": 0,
        }
        assert result == expected

    def test_filter_null_empty_values_nested(self, workflow_utils):
        """
        Test null/empty value filtering with nested dictionaries.
        """
        # Arrange
        test_params = {
            "outer_field": "outer_value",
            "nested_dict": {
                "inner_valid": "inner_value",
                "inner_null": None,
                "inner_empty": "",
                "deeply_nested": {
                    "deep_valid": "deep_value",
                    "deep_null": None,
                },
            },
            "empty_nested": {
                "all_null": None,
                "all_empty": "",
            },
        }

        # Act
        result = workflow_utils._filter_null_empty_values(test_params)

        # Assert
        expected = {
            "outer_field": "outer_value",
            "nested_dict": {
                "inner_valid": "inner_value",
                "deeply_nested": {
                    "deep_valid": "deep_value",
                },
            },
        }
        assert result == expected

    def test_filter_null_empty_values_non_dict_input(self, workflow_utils):
        """
        Test null/empty value filtering with non-dictionary input.
        """
        # Test with string input
        assert workflow_utils._filter_null_empty_values("test_string") == "test_string"

        # Test with list input
        assert workflow_utils._filter_null_empty_values([1, 2, 3]) == [1, 2, 3]

        # Test with None input
        assert workflow_utils._filter_null_empty_values(None) is None

    def test_convert_params_to_dict_with_filtering(self, workflow_utils):
        """
        Test _convert_params_to_dict method with null/empty value filtering.
        """
        # Test case 1: List format with null/empty values
        test_params_list = [
            {"field_name": "valid_field", "field_value": "valid_value"},
            {"field_name": "null_field", "field_value": None},
            {"field_name": "empty_field", "field_value": ""},
            {"field_name": "number_field", "field_value": 42},
            {"field_name": "boolean_field", "field_value": False},  # Should be kept
        ]

        result_list = workflow_utils._convert_params_to_dict(test_params_list)
        expected_list = {
            "valid_field": "valid_value",
            "number_field": 42,
            "boolean_field": False,
        }
        assert result_list == expected_list

        # Test case 2: Dict format with null/empty values
        test_params_dict = {
            "valid_field": "valid_value",
            "null_field": None,
            "empty_field": "",
            "number_field": 42,
            "empty_list": [],
            "zero_field": 0,  # Should be kept
        }

        result_dict = workflow_utils._convert_params_to_dict(test_params_dict)
        expected_dict = {
            "valid_field": "valid_value",
            "number_field": 42,
            "zero_field": 0,
        }
        assert result_dict == expected_dict

    def test_format_params_according_to_schema_with_null_filtering(
        self, workflow_utils
    ):
        """
        Test _format_params_according_to_schema method with null/empty value filtering.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {
                "predefined_fields": [
                    {
                        "field_name": "required_field",
                        "required": True,
                        "data_type": {"type": "string"},
                    },
                    {
                        "field_name": "optional_field",
                        "required": False,
                        "data_type": {"type": "string"},
                    },
                    {
                        "field_name": "required_number",
                        "required": True,
                        "data_type": {"type": "number"},
                    },
                    {
                        "field_name": "optional_number",
                        "required": False,
                        "data_type": {"type": "number"},
                    },
                    {
                        "field_name": "optional_object",
                        "required": False,
                        "data_type": {"type": "object"},
                    },
                    {
                        "field_name": "required_object",
                        "required": True,
                        "data_type": {"type": "object"},
                    },
                ]
            }
        }

        params = {
            "required_field": "required_value",
            "optional_field": None,  # Should be skipped
            "required_number": None,  # Should be included with warning, then filtered
            "optional_number": 42,  # Should be included
            "optional_object": {},  # Should be skipped
            "required_object": {"nested": "value"},  # Should be included
            "extra_field": "extra_value",  # Not in schema, should be ignored
        }

        # Act
        result = workflow_utils._format_params_according_to_schema(
            node_tool_info, params
        )

        # Assert
        expected = {
            "required_field": "required_value",
            # required_number removed because it's null (per filtering requirement)
            "optional_number": 42,
            "required_object": {"nested": "value"},
        }
        assert result == expected

    def test_format_params_according_to_schema_nested_filtering(self, workflow_utils):
        """
        Test _format_params_according_to_schema method with nested object filtering.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {
                "predefined_fields": [
                    {
                        "field_name": "nested_required",
                        "required": True,
                        "data_type": {"type": "object"},
                    },
                    {
                        "field_name": "nested_optional",
                        "required": False,
                        "data_type": {"type": "object"},
                    },
                ]
            }
        }

        params = {
            "nested_required": {
                "valid_field": "valid_value",
                "null_field": None,
                "empty_field": "",
            },
            "nested_optional": {
                "all_null": None,
                "all_empty": "",
            },
        }

        # Act
        result = workflow_utils._format_params_according_to_schema(
            node_tool_info, params
        )

        # Assert
        expected = {
            "nested_required": {
                "valid_field": "valid_value",
            }
            # nested_optional should be completely filtered out as it becomes empty after filtering
        }
        assert result == expected

    def test_resolve_handle_data_with_null_filtering(self, workflow_utils):
        """
        Test _resolve_handle_data method with null/empty value filtering.
        """
        # Arrange
        current_tool_params = {
            "static_param": "static_value",
            "null_static": None,
            "empty_static": "",
            "valid_number": 42,
        }

        all_previous_results = {
            "transition-1": {
                "result": {
                    "valid_handle": "mapped_value",
                    "null_handle": None,
                    "empty_handle": "",
                    "number_handle": 123,
                }
            }
        }

        handle_mappings = {
            "transition-1": [
                {
                    "source_handle_id": "valid_handle",
                    "target_handle_id": "mapped_param",
                    "edge_id": "edge-1",
                },
                {
                    "source_handle_id": "null_handle",
                    "target_handle_id": "null_mapped",
                    "edge_id": "edge-2",
                },
                {
                    "source_handle_id": "empty_handle",
                    "target_handle_id": "empty_mapped",
                    "edge_id": "edge-3",
                },
                {
                    "source_handle_id": "number_handle",
                    "target_handle_id": "number_mapped",
                    "edge_id": "edge-4",
                },
            ]
        }

        # Mock the _extract_data_by_handle method to return the expected values
        def mock_extract_data(source_results, handle_id, source_transition_id):
            if isinstance(source_results, dict) and "result" in source_results:
                result_data = source_results["result"]
                if isinstance(result_data, dict) and handle_id in result_data:
                    return result_data[handle_id]
            return None

        workflow_utils._extract_data_by_handle = mock_extract_data

        # Act
        result = workflow_utils._resolve_handle_data(
            current_tool_params, all_previous_results, handle_mappings
        )

        # Assert
        expected = {
            "static_param": "static_value",
            "valid_number": 42,
            "mapped_param": "mapped_value",
            "number_mapped": 123,
            # null_static, empty_static, null_mapped, empty_mapped should all be filtered out
        }
        assert result == expected

    def test_process_params_for_placeholders_with_null_filtering(self, workflow_utils):
        """
        Test _process_params_for_placeholders method with null/empty value filtering.
        """
        # Test case 1: List format with placeholders and null values
        params_list = [
            {"field_name": "valid_field", "field_value": "${valid_placeholder}"},
            {"field_name": "null_field", "field_value": None},
            {"field_name": "empty_field", "field_value": ""},
            {"field_name": "static_field", "field_value": "static_value"},
        ]

        flattened_results = {
            "valid_placeholder": "resolved_value",
        }

        # Act
        result, has_unresolved = workflow_utils._process_params_for_placeholders(
            params_list, flattened_results
        )

        # Assert
        expected = {
            "valid_field": "resolved_value",
            "static_field": "static_value",
            # null_field and empty_field should be filtered out
        }
        assert result == expected
        assert has_unresolved is False

        # Test case 2: Dict format with placeholders and null values
        params_dict = {
            "valid_field": "${valid_placeholder}",
            "null_field": None,
            "empty_field": "",
            "static_field": "static_value",
            "unresolved_field": "${unknown_placeholder}",
        }

        # Act
        result, has_unresolved = workflow_utils._process_params_for_placeholders(
            params_dict, flattened_results
        )

        # Assert
        expected = {
            "valid_field": "resolved_value",
            "static_field": "static_value",
            "unresolved_field": "${unknown_placeholder}",
            # null_field and empty_field should be filtered out
        }
        assert result == expected
        assert has_unresolved is True

    @pytest.mark.asyncio
    async def test_evaluate_switch_case_no_cases(self, workflow_utils):
        """
        Test switch-case evaluation with no cases defined.
        """
        # Arrange
        transition_routing = {"cases": [], "default_transition": "default_trans"}
        node_result = "test_result"

        # Act
        result = await workflow_utils._evaluate_switch_case(
            transition_routing, node_result
        )

        # Assert
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0] == "default_trans"

    @pytest.mark.asyncio
    async def test_evaluate_switch_case_equals_operator(self, workflow_utils):
        """
        Test switch-case evaluation with equals operator.
        """
        # Arrange
        transition_routing = {
            "cases": [
                {
                    "condition": {
                        "source": "node_output",
                        "operator": "equals",
                        "expected_value": "test_result",
                    },
                    "next_transition": "matched_trans",
                }
            ],
            "default_transition": "default_trans",
        }
        node_result = "test_result"

        # Act
        result = await workflow_utils._evaluate_switch_case(
            transition_routing, node_result
        )

        # Assert
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0] == "matched_trans"

    @pytest.mark.asyncio
    async def test_evaluate_switch_case_contains_operator(self, workflow_utils):
        """
        Test switch-case evaluation with contains operator.
        """
        # Arrange
        transition_routing = {
            "cases": [
                {
                    "condition": {
                        "source": "node_output",
                        "operator": "contains",
                        "expected_value": "test",
                    },
                    "next_transition": "matched_trans",
                }
            ],
            "default_transition": "default_trans",
        }
        node_result = "this is a test result"

        # Act
        result = await workflow_utils._evaluate_switch_case(
            transition_routing, node_result
        )

        # Assert
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0] == "matched_trans"

    @pytest.mark.asyncio
    async def test_evaluate_switch_case_global_context(self, workflow_utils):
        """
        Test switch-case evaluation with global context variables.
        """
        # Arrange
        transition_routing = {
            "cases": [
                {
                    "condition": {
                        "source": "global_context",
                        "operator": "equals",
                        "variable_name": "test_var",
                        "expected_value": "test_value",
                    },
                    "next_transition": "matched_trans",
                }
            ],
            "default_transition": "default_trans",
            "global_context_definitions": {
                "variables": [{"name": "test_var", "value": "test_value"}]
            },
        }
        node_result = None

        # Act
        result = await workflow_utils._evaluate_switch_case(
            transition_routing, node_result
        )

        # Assert
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0] == "matched_trans"

    @pytest.mark.asyncio
    async def test_evaluate_switch_case_ai_evaluate(self, workflow_utils):
        """
        Test switch-case evaluation with ai_evaluate operator.
        """
        # Arrange
        transition_routing = {
            "cases": [
                {
                    "condition": {
                        "source": "node_output",
                        "operator": "ai_evaluate",
                        "expected_value": "positive sentiment",
                    },
                    "next_transition": "matched_trans",
                }
            ],
            "default_transition": "default_trans",
        }
        node_result = "This is great!"

        with patch(
            "app.core_.workflow_utils.resolve_switch_case", AsyncMock(return_value=True)
        ) as mock_resolve:
            # Act
            result = await workflow_utils._evaluate_switch_case(
                transition_routing, node_result
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1
            assert result[0] == "matched_trans"
            mock_resolve.assert_called_once_with(node_result, "positive sentiment")

    @pytest.mark.asyncio
    async def test_evaluate_switch_case_invalid_operator(self, workflow_utils):
        """
        Test switch-case evaluation with invalid operator.
        """
        # Arrange
        transition_routing = {
            "cases": [
                {
                    "condition": {
                        "source": "node_output",
                        "operator": "invalid_operator",
                        "expected_value": "test",
                    },
                    "next_transition": "matched_trans",
                }
            ],
            "default_transition": "default_trans",
        }
        node_result = "test_result"

        # Act
        result = await workflow_utils._evaluate_switch_case(
            transition_routing, node_result
        )

        # Assert
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0] == "default_trans"

    @pytest.mark.asyncio
    async def test_evaluate_switch_case_multiple_cases(self, workflow_utils):
        """
        Test switch-case evaluation with multiple cases.
        """
        # Arrange
        transition_routing = {
            "cases": [
                {
                    "condition": {
                        "source": "node_output",
                        "operator": "equals",
                        "expected_value": "wrong_value",
                    },
                    "next_transition": "wrong_trans",
                },
                {
                    "condition": {
                        "source": "node_output",
                        "operator": "equals",
                        "expected_value": "test_result",
                    },
                    "next_transition": "correct_trans",
                },
            ],
            "default_transition": "default_trans",
        }
        node_result = "test_result"

        # Act
        result = await workflow_utils._evaluate_switch_case(
            transition_routing, node_result
        )

        # Assert
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0] == "correct_trans"

    @pytest.mark.asyncio
    async def test_evaluate_switch_case_multiple_matching_conditions(
        self, workflow_utils
    ):
        """
        Test switch-case evaluation with multiple matching conditions.
        This tests the new behavior where multiple matching conditions return multiple transitions.
        """
        # Arrange
        transition_routing = {
            "cases": [
                {
                    "condition": {
                        "source": "node_output",
                        "operator": "contains",
                        "expected_value": "test",
                    },
                    "next_transition": "first_match_trans",
                },
                {
                    "condition": {
                        "source": "node_output",
                        "operator": "contains",
                        "expected_value": "result",
                    },
                    "next_transition": "second_match_trans",
                },
            ],
            "default_transition": "default_trans",
        }
        node_result = "this is a test result"  # Contains both "test" and "result"

        # Act
        result = await workflow_utils._evaluate_switch_case(
            transition_routing, node_result
        )

        # Assert
        assert isinstance(result, list)
        assert len(result) == 2
        assert "first_match_trans" in result
        assert "second_match_trans" in result

    @pytest.mark.asyncio
    async def test_format_tool_parameters_dictionary_output(self, workflow_utils):
        """
        Test that the tool parameters are formatted correctly and returned as a dictionary with direct key-value pairs.
        """
        # Arrange
        node_tool_info = {
            "input_schema": {
                "predefined_fields": [
                    {
                        "field_name": "topic",
                        "data_type": {"type": "string"},
                        "required": True,
                    },
                    {
                        "field_name": "video_type",
                        "data_type": {"type": "string"},
                        "required": True,
                    },
                    {
                        "field_name": "script_type",
                        "data_type": {"type": "string"},
                        "required": True,
                    },
                    {
                        "field_name": "keywords",
                        "data_type": {
                            "type": "object",
                            "properties": {
                                "time": {"type": "string"},
                                "objective": {"type": "string"},
                                "audience": {"type": "string"},
                                "gender": {"type": "string"},
                                "tone": {"type": "string"},
                                "speakers": {"type": "string"},
                            },
                        },
                        "required": True,
                    },
                ]
            },
            "output_schema": {"type": "object"},
        }

        input_data_configs = [{"from_transition_id": "prev_trans"}]
        transition_id = "trans1"

        # List-style parameters (current format)
        current_tool_params = [
            {
                "field_name": "topic",
                "data_type": "string",
                "field_value": "${topic}",
            },
            {
                "field_name": "video_type",
                "data_type": "string",
                "field_value": "SHORT",
            },
            {
                "field_name": "script_type",
                "data_type": "string",
                "field_value": "TOPIC",
            },
            {
                "field_name": "keywords",
                "data_type": "object",
                "field_value": {
                    "time": "30seconds",
                    "objective": "${objective}",
                    "audience": "18 to 50 ages",
                    "gender": "neutral",
                    "tone": "Technical",
                    "speakers": "influencer",
                },
            },
        ]

        # Mock the state manager methods
        workflow_utils.state_manager.get_transition_result = Mock(
            return_value={
                "topic": "NVIDIA latest Event",
                "objective": "educational",
            }
        )

        # Expected output format as a dictionary with direct key-value pairs
        expected_output = {
            "topic": "NVIDIA latest Event",
            "video_type": "SHORT",
            "script_type": "TOPIC",
            "keywords": {
                "time": "30seconds",
                "objective": "educational",
                "audience": "18 to 50 ages",
                "gender": "neutral",
                "tone": "Technical",
                "speakers": "influencer",
            },
        }

        # Act
        result = await workflow_utils._format_tool_parameters(
            node_tool_info, input_data_configs, transition_id, current_tool_params
        )

        # Assert
        assert isinstance(result, dict)
        assert result == expected_output
        workflow_utils.state_manager.get_transition_result.assert_called_once_with(
            "prev_trans"
        )
