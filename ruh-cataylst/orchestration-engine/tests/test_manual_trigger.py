"""
Test script for manually triggering the Redis event listener.
This script tests the Redis event listener by manually publishing keyspace events.

Usage:
    poetry run python test_manual_trigger.py
"""

import json
import time
import uuid
import sys
import logging
import redis
from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.services.db_connections.redis_event_listener import RedisEventListener
from app.core_.state_manager import WorkflowStateManager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Set to DEBUG for more detailed logs
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("ManualTriggerTest")

def test_manual_trigger():
    """
    Test the Redis event listener by manually publishing keyspace events.
    
    This test:
    1. Creates a unique workflow ID
    2. Initializes a dedicated Redis event listener
    3. Stores workflow state and transition results in Redis
    4. Manually publishes keyspace events to simulate deletion
    5. Verifies the Redis event listener detects the events
    6. Verifies the data is archived to PostgreSQL
    """
    # Generate a unique workflow ID for this test
    workflow_id = f"test-workflow-{uuid.uuid4()}"
    correlation_id = workflow_id  # Using the same ID for simplicity
    
    logger.info(f"Starting test with workflow ID: {workflow_id}")
    
    # Initialize Redis managers
    results_redis_manager = RedisManager(
        db_index=int(settings.redis_results_db_index)
    )
    state_redis_manager = RedisManager(
        db_index=int(settings.redis_state_db_index)
    )
    
    # Initialize PostgreSQL manager
    postgres_manager = get_postgres_manager()
    
    # Initialize workflow state manager
    state_manager = WorkflowStateManager(workflow_id=workflow_id)
    state_manager.results_redis_manager = results_redis_manager
    state_manager.state_redis_manager = state_redis_manager
    state_manager.postgres_manager = postgres_manager
    
    # Initialize a dedicated Redis event listener for this test
    redis_event_listener = RedisEventListener(state_manager)
    
    # Create test data
    workflow_state = {
        "pending_transitions": ["transition1", "transition2"],
        "completed_transitions": [],
        "waiting_transitions": [],
        "terminated": False,
        "paused": False
    }
    
    transition_id = f"transition1-{uuid.uuid4()}"
    transition_result = {
        "status": "success",
        "result": {
            "data": "Test result data",
            "timestamp": time.time()
        }
    }
    
    # Configure Redis for keyspace notifications
    logger.info("Configuring Redis for keyspace notifications...")
    results_redis_manager.redis_client.config_set("notify-keyspace-events", "KEA")
    state_redis_manager.redis_client.config_set("notify-keyspace-events", "KEA")
    
    # Start the Redis event listener in a separate thread
    logger.info("Starting Redis event listener...")
    redis_event_listener.start()
    
    # Wait for the Redis event listener to initialize
    logger.info("Waiting for Redis event listener to initialize...")
    time.sleep(5)
    
    # Store data in Redis
    logger.info("Storing test data in Redis...")
    
    # Store workflow state
    workflow_state_key = f"workflow_state:{workflow_id}"
    state_redis_manager.set_value(workflow_state_key, json.dumps(workflow_state))
    logger.info(f"Stored workflow state with key: {workflow_state_key}")
    
    # Store transition result
    transition_result_key = f"result:{transition_id}"
    results_redis_manager.set_value(transition_result_key, json.dumps(transition_result))
    logger.info(f"Stored transition result with key: {transition_result_key}")
    
    # Verify data is in Redis
    stored_state = state_redis_manager.get_value(workflow_state_key)
    stored_result = results_redis_manager.get_value(transition_result_key)
    
    if stored_state and stored_result:
        logger.info("Data successfully stored in Redis")
    else:
        logger.error("Failed to store data in Redis")
        redis_event_listener.stop()
        return False
    
    # Wait a moment to ensure the Redis event listener is ready
    logger.info("Waiting before publishing keyspace events...")
    time.sleep(5)
    
    # Manually publish keyspace events to simulate deletion
    logger.info("Manually publishing keyspace events to simulate deletion...")
    
    # Publish transition result deletion event
    results_db = results_redis_manager.db_index
    results_channel = f"__keyspace@{results_db}__:{transition_result_key}"
    results_redis_manager.redis_client.publish(results_channel, "del")
    logger.info(f"Published 'del' event to channel: {results_channel}")
    
    # Wait for the event to be processed
    logger.info("Waiting for transition result deletion event to be processed...")
    time.sleep(5)
    
    # Publish workflow state deletion event
    state_db = state_redis_manager.db_index
    state_channel = f"__keyspace@{state_db}__:{workflow_state_key}"
    state_redis_manager.redis_client.publish(state_channel, "del")
    logger.info(f"Published 'del' event to channel: {state_channel}")
    
    # Wait for the event to be processed
    logger.info("Waiting for workflow state deletion event to be processed...")
    time.sleep(5)
    
    # Stop the Redis event listener
    logger.info("Stopping Redis event listener...")
    redis_event_listener.stop()
    
    # Verify data was archived to PostgreSQL
    logger.info("Checking if data was archived to PostgreSQL...")
    
    # Check for transition result in PostgreSQL
    pg_transition_result = postgres_manager.get_transition_result(correlation_id, transition_id)
    
    if pg_transition_result:
        logger.info("Transition result successfully archived to PostgreSQL")
        logger.info(f"Result: {pg_transition_result}")
    else:
        logger.error("Failed to find transition result in PostgreSQL")
        return False
    
    # Check for workflow state in PostgreSQL
    pg_workflow_state = postgres_manager.get_workflow_state(correlation_id)
    
    if pg_workflow_state:
        logger.info("Workflow state successfully archived to PostgreSQL")
        logger.info(f"State: {pg_workflow_state}")
    else:
        logger.error("Failed to find workflow state in PostgreSQL")
        return False
    
    logger.info("Test completed successfully!")
    return True

if __name__ == "__main__":
    logger.info("Starting manual trigger test...")
    
    try:
        success = test_manual_trigger()
        if success:
            logger.info("✅ Test passed: Redis event listener and PostgreSQL archiving works correctly")
            sys.exit(0)
        else:
            logger.error("❌ Test failed: Redis event listener and PostgreSQL archiving did not work as expected")
            sys.exit(1)
    except Exception as e:
        logger.exception(f"❌ Test failed with exception: {e}")
        sys.exit(1)
