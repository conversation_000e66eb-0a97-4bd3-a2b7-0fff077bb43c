"""
Test script for Redis expiration and PostgreSQL fallback.
This script tests the functionality of Redis key expiration and PostgreSQL fallback
by setting a short TTL on Redis keys and verifying that data is archived to PostgreSQL.

Usage:
    poetry run python test_redis_expiration.py
"""

import json
import time
import uuid
import sys
import logging
import asyncio
from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.services.db_connections.redis_event_listener import get_redis_event_listener
from app.core_.state_manager import WorkflowStateManager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("RedisExpirationTest")

async def test_redis_expiration():
    """
    Test the Redis expiration and PostgreSQL fallback functionality.
    
    This test:
    1. Creates a unique workflow ID
    2. Stores workflow state and transition results in Redis with a short TTL
    3. Waits for the Redis keys to expire
    4. Verifies the data is archived to PostgreSQL
    5. Attempts to retrieve the data using the WorkflowStateManager
    """
    # Generate a unique workflow ID for this test
    workflow_id = f"test-workflow-{uuid.uuid4()}"
    correlation_id = workflow_id  # Using the same ID for simplicity
    
    logger.info(f"Starting test with workflow ID: {workflow_id}")
    
    # Initialize Redis managers
    results_redis_manager = RedisManager(db_index=int(settings.redis_results_db_index))
    state_redis_manager = RedisManager(db_index=int(settings.redis_state_db_index))
    
    # Initialize PostgreSQL manager
    postgres_manager = get_postgres_manager()
    
    # Initialize workflow state manager
    state_manager = WorkflowStateManager(workflow_id=workflow_id)
    state_manager.results_redis_manager = results_redis_manager
    state_manager.state_redis_manager = state_redis_manager
    state_manager.postgres_manager = postgres_manager
    
    # Initialize Redis event listener with the state manager
    redis_event_listener = get_redis_event_listener(state_manager)
    
    # Create test data
    workflow_state = {
        "pending_transitions": ["transition1", "transition2"],
        "completed_transitions": [],
        "waiting_transitions": [],
        "terminated": False,
        "paused": False
    }
    
    transition_id = f"transition1-{uuid.uuid4()}"
    transition_result = {
        "status": "success",
        "result": {
            "data": "Test result data",
            "timestamp": time.time()
        }
    }
    
    # Store data in Redis with a short TTL (5 seconds)
    logger.info("Storing test data in Redis with a 5-second TTL...")
    
    # Store workflow state
    workflow_state_key = f"workflow_state:{workflow_id}"
    state_redis_manager.redis_client.set(workflow_state_key, json.dumps(workflow_state), ex=5)
    logger.info(f"Stored workflow state with key: {workflow_state_key} (TTL: 5s)")
    
    # Store transition result
    transition_result_key = f"result:{transition_id}"
    results_redis_manager.redis_client.set(transition_result_key, json.dumps(transition_result), ex=5)
    logger.info(f"Stored transition result with key: {transition_result_key} (TTL: 5s)")
    
    # Verify data is in Redis
    stored_state = state_redis_manager.get_value(workflow_state_key)
    stored_result = results_redis_manager.get_value(transition_result_key)
    
    if stored_state and stored_result:
        logger.info("Data successfully stored in Redis")
    else:
        logger.error("Failed to store data in Redis")
        return False
    
    # Wait for Redis keys to expire (6 seconds to be safe)
    logger.info("Waiting for Redis keys to expire (6 seconds)...")
    await asyncio.sleep(6)
    
    # Verify keys have expired
    stored_state = state_redis_manager.get_value(workflow_state_key)
    stored_result = results_redis_manager.get_value(transition_result_key)
    
    if stored_state is None and stored_result is None:
        logger.info("Redis keys have expired as expected")
    else:
        logger.error("Redis keys did not expire as expected")
        return False
    
    # Wait a moment for the Redis event listener to process the expiration events
    logger.info("Waiting for Redis event listener to process expiration events...")
    await asyncio.sleep(2)
    
    # Verify data was archived to PostgreSQL
    logger.info("Checking if data was archived to PostgreSQL...")
    
    # Check for transition result in PostgreSQL
    pg_transition_result = postgres_manager.get_transition_result(correlation_id, transition_id)
    
    if pg_transition_result:
        logger.info("Transition result successfully archived to PostgreSQL:")
        logger.info(json.dumps(pg_transition_result, indent=2))
    else:
        logger.error("Failed to find transition result in PostgreSQL")
        return False
    
    # Check for workflow state in PostgreSQL
    pg_workflow_state = postgres_manager.get_workflow_state(correlation_id)
    
    if pg_workflow_state:
        logger.info("Workflow state successfully archived to PostgreSQL:")
        logger.info(json.dumps(pg_workflow_state, indent=2))
    else:
        logger.error("Failed to find workflow state in PostgreSQL")
        return False
    
    # Now try to retrieve the data using the WorkflowStateManager
    logger.info("Attempting to retrieve data using WorkflowStateManager...")
    
    # Get transition result
    retrieved_result = state_manager.get_transition_result(transition_id)
    
    if retrieved_result:
        logger.info("Successfully retrieved transition result from PostgreSQL via WorkflowStateManager:")
        logger.info(json.dumps(retrieved_result, indent=2))
    else:
        logger.error("Failed to retrieve transition result from PostgreSQL via WorkflowStateManager")
        return False
    
    # Load workflow state
    state_loaded = await state_manager.load_workflow_state()
    
    if state_loaded:
        logger.info("Successfully loaded workflow state from PostgreSQL via WorkflowStateManager")
        logger.info(f"Pending transitions: {state_manager.pending_transitions}")
        logger.info(f"Completed transitions: {state_manager.completed_transitions}")
        logger.info(f"Waiting transitions: {state_manager.waiting_transitions}")
        logger.info(f"Terminated: {state_manager.terminated}")
        logger.info(f"Paused: {state_manager.workflow_paused}")
    else:
        logger.error("Failed to load workflow state from PostgreSQL via WorkflowStateManager")
        return False
    
    logger.info("Test completed successfully!")
    return True

if __name__ == "__main__":
    logger.info("Starting Redis expiration test...")
    
    try:
        asyncio.run(test_redis_expiration())
        logger.info("✅ Test passed: Redis expiration and PostgreSQL fallback works correctly")
        sys.exit(0)
    except Exception as e:
        logger.exception(f"❌ Test failed with exception: {e}")
        sys.exit(1)
