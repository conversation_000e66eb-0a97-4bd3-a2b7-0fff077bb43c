import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from app.execution.executor_server_kafka import KafkaWorkflowConsumer
from app.core_.executor_core import EnhancedWorkflowEngine

class TestKafkaWorkflowConsumer:
    """
    Test suite for KafkaWorkflowConsumer class.
    Tests Kafka message processing, workflow execution, and error handling.
    """

    @pytest.fixture
    async def kafka_consumer(self):
        """
        Provides a KafkaWorkflowConsumer instance with mocked Kafka components.
        """
        with patch('app.execution.executor_server_kafka.AIOKafkaConsumer') as mock_consumer, \
             patch('app.execution.executor_server_kafka.AIOKafkaProducer') as mock_producer, \
             patch('app.execution.executor_server_kafka.KafkaToolExecutor') as mock_executor:
            
            consumer_instance = KafkaWorkflowConsumer()
            # Mock the Kafka components
            consumer_instance.consumer = AsyncMock(spec=AIOKafkaConsumer)
            consumer_instance.producer = AsyncMock(spec=AIOKafkaProducer)
            consumer_instance.mcp_tool_executor = AsyncMock()
            
            yield consumer_instance

    @pytest.fixture
    def mock_consumer_record(self):
        """
        Creates a mock ConsumerRecord for testing message processing.
        """
        record = Mock()
        record.topic = "test-topic"
        record.value = json.dumps({"test": "data"}).encode('utf-8')
        record.headers = [
            ("correlationId", b"test-correlation-id"),
            ("reply-topic", b"test-reply-topic")
        ]
        record.partition = 0
        record.offset = 100
        return record

    @pytest.mark.asyncio
    async def test_initialization(self, kafka_consumer):
        """
        Test successful initialization of KafkaWorkflowConsumer.
        """
        assert kafka_consumer.running_workflows == {}
        assert kafka_consumer.approval_wait_queue == {}
        assert kafka_consumer.logger is not None

    @pytest.mark.asyncio
    async def test_start_consumer(self, kafka_consumer):
        """
        Test consumer startup sequence.
        """
        # Mock the consumer's getone method to raise CancelledError after one iteration
        kafka_consumer.consumer.getone.side_effect = asyncio.CancelledError()

        # Test the start sequence
        with pytest.raises(asyncio.CancelledError):
            await kafka_consumer.start_consumer()

        # Verify that start methods were called
        kafka_consumer.consumer.start.assert_called_once()
        kafka_consumer.producer.start.assert_called_once()
        kafka_consumer.mcp_tool_executor.start.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_consumer(self, kafka_consumer):
        """
        Test consumer shutdown sequence.
        """
        await kafka_consumer.stop_consumer()

        kafka_consumer.consumer.stop.assert_called_once()
        kafka_consumer.producer.stop.assert_called_once()
        kafka_consumer.mcp_tool_executor.stop.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_response(self, kafka_consumer):
        """
        Test sending response messages through Kafka producer.
        """
        reply_topic = "test-reply-topic"
        correlation_id = "test-correlation-id"
        response_data = {"status": "success", "result": "test result"}

        await kafka_consumer.send_response(reply_topic, correlation_id, response_data)

        kafka_consumer.producer.send.assert_called_once_with(
            reply_topic,
            response_data,
            headers=[("correlationId", b"test-correlation-id")]
        )

    @pytest.mark.asyncio
    async def test_send_error_response(self, kafka_consumer):
        """
        Test sending error response messages.
        """
        reply_topic = "test-reply-topic"
        correlation_id = "test-correlation-id"
        error_message = "Test error message"
        sequence_number = 1

        await kafka_consumer.send_error_response(
            reply_topic, correlation_id, error_message, sequence_number
        )

        expected_response = {
            "status": "error",
            "result": error_message,
            "sequence": sequence_number
        }

        kafka_consumer.producer.send.assert_called_once_with(
            reply_topic,
            expected_response,
            headers=[("correlationId", b"test-correlation-id")]
        )

    @pytest.mark.asyncio
    async def test_process_workflow_request_success(self, kafka_consumer, mock_consumer_record):
        """
        Test successful processing of a workflow request.
        """
        # Mock the workflow fetching and initialization
        with patch('app.execution.executor_server_kafka.fetch_workflow_orchestration') as mock_fetch, \
             patch('app.execution.executor_server_kafka.initialize_workflow_with_params') as mock_init, \
             patch('app.execution.executor_server_kafka.EnhancedWorkflowEngine') as mock_engine:

            mock_fetch.return_value = {"workflow": "test"}
            mock_init.return_value = {"initialized": "workflow"}
            mock_engine_instance = AsyncMock()
            mock_engine.return_value = mock_engine_instance
            mock_engine_instance.execute.return_value = True

            # Prepare test data
            workflow_request = {
                "data": {
                    "workflow_id": "test-workflow"
                }
            }
            mock_consumer_record.value = json.dumps(workflow_request).encode('utf-8')

            # Execute the workflow request
            result = await kafka_consumer.process_workflow_request(
                mock_consumer_record.value,
                mock_consumer_record
            )

            assert result is True
            mock_fetch.assert_called_once_with("test-workflow")
            mock_init.assert_called_once()
            assert "test-correlation-id" in kafka_consumer.running_workflows

    @pytest.mark.asyncio
    async def test_process_execution_request_regenerate(self, kafka_consumer, mock_consumer_record):
        """
        Test processing of a regenerate execution request.
        """
        # Setup mock workflow engine
        mock_engine = AsyncMock()
        mock_engine.transition_handler.regenerate_transition.return_value = True
        kafka_consumer.running_workflows["test-correlation-id"] = mock_engine

        # Prepare test data
        execution_request = {
            "action": "regenerate",
            "node_id": "test-node",
            "params": {"param1": "value1"}
        }
        mock_consumer_record.value = json.dumps(execution_request).encode('utf-8')

        # Execute the request
        result = await kafka_consumer.process_execution_request(
            mock_consumer_record.value,
            mock_consumer_record
        )

        assert result is True
        mock_engine.transition_handler.regenerate_transition.assert_called_once_with(
            transition_id="test-node",
            action_type="regenerate",
            server_params_override={"param1": "value1"}
        )

    @pytest.mark.asyncio
    async def test_process_approval_request_approve(self, kafka_consumer, mock_consumer_record):
        """
        Test processing of an approval request with 'approve' decision.
        """
        # Setup mock workflow engine
        mock_engine = AsyncMock()
        mock_engine.transition_handler._pause_event = AsyncMock()
        kafka_consumer.running_workflows["test-correlation-id"] = mock_engine
        kafka_consumer.approval_wait_queue["test-correlation-id"] = "test-transition"

        # Prepare test data
        approval_request = {
            "decision": "approve"
        }
        mock_consumer_record.value = json.dumps(approval_request).encode('utf-8')

        # Execute the request
        result = await kafka_consumer.process_approval_request(
            mock_consumer_record.value,
            mock_consumer_record
        )

        assert result is True
        assert "test-correlation-id" not in kafka_consumer.approval_wait_queue
        mock_engine.transition_handler._pause_event.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_workflow_result_success(self, kafka_consumer):
        """
        Test handling of successful workflow execution results.
        """
        execution_task = AsyncMock()
        execution_task.return_value = True
        correlation_id = "test-correlation-id"
        workflow_id = "test-workflow"
        reply_topic = "test-reply-topic"

        await kafka_consumer.handle_workflow_result(
            execution_task,
            correlation_id,
            workflow_id,
            reply_topic
        )

        expected_response = {
            "status": "Workflow Completed",
            "result": f" Workflow '{workflow_id}' executed successfully.",
            "workflow_status": "completed"
        }

        kafka_consumer.producer.send.assert_called_once_with(
            reply_topic,
            expected_response,
            headers=[("correlationId", b"test-correlation-id")]
        )

    @pytest.mark.asyncio
    async def test_handle_workflow_result_failure(self, kafka_consumer):
        """
        Test handling of failed workflow execution results.
        """
        execution_task = AsyncMock()
        execution_task.return_value = False
        correlation_id = "test-correlation-id"
        workflow_id = "test-workflow"
        reply_topic = "test-reply-topic"

        await kafka_consumer.handle_workflow_result(
            execution_task,
            correlation_id,
            workflow_id,
            reply_topic
        )

        expected_response = {
            "status": "Workflow Completed",
            "result": f" Failed to execute workflow '{workflow_id}' (execution error).",
            "workflow_status": "failed"
        }

        kafka_consumer.producer.send.assert_called_once_with(
            reply_topic,
            expected_response,
            headers=[("correlationId", b"test-correlation-id")]
        )

    @pytest.mark.asyncio
    async def test_process_message_unknown_topic(self, kafka_consumer, mock_consumer_record):
        """
        Test handling of messages from unknown topics.
        """
        mock_consumer_record.topic = "unknown-topic"
        
        await kafka_consumer.process_message(mock_consumer_record, "unknown-topic")
        
        kafka_consumer.consumer.commit.assert_called_once()

    def test_decode_headers(self, kafka_consumer):
        """
        Test Kafka message headers decoding.
        """
        headers = [
            ("correlationId", b"test-correlation-id"),
            ("reply-topic", b"test-reply-topic")
        ]

        decoded = kafka_consumer.decode_headers(headers)

        assert decoded["correlationId"] == "test-correlation-id"
        assert decoded["reply-topic"] == "test-reply-topic"