import pytest
from unittest.mock import AsyncMock, patch, Mock
import json
from app.utils.agentic_operations import format_schema, resolve_switch_case
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken


class TestAgenticOperations:
    """
    Test suite for agentic operations.
    Tests schema formatting and switch case resolution using AI agents.
    """

    @pytest.fixture
    def mock_model_client(self):
        """
        Provides a mock OpenAIChatCompletionClient.
        """
        return Mock()

    @pytest.fixture
    def mock_assistant_agent(self):
        """
        Provides a mock AssistantAgent with predefined responses.
        """
        agent = AsyncMock()
        return agent

    @pytest.mark.asyncio
    async def test_format_schema_basic(self, mock_model_client, mock_assistant_agent):
        """
        Test basic schema formatting with simple input and output schemas.
        """
        input_schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer"}
            }
        }
        output_schema = {
            "type": "object",
            "properties": {
                "user_name": {"type": "string"},
                "user_age": {"type": "integer"}
            }
        }
        previous_results = {"user_name": "<PERSON>", "user_age": 30}
        current_tool_params = {"name": "${user_name}"}

        # Mock the agent's response
        mock_response = Mock()
        mock_response.chat_message.content = '{"name": "John"}'
        mock_assistant_agent.on_messages.return_value = mock_response

        with patch('app.utils.agentic_operations.OpenAIChatCompletionClient', 
                  return_value=mock_model_client), \
             patch('app.utils.agentic_operations.AssistantAgent', 
                  return_value=mock_assistant_agent):
            
            result = await format_schema(
                input_schema, 
                output_schema, 
                previous_results, 
                current_tool_params
            )

            assert isinstance(result, dict)
            assert result.get("name") == "John"
            mock_assistant_agent.on_messages.assert_called_once()

    @pytest.mark.asyncio
    async def test_format_schema_complex_json(self, mock_model_client, mock_assistant_agent):
        """
        Test schema formatting with complex JSON response including code blocks.
        """
        mock_response = Mock()
        mock_response.chat_message.content = '''```json
        {
            "name": "John",
            "age": 30
        }
        ```'''
        mock_assistant_agent.on_messages.return_value = mock_response

        with patch('app.utils.agentic_operations.OpenAIChatCompletionClient', 
                  return_value=mock_model_client), \
             patch('app.utils.agentic_operations.AssistantAgent', 
                  return_value=mock_assistant_agent):
            
            result = await format_schema({}, {}, {}, {})
            
            assert isinstance(result, dict)
            assert result.get("name") == "John"
            assert result.get("age") == 30

    @pytest.mark.asyncio
    async def test_resolve_switch_case_true(self, mock_model_client, mock_assistant_agent):
        """
        Test switch case resolution returning True.
        """
        mock_response = Mock()
        mock_response.chat_message.content = "True"
        mock_assistant_agent.on_messages.return_value = mock_response

        with patch('app.utils.agentic_operations.OpenAIChatCompletionClient', 
                  return_value=mock_model_client), \
             patch('app.utils.agentic_operations.AssistantAgent', 
                  return_value=mock_assistant_agent):
            
            result = await resolve_switch_case(
                "This is a positive review!", 
                "positive sentiment"
            )
            
            assert result is True
            mock_assistant_agent.on_messages.assert_called_once()

    @pytest.mark.asyncio
    async def test_resolve_switch_case_false(self, mock_model_client, mock_assistant_agent):
        """
        Test switch case resolution returning False.
        """
        mock_response = Mock()
        mock_response.chat_message.content = "False"
        mock_assistant_agent.on_messages.return_value = mock_response

        with patch('app.utils.agentic_operations.OpenAIChatCompletionClient', 
                  return_value=mock_model_client), \
             patch('app.utils.agentic_operations.AssistantAgent', 
                  return_value=mock_assistant_agent):
            
            result = await resolve_switch_case(
                "This is a negative review!", 
                "positive sentiment"
            )
            
            assert result is False

    @pytest.mark.asyncio
    async def test_resolve_switch_case_invalid_response(self, mock_model_client, mock_assistant_agent):
        """
        Test switch case resolution with invalid response from agent.
        """
        mock_response = Mock()
        mock_response.chat_message.content = "Invalid response"
        mock_assistant_agent.on_messages.return_value = mock_response

        with patch('app.utils.agentic_operations.OpenAIChatCompletionClient', 
                  return_value=mock_model_client), \
             patch('app.utils.agentic_operations.AssistantAgent', 
                  return_value=mock_assistant_agent):
            
            result = await resolve_switch_case(
                "Some input", 
                "expected value"
            )
            
            assert result is False

    @pytest.mark.asyncio
    async def test_format_schema_with_placeholders(self, mock_model_client, mock_assistant_agent):
        """
        Test schema formatting with multiple placeholders in parameters.
        """
        input_schema = {
            "type": "object",
            "properties": {
                "user_info": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "email": {"type": "string"}
                    }
                }
            }
        }
        current_tool_params = {
            "user_info": {
                "name": "${user_name}",
                "email": "${user_email}"
            }
        }
        previous_results = {
            "user_name": "John Doe",
            "user_email": "<EMAIL>"
        }

        mock_response = Mock()
        mock_response.chat_message.content = '''```json
        {
            "user_info": {
                "name": "John Doe",
                "email": "<EMAIL>"
            }
        }
        ```'''
        mock_assistant_agent.on_messages.return_value = mock_response

        with patch('app.utils.agentic_operations.OpenAIChatCompletionClient', 
                  return_value=mock_model_client), \
             patch('app.utils.agentic_operations.AssistantAgent', 
                  return_value=mock_assistant_agent):
            
            result = await format_schema(
                input_schema, 
                {}, 
                previous_results, 
                current_tool_params
            )
            
            assert isinstance(result, dict)
            assert result.get("user_info", {}).get("name") == "John Doe"
            assert result.get("user_info", {}).get("email") == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_resolve_switch_case_complex_data(self, mock_model_client, mock_assistant_agent):
        """
        Test switch case resolution with complex data structures.
        """
        actual_value = {
            "status": "completed",
            "details": {
                "success": True,
                "items_processed": 5
            }
        }
        expected_value = "completed"

        mock_response = Mock()
        mock_response.chat_message.content = "True"
        mock_assistant_agent.on_messages.return_value = mock_response

        with patch('app.utils.agentic_operations.OpenAIChatCompletionClient', 
                  return_value=mock_model_client), \
             patch('app.utils.agentic_operations.AssistantAgent', 
                  return_value=mock_assistant_agent):
            
            result = await resolve_switch_case(actual_value, expected_value)
            
            assert result is True
            # Verify the message sent to the agent contains the correct JSON
            call_args = mock_assistant_agent.on_messages.call_args[0][0]
            message_content = call_args[0].content
            assert '"status": "completed"' in message_content
            assert '"completed"' in message_content

    @pytest.mark.asyncio
    async def test_format_schema_error_handling(self, mock_model_client, mock_assistant_agent):
        """
        Test error handling in schema formatting with invalid JSON response.
        """
        mock_response = Mock()
        mock_response.chat_message.content = 'Invalid JSON'
        mock_assistant_agent.on_messages.return_value = mock_response

        with patch('app.utils.agentic_operations.OpenAIChatCompletionClient', 
                  return_value=mock_model_client), \
             patch('app.utils.agentic_operations.AssistantAgent', 
                  return_value=mock_assistant_agent), \
             pytest.raises(json.JSONDecodeError):
            
            await format_schema({}, {}, {}, {})