"""
Test script for Redis key extraction logic.
This script tests the key extraction logic in the Redis event listener.

Usage:
    poetry run python test_key_extraction.py
"""

import json
import time
import uuid
import sys
import logging
import re
from app.services.db_connections.redis_connections import RedisManager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Set to DEBUG for more detailed logs
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("KeyExtractionTest")

def test_key_extraction():
    """
    Test the key extraction logic used in the Redis event listener.
    
    This test:
    1. Creates sample Redis keyspace event messages
    2. Tests the key extraction logic
    3. Verifies the correct keys and IDs are extracted
    """
    # Sample Redis keyspace event messages
    result_message = {
        "type": "pmessage",
        "pattern": b"__keyspace@3__:*",
        "channel": b"__keyspace@3__:result:transition1-12345",
        "data": b"del"
    }
    
    state_message = {
        "type": "pmessage",
        "pattern": b"__keyspace@4__:*",
        "channel": b"__keyspace@4__:workflow_state:workflow-67890",
        "data": b"del"
    }
    
    # Patterns for extracting IDs from Redis keys
    result_key_pattern = re.compile(r"result:(.+)")
    state_key_pattern = re.compile(r"workflow_state:(.+)")
    
    # Test result key extraction
    logger.info("Testing result key extraction...")
    
    # Extract the key from the channel
    channel = result_message["channel"]
    logger.debug(f"Channel type: {type(channel)}, value: {channel}")
    
    if isinstance(channel, bytes):
        channel = channel.decode("utf-8")
        logger.debug(f"Decoded channel: {channel}")
    
    # Extract the full key including the prefix
    # Format: __keyspace@{db}__:result:{transition_id}
    parts = channel.split(":")
    if len(parts) < 2:
        logger.error(f"Invalid channel format: {channel}")
        return False
        
    # Reconstruct the full key (result:{transition_id})
    key = ":".join(parts[1:])
    logger.debug(f"Extracted key: {key}")
    
    # Handle data
    event = result_message["data"]
    logger.debug(f"Event data type: {type(event)}, value: {event}")
    
    if isinstance(event, bytes):
        event = event.decode("utf-8")
        logger.debug(f"Decoded event: {event}")
    
    # Check if this is a 'del' event for a result key
    if event == "del" and key.startswith("result:"):
        match = result_key_pattern.match(key)
        if match:
            transition_id = match.group(1)
            logger.info(f"Successfully extracted transition ID: {transition_id}")
        else:
            logger.error(f"Failed to match result key pattern: {key}")
            return False
    else:
        logger.error(f"Not a 'del' event for a result key: event={event}, key={key}")
        return False
    
    # Test state key extraction
    logger.info("Testing state key extraction...")
    
    # Extract the key from the channel
    channel = state_message["channel"]
    logger.debug(f"Channel type: {type(channel)}, value: {channel}")
    
    if isinstance(channel, bytes):
        channel = channel.decode("utf-8")
        logger.debug(f"Decoded channel: {channel}")
    
    # Extract the full key including the prefix
    # Format: __keyspace@{db}__:workflow_state:{workflow_id}
    parts = channel.split(":")
    if len(parts) < 2:
        logger.error(f"Invalid channel format: {channel}")
        return False
        
    # Reconstruct the full key (workflow_state:{workflow_id})
    key = ":".join(parts[1:])
    logger.debug(f"Extracted key: {key}")
    
    # Handle data
    event = state_message["data"]
    logger.debug(f"Event data type: {type(event)}, value: {event}")
    
    if isinstance(event, bytes):
        event = event.decode("utf-8")
        logger.debug(f"Decoded event: {event}")
    
    # Check if this is a 'del' event for a workflow state key
    if event == "del" and key.startswith("workflow_state:"):
        match = state_key_pattern.match(key)
        if match:
            workflow_id = match.group(1)
            logger.info(f"Successfully extracted workflow ID: {workflow_id}")
        else:
            logger.error(f"Failed to match state key pattern: {key}")
            return False
    else:
        logger.error(f"Not a 'del' event for a workflow state key: event={event}, key={key}")
        return False
    
    logger.info("Key extraction test completed successfully!")
    return True

if __name__ == "__main__":
    logger.info("Starting key extraction test...")
    
    try:
        success = test_key_extraction()
        if success:
            logger.info("✅ Test passed: Key extraction logic works correctly")
            sys.exit(0)
        else:
            logger.error("❌ Test failed: Key extraction logic did not work as expected")
            sys.exit(1)
    except Exception as e:
        logger.exception(f"❌ Test failed with exception: {e}")
        sys.exit(1)
