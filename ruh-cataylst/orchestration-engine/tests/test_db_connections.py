"""
Test script to verify database connection initialization.
"""

import asyncio
import sys
import os
import logging

# Configure basic logging to see output
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("Starting test script...")

from app.services.db_connections.db_initializer import (
    initialize_db_connections,
    get_db_connections,
    close_db_connections,
)
from app.utils.enhanced_logger import get_logger

logger = get_logger("TestDBConnections")
logger.setLevel(logging.INFO)


async def test_db_connections():
    """Test the database connection initialization."""
    print("Testing database connection initialization...")
    logger.info("Testing database connection initialization...")

    try:
        # Initialize connections
        print("Initializing database connections...")
        logger.info("Initializing database connections...")
        results_redis, state_redis, postgres, redis_event_listener = (
            initialize_db_connections()
        )

        print(f"Results Redis: {results_redis}")
        print(f"State Redis: {state_redis}")
        print(f"PostgreSQL: {postgres}")
        print(f"Redis Event Listener: {redis_event_listener}")

        # Check connections
        if results_redis and results_redis.is_connected():
            print("Results Redis connection successful")
            logger.info("Results Redis connection successful")
        else:
            print("Results Redis connection failed")
            logger.error("Results Redis connection failed")

        if state_redis and state_redis.is_connected():
            print("State Redis connection successful")
            logger.info("State Redis connection successful")
        else:
            print("State Redis connection failed")
            logger.error("State Redis connection failed")

        if postgres and postgres.is_connected():
            print("PostgreSQL connection successful")
            logger.info("PostgreSQL connection successful")
        else:
            print("PostgreSQL connection failed")
            logger.error("PostgreSQL connection failed")

        if redis_event_listener:
            print("Redis event listener initialized")
            logger.info("Redis event listener initialized")
        else:
            print("Redis event listener initialization failed")
            logger.error("Redis event listener initialization failed")

        # Test get_db_connections
        print("Testing get_db_connections...")
        logger.info("Testing get_db_connections...")
        connections = get_db_connections()
        if len(connections) == 4:
            print("get_db_connections returned all connections")
            logger.info("get_db_connections returned all connections")
        else:
            print(
                f"get_db_connections returned {len(connections)} connections, expected 4"
            )
            logger.error(
                f"get_db_connections returned {len(connections)} connections, expected 4"
            )

        # Close connections
        print("Closing database connections...")
        logger.info("Closing database connections...")
        close_db_connections()
        print("Database connections closed")
        logger.info("Database connections closed")

        # Test that connections are re-initialized after closing
        print("Re-initializing database connections after closing...")
        logger.info("Re-initializing database connections after closing...")
        new_connections = get_db_connections()
        if len(new_connections) == 4:
            print("Database connections re-initialized successfully")
            logger.info("Database connections re-initialized successfully")
        else:
            print("Failed to re-initialize database connections")
            logger.error("Failed to re-initialize database connections")

        # Close connections again
        print("Closing database connections again...")
        logger.info("Closing database connections again...")
        close_db_connections()
        print("Test completed")
        logger.info("Test completed")
    except Exception as e:
        print(f"Error during test: {e}")
        logger.error(f"Error during test: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(test_db_connections())
