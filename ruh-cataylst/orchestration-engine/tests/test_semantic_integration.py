#!/usr/bin/env python3
"""
Simple integration test for semantic type functionality.
This test verifies that the semantic type extraction and formatting works end-to-end
without requiring full application configuration.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.semantic_type_extractor import extract_semantic_type, extract_semantic_type_for_nested
from app.utils.helper_functions import format_execution_result


def test_semantic_type_extraction():
    """Test basic semantic type extraction functionality."""
    print("Testing semantic type extraction...")
    
    # Test basic email extraction
    field_def = {
        "field_name": "user_email",
        "data_type": {"type": "string", "format": "email"}
    }
    result = extract_semantic_type(field_def)
    assert result == "email", f"Expected 'email', got '{result}'"
    print("✓ Email semantic type extraction works")
    
    # Test default fallback
    field_def = {
        "field_name": "description",
        "data_type": {"type": "string"}
    }
    result = extract_semantic_type(field_def)
    assert result == "string", f"Expected 'string', got '{result}'"
    print("✓ Default semantic type fallback works")
    
    # Test array semantic type
    field_def = {
        "field_name": "email_list",
        "data_type": {
            "type": "array",
            "items": {"type": "string", "format": "email"}
        }
    }
    result = extract_semantic_type_for_nested(field_def, "array")
    assert result == "array_of_email", f"Expected 'array_of_email', got '{result}'"
    print("✓ Array semantic type extraction works")


def test_format_execution_result_integration():
    """Test end-to-end format execution result with semantic types."""
    print("\nTesting format execution result integration...")
    
    # Test schema with semantic types
    output_schema = {
        "predefined_fields": [
            {
                "field_name": "user_email",
                "data_type": {
                    "type": "string",
                    "format": "email",
                    "description": "User email address"
                }
            },
            {
                "field_name": "website_url",
                "data_type": {
                    "type": "string", 
                    "format": "url",
                    "description": "Website URL"
                }
            },
            {
                "field_name": "created_at",
                "data_type": {
                    "type": "string",
                    "format": "datetime",
                    "description": "Creation timestamp"
                }
            },
            {
                "field_name": "description",
                "data_type": {
                    "type": "string",
                    "description": "Description field"
                }
            }
        ]
    }
    
    execution_result = [{
        "user_email": "<EMAIL>",
        "website_url": "https://example.com",
        "created_at": "2024-01-15T10:30:00Z",
        "description": "Test description"
    }]
    
    result = format_execution_result(output_schema, execution_result)
    
    # Verify result structure
    assert isinstance(result, list), "Result should be a list"
    assert len(result) == 4, f"Expected 4 items, got {len(result)}"
    print("✓ Result structure is correct")
    
    # Find and verify email field
    email_item = next(item for item in result if item["property_name"] == "user_email")
    assert email_item["data"] == "<EMAIL>"
    assert email_item["data_type"] == "string"
    assert email_item["semantic_type"] == "email"
    print("✓ Email field with semantic type works")
    
    # Find and verify URL field
    url_item = next(item for item in result if item["property_name"] == "website_url")
    assert url_item["data"] == "https://example.com"
    assert url_item["data_type"] == "string"
    assert url_item["semantic_type"] == "url"
    print("✓ URL field with semantic type works")
    
    # Find and verify datetime field
    datetime_item = next(item for item in result if item["property_name"] == "created_at")
    assert datetime_item["data"] == "2024-01-15T10:30:00Z"
    assert datetime_item["data_type"] == "string"
    assert datetime_item["semantic_type"] == "datetime"
    print("✓ DateTime field with semantic type works")
    
    # Find and verify description field (default semantic type)
    desc_item = next(item for item in result if item["property_name"] == "description")
    assert desc_item["data"] == "Test description"
    assert desc_item["data_type"] == "string"
    assert desc_item["semantic_type"] == "string"  # default
    print("✓ Description field with default semantic type works")


def test_array_semantic_types():
    """Test array handling with semantic types."""
    print("\nTesting array semantic types...")
    
    output_schema = {
        "predefined_fields": [
            {
                "field_name": "email_list",
                "data_type": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "format": "email"
                    },
                    "description": "List of email addresses"
                }
            }
        ]
    }
    
    execution_result = [{
        "email_list": ["<EMAIL>", "<EMAIL>"]
    }]
    
    result = format_execution_result(output_schema, execution_result)
    
    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["property_name"] == "email_list"
    assert result[0]["data_type"] == "array"
    assert result[0]["semantic_type"] == "array_of_email"
    
    # Check array items have semantic types
    array_data = result[0]["data"]
    assert isinstance(array_data, list)
    assert len(array_data) == 2
    assert array_data[0]["semantic_type"] == "email"
    assert array_data[1]["semantic_type"] == "email"
    print("✓ Array with semantic types works")


def test_unknown_property_handling():
    """Test handling of unknown properties."""
    print("\nTesting unknown property handling...")
    
    output_schema = {"predefined_fields": []}
    execution_result = [{"unknown_field": "test value"}]
    
    result = format_execution_result(output_schema, execution_result)
    
    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0]["property_name"] == "unknown_field"
    assert result[0]["data"] == "test value"
    assert result[0]["data_type"] == "unknown"
    assert result[0]["semantic_type"] == "string"  # default for unknown
    print("✓ Unknown property handling works")


def main():
    """Run all integration tests."""
    print("🚀 Running Semantic Type Integration Tests")
    print("=" * 50)
    
    try:
        test_semantic_type_extraction()
        test_format_execution_result_integration()
        test_array_semantic_types()
        test_unknown_property_handling()
        
        print("\n" + "=" * 50)
        print("🎉 All integration tests passed!")
        print("✅ Semantic type extraction and formatting is working correctly")
        print("✅ Enhanced result format includes semantic_type field")
        print("✅ Default 'string' semantic type when format is empty")
        print("✅ Array and object semantic types work properly")
        print("✅ Unknown properties get default semantic type")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
