"""
Test orchestration engine specific result handling.

This test suite validates that the universal data propagation system properly handles
the orchestration engine's specific nested result structure patterns.
"""

import pytest
from unittest.mock import Mock

# Import the classes we're testing
from app.core_.workflow_utils import WorkflowUtils


class TestOrchestrationEngineResultHandling:
    """Test suite for orchestration engine specific result handling."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="orchestration-test-workflow")

    def test_triple_nested_result_with_handles(self, workflow_utils):
        """Test extraction from triple nested result structure with handles."""
        # This matches the structure from your merge_data component
        source_results = {
            "result": {"result": {"result": {"value": '{"new":"112"}', "social": "5"}}}
        }

        # Test extracting specific handles
        value_result = workflow_utils._extract_data_by_handle(
            source_results, "value", "test-transition"
        )
        social_result = workflow_utils._extract_data_by_handle(
            source_results, "social", "test-transition"
        )

        assert value_result == '{"new":"112"}'
        assert social_result == "5"

    def test_triple_nested_result_direct_value(self, workflow_utils):
        """Test extraction from triple nested result with direct singular value."""
        # This would be for components that return a single value
        source_results = {"result": {"result": {"result": "direct_singular_value"}}}

        # For singular values, any handle request should return the value
        result = workflow_utils._extract_data_by_handle(
            source_results, "output_data", "test-transition"
        )

        assert result == "direct_singular_value"

    def test_double_nested_result_with_handles(self, workflow_utils):
        """Test extraction from double nested result structure."""
        source_results = {
            "result": {
                "result": {
                    "processed_text": "Processed content",
                    "metadata": {"count": 42},
                }
            }
        }

        result = workflow_utils._extract_data_by_handle(
            source_results, "processed_text", "test-transition"
        )

        assert result == "Processed content"

    def test_single_nested_result_with_handles(self, workflow_utils):
        """Test extraction from single nested result structure."""
        source_results = {
            "result": {"api_response": "API result data", "status": "success"}
        }

        result = workflow_utils._extract_data_by_handle(
            source_results, "api_response", "test-transition"
        )

        assert result == "API result data"

    def test_complex_nested_structure_like_merge_data(self, workflow_utils):
        """Test with the exact structure from your merge_data component."""
        source_results = {
            "transition_id": "transition-MergeDataComponent-1748518110627",
            "node_id": "MergeDataComponent",
            "tool_name": "MergeDataComponent",
            "result": {
                "request_id": "6f1f3354-4496-401b-9842-4fcdc004df99",
                "status": "success",
                "result": {
                    "status": "success",
                    "result": {"value": '{"new":"112"}', "social": "5"},
                },
            },
            "status": "completed",
            "sequence": 4,
            "workflow_status": "running",
            "approval_required": False,
        }

        # Test extracting the specific handles
        value_result = workflow_utils._extract_data_by_handle(
            source_results, "value", "test-transition"
        )
        social_result = workflow_utils._extract_data_by_handle(
            source_results, "social", "test-transition"
        )

        assert value_result == '{"new":"112"}'
        assert social_result == "5"

    def test_current_api_request_node_structure(self, workflow_utils):
        """Test with your current ApiRequestNode result structure."""
        source_results = {
            "request_id": "04bc3144-7941-4b41-8241-17d832c66a02",
            "component_type": "ApiRequestNode",
            "status": "success",
            "timestamp": 1748523186.756689,
            "result": {
                "result": '{"merge_text":"helloe","hello":"markrint","new_hello":"New_markrint"}'
            },
            "error": None,
        }

        # Test extracting the result data - should get the JSON string
        result_data = workflow_utils._extract_data_by_handle(
            source_results, "result", "test-transition"
        )

        # Should extract the JSON string from result.result
        expected_json = (
            '{"merge_text":"helloe","hello":"markrint","new_hello":"New_markrint"}'
        )
        assert result_data == expected_json

    def test_current_api_request_node_structure_any_handle(self, workflow_utils):
        """Test that any handle request returns the result data."""
        source_results = {
            "request_id": "04bc3144-7941-4b41-8241-17d832c66a02",
            "component_type": "ApiRequestNode",
            "status": "success",
            "timestamp": 1748523186.756689,
            "result": {
                "result": '{"merge_text":"helloe","hello":"markrint","new_hello":"New_markrint"}'
            },
            "error": None,
        }

        # Test that any handle name should return the result data
        for handle_name in ["body", "output_data", "api_response", "data"]:
            result_data = workflow_utils._extract_data_by_handle(
                source_results, handle_name, "test-transition"
            )

            expected_json = (
                '{"merge_text":"helloe","hello":"markrint","new_hello":"New_markrint"}'
            )
            assert result_data == expected_json, f"Failed for handle: {handle_name}"

    def test_fallback_to_legacy_patterns(self, workflow_utils):
        """Test that legacy patterns still work for backward compatibility."""
        source_results = {"output_data": {"legacy_field": "legacy_value"}}

        result = workflow_utils._extract_data_by_handle(
            source_results, "legacy_field", "test-transition"
        )

        assert result == "legacy_value"

    def test_direct_field_access(self, workflow_utils):
        """Test direct field access for simple structures."""
        source_results = {"direct_field": "direct_value"}

        result = workflow_utils._extract_data_by_handle(
            source_results, "direct_field", "test-transition"
        )

        assert result == "direct_value"

    def test_missing_handle_returns_none(self, workflow_utils):
        """Test that missing handles return None."""
        source_results = {"result": {"result": {"result": {"existing_field": "value"}}}}

        result = workflow_utils._extract_data_by_handle(
            source_results, "non_existent_field", "test-transition"
        )

        assert result is None

    def test_empty_result_structure(self, workflow_utils):
        """Test handling of empty result structures."""
        source_results = {"result": {"result": {"result": {}}}}

        result = workflow_utils._extract_data_by_handle(
            source_results, "any_field", "test-transition"
        )

        assert result is None

    def test_nested_structure_summary_helper(self, workflow_utils):
        """Test the nested structure summary helper method."""
        complex_data = {
            "level1": {"level2": {"level3": {"deep_field": "deep_value"}}},
            "simple_field": "simple_value",
        }

        summary = workflow_utils._get_nested_structure_summary(complex_data)

        # Should contain information about the structure
        assert "level1" in summary
        assert "simple_field" in summary
        assert "dict" in summary.lower()

    def test_list_result_extraction(self, workflow_utils):
        """Test extraction from result structures containing lists."""
        # This matches the structure from the logs: {"result": {"result": [{"script": "..."}]}}
        source_results = {
            "result": {
                "result": [
                    {
                        "title": "ai",
                        "script": "test script content",
                        "script_type": "TOPIC",
                        "video_type": "SHORT",
                    }
                ]
            }
        }

        # Test extracting the script field
        script_result = workflow_utils._extract_data_by_handle(
            source_results, "script", "test-transition"
        )

        assert script_result == "test script content"

        # Test extracting other fields from the list
        title_result = workflow_utils._extract_data_by_handle(
            source_results, "title", "test-transition"
        )
        assert title_result == "ai"

        script_type_result = workflow_utils._extract_data_by_handle(
            source_results, "script_type", "test-transition"
        )
        assert script_type_result == "TOPIC"

    def test_list_result_extraction_multiple_items(self, workflow_utils):
        """Test extraction from result structures with multiple list items."""
        source_results = {
            "result": {
                "result": [
                    {"script": "first script", "title": "first"},
                    {"script": "second script", "title": "second"},
                ]
            }
        }

        # Should extract from the first item in the list
        script_result = workflow_utils._extract_data_by_handle(
            source_results, "script", "test-transition"
        )
        assert script_result == "first script"

        title_result = workflow_utils._extract_data_by_handle(
            source_results, "title", "test-transition"
        )
        assert title_result == "first"

    def test_extract_by_path_with_lists(self, workflow_utils):
        """Test the _extract_by_path method with list structures."""
        data = {
            "result": {"result": [{"script": "test content", "metadata": {"count": 1}}]}
        }

        # Test extracting through list structure
        result = workflow_utils._extract_by_path(data, "result.result.script")
        assert result == "test content"

        # Test nested extraction through list
        result = workflow_utils._extract_by_path(data, "result.result.metadata")
        assert result == {"count": 1}

    def test_single_item_list_unwrapping(self, workflow_utils):
        """Test that single-item lists are unwrapped properly."""
        data = {"result": [{"final_value": "unwrapped"}]}

        # Should unwrap the single-item list
        result = workflow_utils._extract_by_path(data, "result")
        assert result == {"final_value": "unwrapped"}

        # Should also work with deeper paths
        result = workflow_utils._extract_by_path(data, "result.final_value")
        assert result == "unwrapped"

    @pytest.mark.asyncio
    async def test_end_to_end_parameter_resolution_with_orchestration_structure(
        self, workflow_utils
    ):
        """Test end-to-end parameter resolution with orchestration engine structure."""
        # Mock state manager to return orchestration engine style results
        mock_state_manager = Mock()
        mock_state_manager.get_transition_result.return_value = {
            "result": {
                "result": {
                    "result": {
                        "output_data": "processed_content",
                        "metadata": "additional_info",
                    }
                }
            }
        }
        workflow_utils.state_manager = mock_state_manager

        input_data_configs = [
            {
                "from_transition_id": "source-transition",
                "handle_mappings": [
                    {
                        "source_handle_id": "output_data",
                        "target_handle_id": "input_field",
                        "edge_id": "test-edge",
                    }
                ],
            }
        ]

        current_tool_params = {"static_param": "static_value"}

        # Test parameter resolution
        result = await workflow_utils._format_tool_parameters(
            {}, input_data_configs, "target-transition", current_tool_params
        )

        # Should successfully resolve the parameter
        assert "input_field" in result
        assert result["input_field"] == "processed_content"
        assert "static_param" in result
        assert result["static_param"] == "static_value"

    def test_wrapped_result_structure_from_transition_handler(self, workflow_utils):
        """Test the wrapped result structure that transition handler now creates."""
        # This simulates the new wrapped structure from transition handler
        wrapped_result = {
            "transition_id": "transition-CombineTextComponent-1748520539598",
            "node_id": "CombineTextComponent",
            "tool_name": "CombineTextComponent",
            "result": {
                "result": '{"merge_text":"helloe","hello":"markrint","new_hello":"New_markrint"}'
            },
            "status": "completed",
            "timestamp": 1748523186.756689,
        }

        # Test extracting with any handle name should work
        for handle_name in ["result", "body", "output_data", "api_response"]:
            result_data = workflow_utils._extract_data_by_handle(
                wrapped_result, handle_name, "test-transition"
            )

            expected_json = (
                '{"merge_text":"helloe","hello":"markrint","new_hello":"New_markrint"}'
            )
            assert result_data == expected_json, f"Failed for handle: {handle_name}"

    def test_raw_execution_result_compatibility(self, workflow_utils):
        """Test that raw execution results (before wrapping) still work for backward compatibility."""
        # This is what the system used to receive directly
        raw_result = (
            '{"merge_text":"helloe","hello":"markrint","new_hello":"New_markrint"}'
        )

        # Should handle raw strings gracefully (though not ideal)
        result_data = workflow_utils._extract_data_by_handle(
            {"raw_data": raw_result}, "raw_data", "test-transition"
        )

        assert result_data == raw_result


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
