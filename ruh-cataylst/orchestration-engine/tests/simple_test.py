#!/usr/bin/env python3
"""
Simple test to verify the transition-specific parameter application fix.
"""

def simple_test():
    """
    Test the logic manually to understand what's happening.
    """
    print("Testing transition-specific field application logic...")
    
    # Simulate the user's scenario
    user_dependent_fields = ["url", "body"]
    user_payload_template = {
        "url": {
            "value": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
            "transition_id": "ApiRequestNode-1748853965235"
        },
        "body": {
            "value": {
                "title": "Optimizing Business Processes: The Role of AI Agent Workflows with Ruh.ai",
                "summary": "Delve into the transformative impact of AI agent workflows in enhancing business efficiency.",
                "category": "AI Innovation"
            },
            "transition_id": "ApiRequestNode-1748854246219"
        }
    }
    
    # Create transitions mapping
    transitions_by_id = {
        "ApiRequestNode-1748853965235": {"id": "ApiRequestNode-1748853965235"},
        "ApiRequestNode-1748854246219": {"id": "ApiRequestNode-1748854246219"},
        "SomeOtherNode-123456789": {"id": "SomeOtherNode-123456789"}
    }
    
    # Test each transition
    transitions = [
        {"id": "ApiRequestNode-1748853965235", "name": "Transition 1"},
        {"id": "ApiRequestNode-1748854246219", "name": "Transition 2"},
        {"id": "SomeOtherNode-123456789", "name": "Transition 3"}
    ]
    
    for transition in transitions:
        transition_id = transition["id"]
        transition_name = transition["name"]
        
        print(f"\n--- Processing {transition_name} (ID: {transition_id}) ---")
        
        # Test URL field
        field_name = "url"
        field_updated = False
        
        print(f"Testing field: {field_name}")
        
        # Check for field name match
        if field_name in user_payload_template:
            payload_value = user_payload_template[field_name]
            print(f"  Found field in payload: {payload_value}")
            
            # Check if the payload value is a dictionary with transition_id
            if (
                isinstance(payload_value, dict)
                and "value" in payload_value
                and "transition_id" in payload_value
            ):
                print(f"  Field has transition_id: {payload_value['transition_id']}")
                
                # If this is the specified transition, update the field
                if payload_value["transition_id"] == transition_id:
                    print(f"  ✅ MATCH! Applying value to {transition_name}")
                    field_updated = True
                else:
                    print(f"  ❌ NO MATCH! Skipping {transition_name} (intended for {payload_value['transition_id']})")
            else:
                print(f"  Simple value (not transition-specific)")
                field_updated = True
        
        # Handle user-dependent fields that weren't updated by transition ID
        if not field_updated and field_name in user_dependent_fields:
            print(f"  Field not updated and is user-dependent, checking fallback logic...")
            
            if field_name in user_payload_template:
                payload_value = user_payload_template[field_name]
                
                # If it's a dict with transition_id, only apply if transition doesn't exist
                if (
                    isinstance(payload_value, dict)
                    and "value" in payload_value
                    and "transition_id" in payload_value
                ):
                    target_transition_id = payload_value.get("transition_id")
                    if target_transition_id not in transitions_by_id:
                        print(f"  Target transition {target_transition_id} doesn't exist, using as fallback")
                        field_updated = True
                    else:
                        print(f"  Target transition {target_transition_id} exists, NOT applying to {transition_name}")
                else:
                    print(f"  Simple value, applying to all matching fields")
                    field_updated = True
        
        print(f"  Final result for {field_name} in {transition_name}: {'APPLIED' if field_updated else 'NOT APPLIED'}")
        
        # Test BODY field
        field_name = "body"
        field_updated = False
        
        print(f"Testing field: {field_name}")
        
        # Check for field name match
        if field_name in user_payload_template:
            payload_value = user_payload_template[field_name]
            print(f"  Found field in payload: transition_id = {payload_value.get('transition_id', 'N/A')}")
            
            # Check if the payload value is a dictionary with transition_id
            if (
                isinstance(payload_value, dict)
                and "value" in payload_value
                and "transition_id" in payload_value
            ):
                print(f"  Field has transition_id: {payload_value['transition_id']}")
                
                # If this is the specified transition, update the field
                if payload_value["transition_id"] == transition_id:
                    print(f"  ✅ MATCH! Applying value to {transition_name}")
                    field_updated = True
                else:
                    print(f"  ❌ NO MATCH! Skipping {transition_name} (intended for {payload_value['transition_id']})")
            else:
                print(f"  Simple value (not transition-specific)")
                field_updated = True
        
        # Handle user-dependent fields that weren't updated by transition ID
        if not field_updated and field_name in user_dependent_fields:
            print(f"  Field not updated and is user-dependent, checking fallback logic...")
            
            if field_name in user_payload_template:
                payload_value = user_payload_template[field_name]
                
                # If it's a dict with transition_id, only apply if transition doesn't exist
                if (
                    isinstance(payload_value, dict)
                    and "value" in payload_value
                    and "transition_id" in payload_value
                ):
                    target_transition_id = payload_value.get("transition_id")
                    if target_transition_id not in transitions_by_id:
                        print(f"  Target transition {target_transition_id} doesn't exist, using as fallback")
                        field_updated = True
                    else:
                        print(f"  Target transition {target_transition_id} exists, NOT applying to {transition_name}")
                else:
                    print(f"  Simple value, applying to all matching fields")
                    field_updated = True
        
        print(f"  Final result for {field_name} in {transition_name}: {'APPLIED' if field_updated else 'NOT APPLIED'}")


if __name__ == "__main__":
    simple_test()
