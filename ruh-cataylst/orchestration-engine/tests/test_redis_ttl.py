"""
Test script for Redis TTL and PostgreSQL archiving.
This script tests the Redis TTL settings and PostgreSQL archiving functionality.

Usage:
    poetry run python test_redis_ttl.py
"""

import json
import time
import uuid
import sys
import logging
from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.services.db_connections.redis_event_listener import get_redis_event_listener
from app.core_.state_manager import WorkflowStateManager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Set to DEBUG for more detailed logs
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger("RedisTTLTest")


def test_redis_ttl():
    """
    Test the Redis TTL settings and PostgreSQL archiving functionality.

    This test:
    1. Creates a unique workflow ID
    2. Stores workflow state and transition results in Redis with TTL
    3. Verifies the TTL is set correctly
    4. Waits for the Redis keys to expire
    5. Verifies the data is archived to PostgreSQL
    """
    # Generate a unique workflow ID for this test
    workflow_id = f"test-workflow-{uuid.uuid4()}"
    correlation_id = workflow_id  # Using the same ID for simplicity

    logger.info(f"Starting test with workflow ID: {workflow_id}")

    # Initialize Redis managers
    results_redis_manager = RedisManager(db_index=int(settings.redis_results_db_index))
    state_redis_manager = RedisManager(db_index=int(settings.redis_state_db_index))

    # Initialize PostgreSQL manager
    postgres_manager = get_postgres_manager()

    # Initialize workflow state manager
    state_manager = WorkflowStateManager(workflow_id=workflow_id)
    state_manager.results_redis_manager = results_redis_manager
    state_manager.state_redis_manager = state_redis_manager
    state_manager.postgres_manager = postgres_manager

    # Initialize Redis event listener
    redis_event_listener = get_redis_event_listener(state_manager)

    # Create test data
    workflow_state = {
        "pending_transitions": ["transition1", "transition2"],
        "completed_transitions": [],
        "waiting_transitions": [],
        "terminated": False,
        "paused": False,
    }

    transition_id = f"transition1-{uuid.uuid4()}"
    transition_result = {
        "status": "success",
        "result": {"data": "Test result data", "timestamp": time.time()},
    }

    # Configure Redis for keyspace notifications including expirations
    logger.info("Configuring Redis for keyspace notifications including expirations...")
    results_redis_manager.redis_client.config_set("notify-keyspace-events", "KEx")
    state_redis_manager.redis_client.config_set("notify-keyspace-events", "KEx")

    # Store data in Redis with short TTL for testing (5 seconds)
    logger.info("Storing test data in Redis with short TTL...")

    # Store workflow state
    workflow_state_key = f"workflow_state:{workflow_id}"
    state_redis_manager.set_value(workflow_state_key, json.dumps(workflow_state), ttl=5)
    logger.info(f"Stored workflow state with key: {workflow_state_key} (TTL: 5s)")

    # Store transition result
    transition_result_key = f"result:{transition_id}"
    results_redis_manager.set_value(
        transition_result_key, json.dumps(transition_result), ttl=5
    )
    logger.info(f"Stored transition result with key: {transition_result_key} (TTL: 5s)")

    # Store the data in memory for the state manager to use when archiving
    state_manager.transition_results[transition_id] = transition_result
    state_manager.pending_transitions = set(workflow_state["pending_transitions"])
    state_manager.completed_transitions = set(workflow_state["completed_transitions"])
    state_manager.waiting_transitions = set(workflow_state["waiting_transitions"])
    state_manager.terminated = workflow_state["terminated"]
    state_manager.workflow_paused = workflow_state["paused"]
    logger.info("Stored test data in memory for the state manager")

    # Verify TTL is set correctly
    result_ttl = results_redis_manager.redis_client.ttl(transition_result_key)
    state_ttl = state_redis_manager.redis_client.ttl(workflow_state_key)

    logger.info(f"Transition result TTL: {result_ttl} seconds")
    logger.info(f"Workflow state TTL: {state_ttl} seconds")

    if result_ttl <= 0 or state_ttl <= 0:
        logger.error("TTL not set correctly")
        return False

    # Wait for Redis keys to expire (6 seconds to be safe)
    logger.info("Waiting for Redis keys to expire (6 seconds)...")
    time.sleep(6)

    # Verify keys have expired
    result_exists = results_redis_manager.redis_client.exists(transition_result_key)
    state_exists = state_redis_manager.redis_client.exists(workflow_state_key)

    if result_exists or state_exists:
        logger.error("Redis keys did not expire as expected")
        return False
    else:
        logger.info("Redis keys have expired as expected")

    # Wait for the Redis event listener to process the expiration events
    logger.info("Waiting for Redis event listener to process expiration events...")
    time.sleep(10)  # Give the event listener more time to process the events

    # Verify data was archived to PostgreSQL
    logger.info("Checking if data was archived to PostgreSQL...")

    # Check for transition result in PostgreSQL
    pg_transition_result = postgres_manager.get_transition_result(
        correlation_id, transition_id
    )

    if pg_transition_result:
        logger.info("Transition result successfully archived to PostgreSQL:")
        logger.info(f"Result: {pg_transition_result}")
    else:
        logger.error("Failed to find transition result in PostgreSQL")
        return False

    # Check for workflow state in PostgreSQL
    pg_workflow_state = postgres_manager.get_workflow_state(correlation_id)

    if pg_workflow_state:
        logger.info("Workflow state successfully archived to PostgreSQL:")
        logger.info(f"State: {pg_workflow_state}")
    else:
        logger.error("Failed to find workflow state in PostgreSQL")
        return False

    logger.info("Test completed successfully!")
    return True


if __name__ == "__main__":
    logger.info("Starting Redis TTL test...")

    try:
        success = test_redis_ttl()
        if success:
            logger.info(
                "✅ Test passed: Redis TTL and PostgreSQL archiving works correctly"
            )
            sys.exit(0)
        else:
            logger.error(
                "❌ Test failed: Redis TTL and PostgreSQL archiving did not work as expected"
            )
            sys.exit(1)
    except Exception as e:
        logger.exception(f"❌ Test failed with exception: {e}")
        sys.exit(1)
