"""
Test script for directly testing PostgreSQL manager's ability to store and retrieve transition results.

Usage:
    poetry run python test_postgres_direct.py
"""

import json
import time
import uuid
import sys
import logging
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Set to DEBUG for more detailed logs
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger("PostgresDirectTest")


def test_postgres_direct():
    """
    Test the PostgreSQL manager's ability to store and retrieve transition results directly.
    
    This test:
    1. Creates a unique workflow ID
    2. Creates a test transition result
    3. Stores the transition result directly in PostgreSQL
    4. Retrieves the transition result from PostgreSQL
    5. Verifies the retrieved result matches the original
    """
    # Generate a unique workflow ID for this test
    workflow_id = f"test-workflow-{uuid.uuid4()}"
    correlation_id = workflow_id  # Using the same ID for simplicity
    
    logger.info(f"Starting test with workflow ID: {workflow_id}")
    
    # Initialize PostgreSQL manager
    postgres_manager = get_postgres_manager()
    
    # Create test data
    transition_id = f"transition1-{uuid.uuid4()}"
    transition_result = {
        "status": "success",
        "result": {
            "data": "Test result data",
            "timestamp": time.time()
        }
    }
    
    logger.info(f"Created test transition result: {transition_result}")
    
    # Store transition result directly in PostgreSQL
    logger.info("Storing transition result directly in PostgreSQL...")
    success = postgres_manager.store_transition_result(
        correlation_id, transition_id, transition_result
    )
    
    if success:
        logger.info(f"Successfully stored transition result for {transition_id}")
    else:
        logger.error(f"Failed to store transition result for {transition_id}")
        return False
    
    # Retrieve transition result from PostgreSQL
    logger.info("Retrieving transition result from PostgreSQL...")
    retrieved_result = postgres_manager.get_transition_result(
        correlation_id, transition_id
    )
    
    if retrieved_result:
        logger.info(f"Successfully retrieved transition result: {retrieved_result}")
        
        # Verify the retrieved result matches the original
        if retrieved_result == transition_result:
            logger.info("Retrieved result matches the original")
            return True
        else:
            logger.error("Retrieved result does not match the original")
            logger.error(f"Original: {transition_result}")
            logger.error(f"Retrieved: {retrieved_result}")
            return False
    else:
        logger.error(f"Failed to retrieve transition result for {transition_id}")
        return False


if __name__ == "__main__":
    logger.info("Starting PostgreSQL direct test...")
    
    try:
        success = test_postgres_direct()
        if success:
            logger.info("✅ Test passed: PostgreSQL direct test works correctly")
            sys.exit(0)
        else:
            logger.error("❌ Test failed: PostgreSQL direct test did not work as expected")
            sys.exit(1)
    except Exception as e:
        logger.exception(f"❌ Test failed with exception: {e}")
        sys.exit(1)
