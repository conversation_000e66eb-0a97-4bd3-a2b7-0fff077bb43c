"""
Test script for PostgreSQL retrieval functionality.
This script tests the ability to retrieve workflow state and transition results from PostgreSQL
when they are not found in Redis.

Usage:
    poetry run python test_postgres_retrieval.py
"""

import json
import time
import uuid
import sys
import logging
from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.core_.state_manager import WorkflowStateManager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("PostgresRetrievalTest")

def test_postgres_retrieval():
    """
    Test the PostgreSQL retrieval functionality.
    
    This test:
    1. Creates a unique workflow ID
    2. Stores data directly in PostgreSQL
    3. Attempts to retrieve the data using the WorkflowStateManager
    4. Verifies the data is correctly retrieved from PostgreSQL
    """
    # Generate a unique workflow ID for this test
    workflow_id = f"test-workflow-{uuid.uuid4()}"
    correlation_id = workflow_id  # Using the same ID for simplicity
    
    logger.info(f"Starting test with workflow ID: {workflow_id}")
    
    # Initialize Redis managers
    results_redis_manager = RedisManager(db_index=int(settings.redis_results_db_index))
    state_redis_manager = RedisManager(db_index=int(settings.redis_state_db_index))
    
    # Initialize PostgreSQL manager
    postgres_manager = get_postgres_manager()
    
    # Initialize workflow state manager
    state_manager = WorkflowStateManager(workflow_id=workflow_id)
    state_manager.results_redis_manager = results_redis_manager
    state_manager.state_redis_manager = state_redis_manager
    state_manager.postgres_manager = postgres_manager
    
    # Create test data
    workflow_state = {
        "pending_transitions": ["transition1", "transition2"],
        "completed_transitions": [],
        "waiting_transitions": [],
        "terminated": False,
        "paused": False
    }
    
    transition_id = f"transition1-{uuid.uuid4()}"
    transition_result = {
        "status": "success",
        "result": {
            "data": "Test result data",
            "timestamp": time.time()
        }
    }
    
    # Store data directly in PostgreSQL
    logger.info("Storing test data directly in PostgreSQL...")
    
    # Store workflow state
    postgres_manager.store_workflow_state(correlation_id, workflow_id, workflow_state)
    logger.info(f"Stored workflow state in PostgreSQL for workflow ID: {workflow_id}")
    
    # Store transition result
    postgres_manager.store_transition_result(correlation_id, transition_id, transition_result)
    logger.info(f"Stored transition result in PostgreSQL for transition ID: {transition_id}")
    
    # Verify data is in PostgreSQL
    pg_workflow_state = postgres_manager.get_workflow_state(correlation_id)
    pg_transition_result = postgres_manager.get_transition_result(correlation_id, transition_id)
    
    if pg_workflow_state and pg_transition_result:
        logger.info("Data successfully stored in PostgreSQL")
    else:
        logger.error("Failed to store data in PostgreSQL")
        return False
    
    # Now try to retrieve the data using the WorkflowStateManager
    logger.info("Attempting to retrieve data using WorkflowStateManager...")
    
    # Get transition result
    retrieved_result = state_manager.get_transition_result(transition_id)
    
    if retrieved_result:
        logger.info("Successfully retrieved transition result from PostgreSQL via WorkflowStateManager:")
        logger.info(json.dumps(retrieved_result, indent=2))
    else:
        logger.error("Failed to retrieve transition result from PostgreSQL via WorkflowStateManager")
        return False
    
    # Load workflow state
    state_loaded = state_manager.load_workflow_state()
    
    if state_loaded:
        logger.info("Successfully loaded workflow state from PostgreSQL via WorkflowStateManager")
        logger.info(f"Pending transitions: {state_manager.pending_transitions}")
        logger.info(f"Completed transitions: {state_manager.completed_transitions}")
        logger.info(f"Waiting transitions: {state_manager.waiting_transitions}")
        logger.info(f"Terminated: {state_manager.terminated}")
        logger.info(f"Paused: {state_manager.workflow_paused}")
    else:
        logger.error("Failed to load workflow state from PostgreSQL via WorkflowStateManager")
        return False
    
    logger.info("Test completed successfully!")
    return True

if __name__ == "__main__":
    logger.info("Starting PostgreSQL retrieval test...")
    
    try:
        success = test_postgres_retrieval()
        if success:
            logger.info("✅ Test passed: PostgreSQL retrieval works correctly")
            sys.exit(0)
        else:
            logger.error("❌ Test failed: PostgreSQL retrieval did not work as expected")
            sys.exit(1)
    except Exception as e:
        logger.exception(f"❌ Test failed with exception: {e}")
        sys.exit(1)
