#!/usr/bin/env python3
"""
Debug script to understand the transition-specific parameter application issue.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

try:
    from services.initialize_workflow import initialize_workflow_with_params

    print("✅ Successfully imported initialize_workflow_with_params")
except ImportError as e:
    print(f"❌ Failed to import: {e}")
    sys.exit(1)


def debug_transition_specific_issue():
    """
    Debug the transition-specific field application issue.
    """
    print("Debugging transition-specific field application...")

    # Let's test the exact same scenario as the existing test that should work
    workflow = {
        "transitions": [
            {
                "id": "transition-1",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [{"field_name": "topic", "field_value": None}]
                            }
                        }
                    ]
                },
            },
            {
                "id": "transition-2",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [{"field_name": "topic", "field_value": None}]
                            }
                        }
                    ]
                },
            },
        ]
    }

    params = {
        "payload": {
            "user_dependent_fields": ["topic"],
            "user_payload_template": {
                "topic": {
                    "value": "test topic for transition 2",
                    "transition_id": "transition-2",
                }
            },
        }
    }

    print("Original workflow:")
    print(
        f"Transition 1 topic: {workflow['transitions'][0]['node_info']['tools_to_use'][0]['tool_params']['items'][0]['field_value']}"
    )
    print(
        f"Transition 2 topic: {workflow['transitions'][1]['node_info']['tools_to_use'][0]['tool_params']['items'][0]['field_value']}"
    )

    print("\nPayload:")
    print(f"user_dependent_fields: {params['payload']['user_dependent_fields']}")
    print(f"user_payload_template: {params['payload']['user_payload_template']}")

    result = initialize_workflow_with_params(workflow, params)

    print("\nAfter initialization:")
    transition1_topic = result["transitions"][0]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]
    transition2_topic = result["transitions"][1]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]

    print(f"Transition 1 topic value: {transition1_topic}")
    print(f"Transition 2 topic value: {transition2_topic}")

    # Check if the issue is fixed
    if transition1_topic is None and transition2_topic == "test topic for transition 2":
        print(
            "✅ FIXED: Transition-specific values are correctly applied only to the intended transition!"
        )
        return True
    else:
        print(
            "❌ ISSUE STILL EXISTS: Transition-specific values are being applied to wrong transitions!"
        )
        print(f"Expected transition 1: None, got: {transition1_topic}")
        print(
            f"Expected transition 2: 'test topic for transition 2', got: {transition2_topic}"
        )
        return False


if __name__ == "__main__":
    debug_transition_specific_issue()
