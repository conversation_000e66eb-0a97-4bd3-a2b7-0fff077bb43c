"""
Comprehensive test cases for universal data propagation system.

This test suite validates the handle-based data propagation system works
correctly with any node type and workflow structure.
"""

import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

# Import the classes we're testing
from app.core_.workflow_utils import WorkflowUtils
from app.core_.transition_handler import TransitionHandler
from app.core_.state_manager import WorkflowStateManager


class TestUniversalDataPropagation:
    """Test suite for universal data propagation system."""

    @pytest.fixture
    def sample_workflow_data(self):
        """Load the sample workflow for testing."""
        workflow_path = (
            Path(__file__).parent.parent.parent.parent
            / "ruh.ai"
            / "workflow-service"
            / "testing"
            / "sample_workflow.json"
        )
        with open(workflow_path, "r") as f:
            return json.load(f)

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="test-workflow")

    @pytest.fixture
    def mock_state_manager(self):
        """Create mock state manager."""
        state_manager = Mock(spec=WorkflowStateManager)
        state_manager.get_transition_result = Mock()
        return state_manager

    @pytest.fixture
    def sample_handle_mappings(self):
        """Sample handle mappings for testing."""
        return {
            "transition-source": [
                {
                    "source_transition_id": "transition-source",
                    "source_handle_id": "interview_agenda",
                    "target_handle_id": "agenda",
                    "edge_id": "edge-1",
                },
                {
                    "source_transition_id": "transition-source",
                    "source_handle_id": "resume_details",
                    "target_handle_id": "resume_details",
                    "edge_id": "edge-2",
                },
            ]
        }

    @pytest.fixture
    def sample_results(self):
        """Sample transition results for testing."""
        return {
            "transition-source": {
                "interview_agenda": "Technical interview agenda with coding questions",
                "resume_details": "Senior Software Engineer with 5 years experience",
                "jd_details": "Full-stack developer position",
            }
        }

    def test_extract_handle_mappings(self, workflow_utils):
        """Test extraction of handle mappings from input data configs."""
        input_data_configs = [
            {
                "from_transition_id": "transition-1",
                "handle_mappings": [
                    {
                        "source_handle_id": "output1",
                        "target_handle_id": "input1",
                        "edge_id": "edge-1",
                    }
                ],
            },
            {
                "from_transition_id": "transition-2",
                "handle_mappings": [
                    {
                        "source_handle_id": "output2",
                        "target_handle_id": "input2",
                        "edge_id": "edge-2",
                    }
                ],
            },
        ]

        result = workflow_utils._extract_handle_mappings(input_data_configs)

        assert len(result) == 2
        assert "transition-1" in result
        assert "transition-2" in result
        assert len(result["transition-1"]) == 1
        assert result["transition-1"][0]["source_handle_id"] == "output1"

    def test_resolve_handle_data_success(
        self, workflow_utils, sample_handle_mappings, sample_results
    ):
        """Test successful handle data resolution."""
        current_tool_params = {"static_param": "static_value"}

        result = workflow_utils._resolve_handle_data(
            current_tool_params, sample_results, sample_handle_mappings
        )

        assert "agenda" in result
        assert "resume_details" in result
        assert "static_param" in result
        assert result["agenda"] == "Technical interview agenda with coding questions"
        assert (
            result["resume_details"]
            == "Senior Software Engineer with 5 years experience"
        )
        assert result["static_param"] == "static_value"

    def test_extract_data_by_handle_direct_mapping(self, workflow_utils):
        """Test direct handle mapping extraction."""
        source_results = {
            "interview_agenda": "Test agenda",
            "other_field": "Other value",
        }

        result = workflow_utils._extract_data_by_handle(
            source_results, "interview_agenda", "test-transition"
        )

        assert result == "Test agenda"

    def test_extract_data_by_handle_nested_result(self, workflow_utils):
        """Test extraction from nested result structure."""
        source_results = {
            "result": {
                "interview_agenda": "Nested agenda",
                "other_data": "Other nested value",
            }
        }

        result = workflow_utils._extract_data_by_handle(
            source_results, "interview_agenda", "test-transition"
        )

        assert result == "Nested agenda"

    def test_extract_data_by_handle_fallback_patterns(self, workflow_utils):
        """Test fallback pattern extraction."""
        source_results = {"output_data": {"target_field": "Fallback value"}}

        result = workflow_utils._extract_data_by_handle(
            source_results, "target_field", "test-transition"
        )

        assert result == "Fallback value"

    def test_extract_by_path_simple(self, workflow_utils):
        """Test simple path extraction."""
        data = {"field1": "value1", "field2": "value2"}

        result = workflow_utils._extract_by_path(data, "field1")
        assert result == "value1"

    def test_extract_by_path_nested(self, workflow_utils):
        """Test nested path extraction."""
        data = {"level1": {"level2": {"target": "nested_value"}}}

        result = workflow_utils._extract_by_path(data, "level1.level2.target")
        assert result == "nested_value"

    def test_extract_by_path_missing(self, workflow_utils):
        """Test path extraction with missing path."""
        data = {"field1": "value1"}

        result = workflow_utils._extract_by_path(data, "missing.path")
        assert result is None

    def test_convert_params_to_dict_from_list(self, workflow_utils):
        """Test parameter conversion from list format."""
        params = [
            {"field_name": "param1", "field_value": "value1"},
            {"field_name": "param2", "field_value": "value2"},
        ]

        result = workflow_utils._convert_params_to_dict(params)

        assert result == {"param1": "value1", "param2": "value2"}

    def test_convert_params_to_dict_from_dict(self, workflow_utils):
        """Test parameter conversion from dict format."""
        params = {"param1": "value1", "param2": "value2"}

        result = workflow_utils._convert_params_to_dict(params)

        assert result == {"param1": "value1", "param2": "value2"}

    def test_convert_params_to_dict_invalid(self, workflow_utils):
        """Test parameter conversion with invalid input."""
        params = "invalid_input"

        result = workflow_utils._convert_params_to_dict(params)

        assert result == {}

    @pytest.mark.asyncio
    async def test_format_tool_parameters_with_handle_mappings(
        self, workflow_utils, mock_state_manager
    ):
        """Test tool parameter formatting with handle mappings."""
        # Setup mock state manager
        mock_state_manager.get_transition_result.return_value = {
            "output_field": "test_value"
        }
        workflow_utils.state_manager = mock_state_manager

        input_data_configs = [
            {
                "from_transition_id": "source-transition",
                "handle_mappings": [
                    {
                        "source_handle_id": "output_field",
                        "target_handle_id": "input_field",
                        "edge_id": "test-edge",
                    }
                ],
            }
        ]

        current_tool_params = {"static_param": "static_value"}

        result = await workflow_utils._format_tool_parameters(
            {}, input_data_configs, "test-transition", current_tool_params
        )

        assert "input_field" in result
        assert "static_param" in result
        assert result["input_field"] == "test_value"
        assert result["static_param"] == "static_value"

    def test_validate_handle_mapping_compatibility_success(self, workflow_utils):
        """Test successful handle mapping validation."""
        handle_mappings = {
            "source-transition": [
                {
                    "source_handle_id": "available_field",
                    "target_handle_id": "target_field",
                }
            ]
        }

        all_previous_results = {"source-transition": {"available_field": "test_value"}}

        result = workflow_utils.validate_handle_mapping_compatibility(
            handle_mappings, all_previous_results
        )

        assert result["overall_compatibility"] == "fully_compatible"
        assert result["compatible_mappings"] == 1
        assert result["incompatible_mappings"] == 0

    def test_validate_handle_mapping_compatibility_missing_source(self, workflow_utils):
        """Test handle mapping validation with missing source."""
        handle_mappings = {
            "missing-transition": [
                {"source_handle_id": "field", "target_handle_id": "target_field"}
            ]
        }

        all_previous_results = {}

        result = workflow_utils.validate_handle_mapping_compatibility(
            handle_mappings, all_previous_results
        )

        assert result["overall_compatibility"] == "incompatible"
        assert result["missing_sources"] == 1

    def test_create_universal_parameter_mapping_success(self, workflow_utils):
        """Test universal parameter mapping creation."""
        handle_mappings = {
            "source-transition": [
                {
                    "source_handle_id": "output1",
                    "target_handle_id": "input1",
                    "edge_id": "edge-1",
                }
            ]
        }

        all_previous_results = {"source-transition": {"output1": "mapped_value"}}

        result = workflow_utils.create_universal_parameter_mapping(
            handle_mappings, all_previous_results
        )

        assert "resolved_parameters" in result
        assert "mapping_metadata" in result
        assert result["resolved_parameters"]["input1"] == "mapped_value"
        assert result["mapping_metadata"]["successful_mappings"] == 1
        assert result["mapping_metadata"]["failed_mappings"] == 0

    def test_create_universal_parameter_mapping_failure(self, workflow_utils):
        """Test universal parameter mapping with failures."""
        handle_mappings = {
            "source-transition": [
                {
                    "source_handle_id": "missing_field",
                    "target_handle_id": "input1",
                    "edge_id": "edge-1",
                }
            ]
        }

        all_previous_results = {"source-transition": {"other_field": "other_value"}}

        result = workflow_utils.create_universal_parameter_mapping(
            handle_mappings, all_previous_results
        )

        assert result["mapping_metadata"]["successful_mappings"] == 0
        assert result["mapping_metadata"]["failed_mappings"] == 1
        assert "input1" not in result["resolved_parameters"]


class TestDynamicResultStructureHandling:
    """Test suite for dynamic result structure handling."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="test-workflow")

    def test_analyze_dict_structure_flat(self, workflow_utils):
        """Test analysis of flat dictionary structure."""
        result_data = {"field1": "value1", "field2": 42, "field3": True}

        analysis = workflow_utils.create_dynamic_result_structure_analyzer(
            result_data, "test_node"
        )

        assert analysis["structure_pattern"] == "dictionary"
        assert analysis["nesting_depth"] == 0
        assert len(analysis["available_paths"]) == 3
        assert "field1" in analysis["available_paths"]
        assert len(analysis["handle_candidates"]) == 3

    def test_analyze_dict_structure_nested(self, workflow_utils):
        """Test analysis of nested dictionary structure."""
        result_data = {
            "result": {
                "output_data": {"target_field": "nested_value"},
                "metadata": {"timestamp": "2024-01-01"},
            },
            "status": "success",
        }

        analysis = workflow_utils.create_dynamic_result_structure_analyzer(
            result_data, "test_node"
        )

        assert analysis["structure_pattern"] == "dictionary"
        assert analysis["nesting_depth"] >= 2
        assert "result.output_data.target_field" in analysis["available_paths"]
        assert "status" in analysis["handle_candidates"]

    def test_analyze_array_structure(self, workflow_utils):
        """Test analysis of array structure."""
        result_data = [{"item": "value1"}, {"item": "value2"}]

        analysis = workflow_utils.create_dynamic_result_structure_analyzer(
            result_data, "test_node"
        )

        assert analysis["structure_pattern"] == "array"
        assert "[0].item" in analysis["available_paths"]

    def test_analyze_string_structure(self, workflow_utils):
        """Test analysis of string structure."""
        result_data = "Simple string result"

        analysis = workflow_utils.create_dynamic_result_structure_analyzer(
            result_data, "test_node"
        )

        assert analysis["structure_pattern"] == "string"
        assert "<direct_string>" in analysis["available_paths"]

    def test_analyze_null_structure(self, workflow_utils):
        """Test analysis of null result."""
        result_data = None

        analysis = workflow_utils.create_dynamic_result_structure_analyzer(
            result_data, "test_node"
        )

        assert analysis["structure_pattern"] == "null_result"
        assert "Result is null - check node execution" in analysis["recommendations"]

    def test_generate_structure_recommendations_common_patterns(self, workflow_utils):
        """Test recommendation generation for common patterns."""
        result_data = {
            "result": "main_output",
            "data": "secondary_output",
            "metadata": {"info": "extra"},
        }

        analysis = workflow_utils.create_dynamic_result_structure_analyzer(
            result_data, "test_node"
        )

        recommendations = analysis["recommendations"]
        assert any("'result' field found" in rec for rec in recommendations)
        assert any("'data' field found" in rec for rec in recommendations)


class TestHandleValidationSystem:
    """Test suite for comprehensive handle validation system."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="test-workflow")

    @pytest.fixture
    def sample_workflow_data(self):
        """Sample workflow data for validation testing."""
        return {
            "nodes": [
                {
                    "id": "node-1",
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "inputs": [
                                {
                                    "name": "input1",
                                    "data_type": "string",
                                    "required": True,
                                }
                            ],
                            "outputs": [{"name": "output1", "data_type": "string"}],
                        },
                    },
                },
                {
                    "id": "node-2",
                    "data": {
                        "type": "component",
                        "definition": {
                            "inputs": [
                                {
                                    "name": "input2",
                                    "data_type": "string",
                                    "required": True,
                                }
                            ],
                            "outputs": [{"name": "output2", "data_type": "string"}],
                        },
                    },
                },
            ]
        }

    @pytest.fixture
    def sample_transitions_data(self):
        """Sample transitions data for validation testing."""
        return [
            {
                "id": "transition-1",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "output1",
                                    "target_handle_id": "input2",
                                    "edge_id": "edge-1",
                                }
                            ]
                        }
                    ]
                },
            }
        ]

    def test_validate_node_handles_valid(self, workflow_utils, sample_workflow_data):
        """Test validation of valid node handles."""
        node = sample_workflow_data["nodes"][0]

        result = workflow_utils._validate_node_handles(node)

        assert result["status"] == "valid"
        assert len(result["handle_info"]["input_handles"]) == 1
        assert len(result["handle_info"]["output_handles"]) == 1
        assert result["handle_info"]["total_handles"] == 2

    def test_validate_node_handles_no_handles(self, workflow_utils):
        """Test validation of node with no handles."""
        node = {
            "id": "empty-node",
            "data": {"type": "unknown", "definition": {"inputs": [], "outputs": []}},
        }

        result = workflow_utils._validate_node_handles(node)

        assert result["status"] == "warning"
        assert "no defined input or output handles" in result["issues"][0]

    def test_validate_node_handles_duplicates(self, workflow_utils):
        """Test validation of node with duplicate handle names."""
        node = {
            "id": "duplicate-node",
            "data": {
                "type": "test",
                "definition": {
                    "inputs": [{"name": "duplicate", "data_type": "string"}],
                    "outputs": [{"name": "duplicate", "data_type": "string"}],
                },
            },
        }

        result = workflow_utils._validate_node_handles(node)

        assert result["status"] == "invalid"
        assert "Duplicate handle names" in result["issues"][0]

    def test_validate_handle_connection_valid(
        self, workflow_utils, sample_workflow_data
    ):
        """Test validation of valid handle connection."""
        mapping = {
            "source_handle_id": "output1",
            "target_handle_id": "input2",
            "edge_id": "edge-1",
        }

        result = workflow_utils._validate_handle_connection(
            mapping, sample_workflow_data
        )

        assert result["status"] == "valid"
        assert result["source_handle_id"] == "output1"
        assert result["target_handle_id"] == "input2"

    def test_validate_handle_connection_missing_handles(
        self, workflow_utils, sample_workflow_data
    ):
        """Test validation of connection with missing handles."""
        mapping = {
            "source_handle_id": "",
            "target_handle_id": "input2",
            "edge_id": "edge-1",
        }

        result = workflow_utils._validate_handle_connection(
            mapping, sample_workflow_data
        )

        assert result["status"] == "invalid"
        assert "Missing source or target handle ID" in result["issues"]

    def test_validate_handle_connection_self_connection(
        self, workflow_utils, sample_workflow_data
    ):
        """Test validation of self-connection."""
        mapping = {
            "source_handle_id": "same_handle",
            "target_handle_id": "same_handle",
            "edge_id": "edge-1",
        }

        result = workflow_utils._validate_handle_connection(
            mapping, sample_workflow_data
        )

        assert result["status"] == "warning"
        assert "Handle connects to itself" in result["issues"]

    def test_detect_handle_conflicts_multiple_sources(self, workflow_utils):
        """Test detection of multiple sources to same target."""
        transitions_data = [
            {
                "id": "transition-1",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "source1",
                                    "target_handle_id": "target",
                                    "edge_id": "edge-1",
                                },
                                {
                                    "source_handle_id": "source2",
                                    "target_handle_id": "target",
                                    "edge_id": "edge-2",
                                },
                            ]
                        }
                    ]
                },
            }
        ]

        conflicts = workflow_utils._detect_handle_conflicts({}, transitions_data)

        assert len(conflicts) == 1
        assert conflicts[0]["type"] == "multiple_sources"
        assert conflicts[0]["target_handle"] == "target"
        assert len(conflicts[0]["sources"]) == 2

    def test_create_comprehensive_handle_validator(
        self, workflow_utils, sample_workflow_data, sample_transitions_data
    ):
        """Test comprehensive handle validation."""
        result = workflow_utils.create_comprehensive_handle_validator(
            sample_workflow_data, sample_transitions_data
        )

        assert "workflow_validation" in result
        assert "node_validation" in result
        assert "connection_validation" in result
        assert "handle_conflicts" in result
        assert "recommendations" in result

        # Check that nodes were validated
        assert len(result["node_validation"]) == 2

        # Check workflow validation summary
        workflow_validation = result["workflow_validation"]
        assert "overall_status" in workflow_validation
        assert "total_handle_connections" in workflow_validation


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
