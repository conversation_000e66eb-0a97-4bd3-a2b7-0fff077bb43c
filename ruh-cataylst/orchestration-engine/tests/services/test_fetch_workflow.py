import pytest
from unittest.mock import Mock, patch
import requests
import json
from app.services.fetch_workflow import fetch_workflow_orchestration


class TestFetchWorkflow:
    """
    Test suite for fetch_workflow module.
    Tests API interactions, response handling, and error scenarios for workflow fetching.
    """

    @pytest.fixture
    def mock_settings(self):
        """
        Provides mock settings for API configuration.
        """
        with patch('app.services.fetch_workflow.settings') as mock_settings:
            mock_settings.api_gateway_url = "http://test-api.com"
            mock_settings.orchestration_server_auth_key = Mock(
                get_secret_value=lambda: "test-auth-key"
            )
            yield mock_settings

    @pytest.fixture
    def mock_successful_response(self):
        """
        Provides a mock successful API response.
        """
        mock_response = Mock(spec=requests.Response)
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Workflow string retrieved successfully",
            "workflow": {
                "workflow_url": "http://test-workflow-url.com",
                "other_data": "test"
            }
        }
        return mock_response

    @pytest.fixture
    def mock_workflow_response(self):
        """
        Provides a mock workflow data response.
        """
        mock_response = Mock(spec=requests.Response)
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "nodes": [],
            "transitions": [],
            "metadata": {"version": "1.0"}
        }
        return mock_response

    def test_successful_workflow_fetch(self, mock_settings, mock_successful_response, mock_workflow_response):
        """
        Test successful workflow fetching with valid responses.
        """
        with patch('requests.get') as mock_get:
            mock_get.side_effect = [mock_successful_response, mock_workflow_response]
            
            result = fetch_workflow_orchestration(16)
            
            # Verify API calls
            assert mock_get.call_count == 2
            mock_get.assert_any_call(
                "http://test-api.com/api/v1/workflows/orchestration/16",
                headers={"X-Server-Auth-Key": "test-auth-key"}
            )
            mock_get.assert_any_call("http://test-workflow-url.com")
            
            # Verify result
            assert isinstance(result, dict)
            assert "nodes" in result
            assert "transitions" in result
            assert "metadata" in result

    def test_api_error_response(self, mock_settings):
        """
        Test handling of API error response.
        """
        with patch('requests.get') as mock_get:
            mock_response = Mock(spec=requests.Response)
            mock_response.status_code = 404
            mock_response.text = "Workflow not found"
            mock_get.return_value = mock_response

            with pytest.raises(requests.HTTPError) as exc_info:
                fetch_workflow_orchestration(16)

            assert "Error 404: Workflow not found" in str(exc_info.value)

    def test_invalid_json_response(self, mock_settings, mock_successful_response):
        """
        Test handling of invalid JSON in API response.
        """
        with patch('requests.get') as mock_get:
            mock_response = Mock(spec=requests.Response)
            mock_response.status_code = 200
            mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
            mock_get.return_value = mock_response

            with pytest.raises(ValueError) as exc_info:
                fetch_workflow_orchestration(16)

            assert "Invalid JSON response received" in str(exc_info.value)

    def test_missing_workflow_url(self, mock_settings):
        """
        Test handling of missing workflow_url in response.
        """
        with patch('requests.get') as mock_get:
            mock_response = Mock(spec=requests.Response)
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "success": True,
                "workflow": {"other_data": "test"}
            }
            mock_get.return_value = mock_response

            with pytest.raises(KeyError) as exc_info:
                fetch_workflow_orchestration(16)

            assert "'workflow_url'is missing in API response" in str(exc_info.value)

    def test_workflow_url_request_failure(self, mock_settings, mock_successful_response):
        """
        Test handling of workflow URL request failure.
        """
        with patch('requests.get') as mock_get:
            mock_get.side_effect = [
                mock_successful_response,
                requests.RequestException("Failed to fetch workflow")
            ]

            with pytest.raises(ValueError) as exc_info:
                fetch_workflow_orchestration(16)

            assert "Unable to retrieve the workflow from URL" in str(exc_info.value)

    def test_invalid_workflow_json(self, mock_settings, mock_successful_response):
        """
        Test handling of invalid JSON in workflow response.
        """
        with patch('requests.get') as mock_get:
            workflow_response = Mock(spec=requests.Response)
            workflow_response.status_code = 200
            workflow_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
            
            mock_get.side_effect = [mock_successful_response, workflow_response]

            with pytest.raises(ValueError) as exc_info:
                fetch_workflow_orchestration(16)

            assert "Unable to retrieve the workflow from URL" in str(exc_info.value)

    def test_network_error_handling(self, mock_settings):
        """
        Test handling of network connection errors.
        """
        with patch('requests.get') as mock_get:
            mock_get.side_effect = requests.ConnectionError("Network error")

            with pytest.raises(requests.HTTPError) as exc_info:
                fetch_workflow_orchestration(16)

            assert "Network error" in str(exc_info.value)

    def test_timeout_handling(self, mock_settings):
        """
        Test handling of request timeouts.
        """
        with patch('requests.get') as mock_get:
            mock_get.side_effect = requests.Timeout("Request timed out")

            with pytest.raises(requests.HTTPError) as exc_info:
                fetch_workflow_orchestration(16)

            assert "Request timed out" in str(exc_info.value)

    def test_server_error_handling(self, mock_settings):
        """
        Test handling of server errors (5xx responses).
        """
        with patch('requests.get') as mock_get:
            mock_response = Mock(spec=requests.Response)
            mock_response.status_code = 500
            mock_response.text = "Internal Server Error"
            mock_get.return_value = mock_response

            with pytest.raises(requests.HTTPError) as exc_info:
                fetch_workflow_orchestration(16)

            assert "Error 500: Internal Server Error" in str(exc_info.value)