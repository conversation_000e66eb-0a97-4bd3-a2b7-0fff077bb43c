import pytest
from unittest.mock import Mock, patch
import redis
from app.services.db_connections.redis_connections import RedisManager


class TestRedisManager:
    """
    Test suite for RedisManager class.
    Tests Redis connection management, CRUD operations, and error handling.
    """

    @pytest.fixture
    def mock_settings(self):
        """
        Provides mock settings for Redis configuration.
        """
        with patch('app.services.db_connections.redis_connections.settings') as mock_settings:
            mock_settings.redis_host = "localhost"
            mock_settings.redis_port = "6379"
            mock_settings.redis_password = Mock(get_secret_value=lambda: "test_password")
            yield mock_settings

    @pytest.fixture
    def redis_manager(self, mock_settings):
        """
        Provides a RedisManager instance with mocked settings.
        """
        with patch('redis.Redis') as mock_redis:
            mock_redis.return_value.ping.return_value = True
            manager = RedisManager(db_index=0)
            yield manager

    def test_initialization_success(self, mock_settings):
        """
        Test successful initialization of RedisManager.
        """
        with patch('redis.Redis') as mock_redis:
            mock_redis.return_value.ping.return_value = True
            
            manager = RedisManager(db_index=0)
            
            assert manager.redis_host == "localhost"
            assert manager.redis_port == "6379"
            assert manager.redis_password == "test_password"
            assert manager.db_index == 0
            assert manager.redis_client is not None

    def test_initialization_missing_config(self):
        """
        Test initialization with missing configuration values.
        """
        with patch('app.services.db_connections.redis_connections.settings') as mock_settings:
            mock_settings.redis_host = None
            mock_settings.redis_port = None
            mock_settings.redis_password = None

            with pytest.raises(ValueError) as exc_info:
                RedisManager(db_index=0)

            assert "Redis connection details are missing" in str(exc_info.value)

    def test_connection_error(self, mock_settings):
        """
        Test handling of connection errors during initialization.
        """
        with patch('redis.Redis') as mock_redis:
            mock_redis.return_value.ping.side_effect = redis.exceptions.ConnectionError(
                "Connection refused"
            )

            with pytest.raises(redis.exceptions.ConnectionError) as exc_info:
                RedisManager(db_index=0)

            assert "Connection refused" in str(exc_info.value)

    def test_is_connected_true(self, redis_manager):
        """
        Test is_connected method when connection is successful.
        """
        assert redis_manager.is_connected() is True

    def test_is_connected_false(self, redis_manager):
        """
        Test is_connected method when connection fails.
        """
        redis_manager.redis_client.ping.side_effect = redis.exceptions.ConnectionError()
        assert redis_manager.is_connected() is False

    def test_set_value_success(self, redis_manager):
        """
        Test successful set_value operation.
        """
        key = "test_key"
        value = "test_value"
        
        result = redis_manager.set_value(key, value)
        
        redis_manager.redis_client.set.assert_called_once_with(key, value)
        assert result is True

    def test_set_value_failure(self, redis_manager):
        """
        Test set_value operation failure.
        """
        redis_manager.redis_client.set.side_effect = redis.exceptions.RedisError()
        
        result = redis_manager.set_value("test_key", "test_value")
        
        assert result is False

    def test_get_value_success(self, redis_manager):
        """
        Test successful get_value operation.
        """
        key = "test_key"
        expected_value = "test_value"
        redis_manager.redis_client.get.return_value = expected_value
        
        result = redis_manager.get_value(key)
        
        redis_manager.redis_client.get.assert_called_once_with(key)
        assert result == expected_value

    def test_get_value_failure(self, redis_manager):
        """
        Test get_value operation failure.
        """
        redis_manager.redis_client.get.side_effect = redis.exceptions.RedisError()
        
        result = redis_manager.get_value("test_key")
        
        assert result is None

    def test_get_value_not_connected(self, redis_manager):
        """
        Test get_value when Redis is not connected.
        """
        redis_manager.redis_client.ping.side_effect = redis.exceptions.ConnectionError()
        
        result = redis_manager.get_value("test_key")
        
        assert result is None

    def test_update_value_success(self, redis_manager):
        """
        Test successful update_value operation.
        """
        key = "test_key"
        value = "updated_value"
        
        result = redis_manager.update_value(key, value)
        
        redis_manager.redis_client.set.assert_called_once_with(key, value)
        assert result is True

    def test_delete_value_success(self, redis_manager):
        """
        Test successful delete_value operation.
        """
        key = "test_key"
        
        result = redis_manager.delete_value(key)
        
        redis_manager.redis_client.delete.assert_called_once_with(key)
        assert result is True

    def test_delete_value_failure(self, redis_manager):
        """
        Test delete_value operation failure.
        """
        redis_manager.redis_client.delete.side_effect = redis.exceptions.RedisError()
        
        result = redis_manager.delete_value("test_key")
        
        assert result is False

    def test_close_connection(self, redis_manager):
        """
        Test successful connection closure.
        """
        redis_manager.close_connection()
        
        redis_manager.redis_client.close.assert_called_once()
        assert redis_manager.redis_client is None

    def test_multiple_db_indices(self, mock_settings):
        """
        Test RedisManager with different database indices.
        """
        with patch('redis.Redis') as mock_redis:
            mock_redis.return_value.ping.return_value = True
            
            # Test with different db indices
            db_indices = [0, 1, 2]
            for index in db_indices:
                manager = RedisManager(db_index=index)
                assert manager.db_index == index
                mock_redis.assert_called_with(
                    host="localhost",
                    port=int("6379"),
                    password="test_password",
                    db=index,
                    decode_responses=True
                )

    def test_connection_params(self, mock_settings):
        """
        Test connection parameters are correctly passed to Redis client.
        """
        with patch('redis.Redis') as mock_redis:
            mock_redis.return_value.ping.return_value = True
            
            RedisManager(db_index=0)
            
            mock_redis.assert_called_once_with(
                host="localhost",
                port=int("6379"),
                password="test_password",
                db=0,
                decode_responses=True
            )