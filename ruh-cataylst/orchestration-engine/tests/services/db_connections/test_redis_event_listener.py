import pytest
from unittest.mock import Mock, patch, MagicMock
import threading
import time
from app.services.db_connections.redis_event_listener import RedisEventListener


class TestRedisEventListener:
    """
    Test suite for RedisEventListener class.
    Tests Redis event subscription, event handling, and archiving functionality.
    """

    @pytest.fixture
    def mock_redis_manager(self):
        """
        Provides a mock RedisManager.
        """
        mock = MagicMock()
        mock.is_connected.return_value = True
        mock.db_index = 0
        return mock

    @pytest.fixture
    def mock_postgres_manager(self):
        """
        Provides a mock PostgresManager.
        """
        mock = MagicMock()
        mock.is_connected.return_value = True
        return mock

    @pytest.fixture
    def mock_workflow_state_manager(self):
        """
        Provides a mock WorkflowStateManager.
        """
        mock = MagicMock()
        mock.archive_transition_result.return_value = True
        mock.archive_workflow_state.return_value = True
        return mock

    @pytest.fixture
    def redis_event_listener(self, mock_redis_manager, mock_postgres_manager, mock_workflow_state_manager):
        """
        Provides a RedisEventListener instance with mocked dependencies.
        """
        with patch('app.services.db_connections.redis_event_listener.RedisManager', return_value=mock_redis_manager), \
             patch('app.services.db_connections.redis_event_listener.PostgresManager', return_value=mock_postgres_manager):
            listener = RedisEventListener(mock_workflow_state_manager)
            listener.results_redis_manager = mock_redis_manager
            listener.state_redis_manager = mock_redis_manager
            listener.postgres_manager = mock_postgres_manager
            yield listener

    def test_init(self, mock_workflow_state_manager):
        """
        Test initialization with a workflow state manager.
        """
        with patch('app.services.db_connections.redis_event_listener.RedisManager') as mock_redis_manager_class, \
             patch('app.services.db_connections.redis_event_listener.PostgresManager') as mock_postgres_manager_class:
            
            mock_redis_manager = MagicMock()
            mock_postgres_manager = MagicMock()
            mock_redis_manager_class.return_value = mock_redis_manager
            mock_postgres_manager_class.return_value = mock_postgres_manager
            
            listener = RedisEventListener(mock_workflow_state_manager)
            
            assert listener.workflow_state_manager == mock_workflow_state_manager
            assert listener.running is False
            assert listener.thread is None
            assert listener.results_redis_manager == mock_redis_manager
            assert listener.state_redis_manager == mock_redis_manager
            assert listener.postgres_manager == mock_postgres_manager

    def test_configure_redis_notifications(self, redis_event_listener):
        """
        Test _configure_redis_notifications method.
        """
        result = redis_event_listener._configure_redis_notifications()
        
        assert result is True
        redis_event_listener.results_redis_manager.redis_client.config_set.assert_called_with('notify-keyspace-events', 'KEA')
        redis_event_listener.state_redis_manager.redis_client.config_set.assert_called_with('notify-keyspace-events', 'KEA')

    def test_configure_redis_notifications_error(self, redis_event_listener):
        """
        Test _configure_redis_notifications method when an error occurs.
        """
        redis_event_listener.results_redis_manager.redis_client.config_set.side_effect = Exception("Redis error")
        
        result = redis_event_listener._configure_redis_notifications()
        
        assert result is False

    def test_handle_results_db_event_del_result(self, redis_event_listener):
        """
        Test _handle_results_db_event method for a 'del' event on a result key.
        """
        # Mock message for a 'del' event on a result key
        message = {
            'channel': b'__keyspace@0__:result:test-transition-id',
            'data': b'del',
            'type': 'pmessage'
        }
        
        redis_event_listener._handle_results_db_event(message)
        
        # Verify that archive_transition_result was called with the correct transition ID
        redis_event_listener.workflow_state_manager.archive_transition_result.assert_called_with('test-transition-id')

    def test_handle_results_db_event_not_del(self, redis_event_listener):
        """
        Test _handle_results_db_event method for a non-'del' event.
        """
        # Mock message for a 'set' event
        message = {
            'channel': b'__keyspace@0__:result:test-transition-id',
            'data': b'set',
            'type': 'pmessage'
        }
        
        redis_event_listener._handle_results_db_event(message)
        
        # Verify that archive_transition_result was not called
        redis_event_listener.workflow_state_manager.archive_transition_result.assert_not_called()

    def test_handle_results_db_event_not_result_key(self, redis_event_listener):
        """
        Test _handle_results_db_event method for a key that is not a result key.
        """
        # Mock message for a 'del' event on a non-result key
        message = {
            'channel': b'__keyspace@0__:other:test-id',
            'data': b'del',
            'type': 'pmessage'
        }
        
        redis_event_listener._handle_results_db_event(message)
        
        # Verify that archive_transition_result was not called
        redis_event_listener.workflow_state_manager.archive_transition_result.assert_not_called()

    def test_handle_state_db_event_del_workflow_state(self, redis_event_listener):
        """
        Test _handle_state_db_event method for a 'del' event on a workflow state key.
        """
        # Mock message for a 'del' event on a workflow state key
        message = {
            'channel': b'__keyspace@1__:workflow_state:test-workflow-id',
            'data': b'del',
            'type': 'pmessage'
        }
        
        redis_event_listener._handle_state_db_event(message)
        
        # Verify that archive_workflow_state was called
        redis_event_listener.workflow_state_manager.archive_workflow_state.assert_called_once()

    def test_handle_state_db_event_not_del(self, redis_event_listener):
        """
        Test _handle_state_db_event method for a non-'del' event.
        """
        # Mock message for a 'set' event
        message = {
            'channel': b'__keyspace@1__:workflow_state:test-workflow-id',
            'data': b'set',
            'type': 'pmessage'
        }
        
        redis_event_listener._handle_state_db_event(message)
        
        # Verify that archive_workflow_state was not called
        redis_event_listener.workflow_state_manager.archive_workflow_state.assert_not_called()

    def test_handle_state_db_event_not_workflow_state_key(self, redis_event_listener):
        """
        Test _handle_state_db_event method for a key that is not a workflow state key.
        """
        # Mock message for a 'del' event on a non-workflow state key
        message = {
            'channel': b'__keyspace@1__:other:test-id',
            'data': b'del',
            'type': 'pmessage'
        }
        
        redis_event_listener._handle_state_db_event(message)
        
        # Verify that archive_workflow_state was not called
        redis_event_listener.workflow_state_manager.archive_workflow_state.assert_not_called()

    def test_start_already_running(self, redis_event_listener):
        """
        Test start method when already running.
        """
        redis_event_listener.running = True
        
        with patch.object(threading, 'Thread') as mock_thread:
            redis_event_listener.start()
            
            # Verify that a new thread was not created
            mock_thread.assert_not_called()

    def test_start(self, redis_event_listener):
        """
        Test start method.
        """
        with patch.object(threading, 'Thread') as mock_thread:
            mock_thread_instance = MagicMock()
            mock_thread.return_value = mock_thread_instance
            
            redis_event_listener.start()
            
            assert redis_event_listener.running is True
            mock_thread.assert_called_with(target=redis_event_listener._listen_for_events)
            mock_thread_instance.start.assert_called_once()

    def test_stop_not_running(self, redis_event_listener):
        """
        Test stop method when not running.
        """
        redis_event_listener.running = False
        redis_event_listener.thread = MagicMock()
        
        redis_event_listener.stop()
        
        # Verify that thread.join was not called
        redis_event_listener.thread.join.assert_not_called()

    def test_stop(self, redis_event_listener):
        """
        Test stop method.
        """
        redis_event_listener.running = True
        redis_event_listener.thread = MagicMock()
        
        redis_event_listener.stop()
        
        assert redis_event_listener.running is False
        redis_event_listener.thread.join.assert_called_with(timeout=2.0)

    def test_set_workflow_state_manager(self, redis_event_listener):
        """
        Test set_workflow_state_manager method.
        """
        new_manager = MagicMock()
        redis_event_listener.set_workflow_state_manager(new_manager)
        
        assert redis_event_listener.workflow_state_manager == new_manager
