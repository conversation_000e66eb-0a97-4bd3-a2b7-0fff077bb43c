import pytest
from app.services.initialize_workflow import initialize_workflow_with_params


class TestInitializeWorkflow:
    """
    Test suite for initialize_workflow module.
    Tests workflow parameter initialization, field value replacement, and validation.
    """

    @pytest.fixture
    def basic_workflow(self):
        """
        Provides a basic workflow structure for testing.
        """
        return {
            "transitions": [
                {
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "topic", "field_value": None}
                                    ]
                                }
                            }
                        ]
                    }
                }
            ]
        }

    @pytest.fixture
    def workflow_with_conditional(self):
        """
        Provides a workflow structure with conditional routing for testing.
        """
        return {
            "transitions": [
                {
                    "conditional_routing": {
                        "global_context_definitions": {
                            "variables": [{"name": "max_retries", "value": None}]
                        }
                    }
                }
            ]
        }

    def test_basic_parameter_initialization(self, basic_workflow):
        """
        Test basic parameter initialization with valid input.
        """
        params = {
            "payload": {
                "user_dependent_fields": ["topic"],
                "user_payload_template": {"topic": "test topic"},
            }
        }

        result = initialize_workflow_with_params(basic_workflow, params)

        assert (
            result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][0]["field_value"]
            == "test topic"
        )

    def test_global_context_initialization(self, workflow_with_conditional):
        """
        Test global context variable initialization.
        """
        params = {
            "payload": {
                "user_dependent_fields": [],
                "user_payload_template": {"global_context_defs": {"max_retries": 3}},
            }
        }

        result = initialize_workflow_with_params(workflow_with_conditional, params)

        assert (
            result["transitions"][0]["conditional_routing"][
                "global_context_definitions"
            ]["variables"][0]["value"]
            == 3
        )

    def test_missing_required_field(self, basic_workflow):
        """
        Test handling of missing required field value.
        """
        params = {
            "payload": {"user_dependent_fields": ["topic"], "user_payload_template": {}}
        }

        with pytest.raises(ValueError) as exc_info:
            initialize_workflow_with_params(basic_workflow, params)

        assert "Missing value for required user-dependent field: 'topic'" in str(
            exc_info.value
        )

    def test_missing_required_global_context(self, workflow_with_conditional):
        """
        Test handling of missing required global context variable.
        """
        params = {
            "payload": {
                "user_dependent_fields": [],
                "user_payload_template": {"global_context_defs": {}},
            }
        }

        with pytest.raises(ValueError) as exc_info:
            initialize_workflow_with_params(workflow_with_conditional, params)

        assert (
            "Missing value for required global context variable: 'max_retries'"
            in str(exc_info.value)
        )

    def test_empty_workflow(self):
        """
        Test initialization with empty workflow.
        """
        empty_workflow = {"transitions": []}
        params = {"payload": {"user_dependent_fields": [], "user_payload_template": {}}}

        result = initialize_workflow_with_params(empty_workflow, params)
        assert result == empty_workflow

    def test_workflow_with_multiple_tools(self):
        """
        Test initialization of workflow with multiple tools and parameters.
        """
        workflow = {
            "transitions": [
                {
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "topic", "field_value": None},
                                        {"field_name": "length", "field_value": None},
                                    ]
                                }
                            },
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "format", "field_value": None}
                                    ]
                                }
                            },
                        ]
                    }
                }
            ]
        }

        params = {
            "payload": {
                "user_dependent_fields": ["topic", "format"],
                "user_payload_template": {
                    "topic": "test topic",
                    "length": "10min",
                    "format": "mp4",
                },
            }
        }

        result = initialize_workflow_with_params(workflow, params)

        tools = result["transitions"][0]["node_info"]["tools_to_use"]
        assert tools[0]["tool_params"]["items"][0]["field_value"] == "test topic"
        assert tools[0]["tool_params"]["items"][1]["field_value"] == "10min"
        assert tools[1]["tool_params"]["items"][0]["field_value"] == "mp4"

    def test_mixed_parameter_types(self):
        """
        Test initialization with mixed parameter types (strings, numbers, booleans).
        """
        workflow = {
            "transitions": [
                {
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "count", "field_value": None},
                                        {"field_name": "enabled", "field_value": None},
                                    ]
                                }
                            }
                        ]
                    }
                }
            ]
        }

        params = {
            "payload": {
                "user_dependent_fields": ["count", "enabled"],
                "user_payload_template": {"count": 42, "enabled": True},
            }
        }

        result = initialize_workflow_with_params(workflow, params)

        items = result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
            "items"
        ]
        assert items[0]["field_value"] == 42
        assert items[1]["field_value"] is True

    def test_non_dependent_field_update(self):
        """
        Test updating non-dependent fields with None values.
        """
        workflow = {
            "transitions": [
                {
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {
                                            "field_name": "optional_param",
                                            "field_value": None,
                                        }
                                    ]
                                }
                            }
                        ]
                    }
                }
            ]
        }

        params = {
            "payload": {
                "user_dependent_fields": [],
                "user_payload_template": {"optional_param": "default_value"},
            }
        }

        result = initialize_workflow_with_params(workflow, params)
        assert (
            result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][0]["field_value"]
            == "default_value"
        )

    def test_null_required_field_validation(self):
        """
        Test that a ValueError is only thrown when a field is both null and required.
        """
        # Create a workflow with a node that has a required field
        workflow = {
            "nodes": [
                {
                    "id": "test_node",
                    "server_tools": [
                        {
                            "input_schema": {
                                "predefined_fields": [
                                    {"field_name": "required_field", "required": True},
                                    {"field_name": "optional_field", "required": False},
                                ]
                            }
                        }
                    ],
                }
            ],
            "transitions": [
                {
                    "node_info": {
                        "node_id": "test_node",
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {
                                            "field_name": "required_field",
                                            "field_value": None,
                                        },
                                        {
                                            "field_name": "optional_field",
                                            "field_value": None,
                                        },
                                    ]
                                }
                            }
                        ],
                    }
                }
            ],
        }

        params = {"payload": {"user_dependent_fields": [], "user_payload_template": {}}}

        # Should raise ValueError for the required field
        with pytest.raises(ValueError) as exc_info:
            initialize_workflow_with_params(workflow, params)
        assert "Missing value for required field: 'required_field'" in str(
            exc_info.value
        )

        # Now provide a value for the required field
        params["payload"]["user_payload_template"]["required_field"] = "test_value"

        # Should not raise ValueError now
        result = initialize_workflow_with_params(workflow, params)
        assert (
            result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][0]["field_value"]
            == "test_value"
        )
        # The optional field should still be None
        assert (
            result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][1]["field_value"]
            is None
        )

    def test_field_with_transition_id(self):
        """
        Test initialization with field values that include transition IDs.
        """
        workflow = {
            "transitions": [
                {
                    "id": "transition-1",
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "topic", "field_value": None}
                                    ]
                                }
                            }
                        ]
                    },
                },
                {
                    "id": "transition-2",
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "topic", "field_value": None}
                                    ]
                                }
                            }
                        ]
                    },
                },
            ]
        }

        params = {
            "payload": {
                "user_dependent_fields": ["topic"],
                "user_payload_template": {
                    "topic": {
                        "value": "test topic for transition 2",
                        "transition_id": "transition-2",
                    }
                },
            }
        }

        result = initialize_workflow_with_params(workflow, params)

        # The field in transition-1 should remain None
        assert (
            result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][0]["field_value"]
            is None
        )

        # The field in transition-2 should be updated
        assert (
            result["transitions"][1]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][0]["field_value"]
            == "test topic for transition 2"
        )

    def test_field_with_transition_id_fallback(self):
        """
        Test that fields with transition IDs fall back to name matching when the transition ID is not found.
        """
        workflow = {
            "transitions": [
                {
                    "id": "transition-1",
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "topic", "field_value": None}
                                    ]
                                }
                            }
                        ]
                    },
                }
            ]
        }

        params = {
            "payload": {
                "user_dependent_fields": ["topic"],
                "user_payload_template": {
                    "topic": {
                        "value": "test topic",
                        "transition_id": "non-existent-transition",
                    }
                },
            }
        }

        result = initialize_workflow_with_params(workflow, params)

        # Should fall back to updating by field name
        assert (
            result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][0]["field_value"]
            == "test topic"
        )

    def test_mixed_field_formats(self):
        """
        Test initialization with a mix of field formats (simple values and objects with transition_id).
        """
        workflow = {
            "transitions": [
                {
                    "id": "transition-1",
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "topic", "field_value": None},
                                        {"field_name": "format", "field_value": None},
                                    ]
                                }
                            }
                        ]
                    },
                },
                {
                    "id": "transition-2",
                    "node_info": {
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "topic", "field_value": None},
                                        {"field_name": "voice_id", "field_value": None},
                                    ]
                                }
                            }
                        ]
                    },
                },
            ]
        }

        params = {
            "payload": {
                "user_dependent_fields": ["topic", "format", "voice_id"],
                "user_payload_template": {
                    "topic": {
                        "value": "test topic for transition 2",
                        "transition_id": "transition-2",
                    },
                    "format": "mp4",
                    "voice_id": {"value": "voice-123", "transition_id": "transition-2"},
                },
            }
        }

        result = initialize_workflow_with_params(workflow, params)

        # Simple value should update all matching fields
        assert (
            result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][1]["field_value"]
            == "mp4"
        )

        # Fields with transition IDs should only update in the specified transition
        assert (
            result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][0]["field_value"]
            is None
        )
        assert (
            result["transitions"][1]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][0]["field_value"]
            == "test topic for transition 2"
        )
        assert (
            result["transitions"][1]["node_info"]["tools_to_use"][0]["tool_params"][
                "items"
            ][1]["field_value"]
            == "voice-123"
        )

    def test_required_field_with_handle_mapping_no_validation_error(self):
        """
        Test that required fields with handle mappings do not trigger validation errors.
        This tests the fix for the issue where validation was throwing errors for fields
        that would be resolved later through handle mappings.
        """
        workflow = {
            "nodes": [
                {
                    "id": "test_node",
                    "server_tools": [
                        {
                            "input_schema": {
                                "predefined_fields": [
                                    {"field_name": "mapped_field", "required": True},
                                    {
                                        "field_name": "unmapped_required_field",
                                        "required": True,
                                    },
                                    {"field_name": "optional_field", "required": False},
                                ]
                            }
                        }
                    ],
                }
            ],
            "transitions": [
                {
                    "id": "target_transition",
                    "node_info": {
                        "node_id": "test_node",
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {
                                            "field_name": "mapped_field",
                                            "field_value": None,  # This will be resolved via handle mapping
                                        },
                                        {
                                            "field_name": "unmapped_required_field",
                                            "field_value": None,  # This should trigger validation error
                                        },
                                        {
                                            "field_name": "optional_field",
                                            "field_value": None,  # This should not trigger error (optional)
                                        },
                                    ]
                                }
                            }
                        ],
                        "input_data": [
                            {
                                "from_transition_id": "source_transition",
                                "handle_mappings": [
                                    {
                                        "source_transition_id": "source_transition",
                                        "source_handle_id": "output_field",
                                        "target_handle_id": "mapped_field",  # This field has a handle mapping
                                        "edge_id": "test-edge-1",
                                    }
                                ],
                            }
                        ],
                    },
                }
            ],
        }

        params = {"payload": {"user_dependent_fields": [], "user_payload_template": {}}}

        # Should raise ValueError only for the unmapped required field
        with pytest.raises(ValueError) as exc_info:
            initialize_workflow_with_params(workflow, params)
        assert "Missing value for required field: 'unmapped_required_field'" in str(
            exc_info.value
        )

        # Now provide a value for the unmapped required field
        params["payload"]["user_payload_template"][
            "unmapped_required_field"
        ] = "test_value"

        # Should not raise ValueError now, even though mapped_field is None
        # because it has a handle mapping
        result = initialize_workflow_with_params(workflow, params)

        tools = result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
            "items"
        ]

        # mapped_field should still be None (will be resolved later by handle mapping)
        assert tools[0]["field_value"] is None

        # unmapped_required_field should have the provided value
        assert tools[1]["field_value"] == "test_value"

        # optional_field should remain None (no error since it's optional)
        assert tools[2]["field_value"] is None

    def test_multiple_handle_mappings_validation(self):
        """
        Test validation with multiple handle mappings for different fields.
        """
        workflow = {
            "nodes": [
                {
                    "id": "test_node",
                    "server_tools": [
                        {
                            "input_schema": {
                                "predefined_fields": [
                                    {"field_name": "field1", "required": True},
                                    {"field_name": "field2", "required": True},
                                    {"field_name": "field3", "required": True},
                                ]
                            }
                        }
                    ],
                }
            ],
            "transitions": [
                {
                    "id": "target_transition",
                    "node_info": {
                        "node_id": "test_node",
                        "tools_to_use": [
                            {
                                "tool_params": {
                                    "items": [
                                        {"field_name": "field1", "field_value": None},
                                        {"field_name": "field2", "field_value": None},
                                        {"field_name": "field3", "field_value": None},
                                    ]
                                }
                            }
                        ],
                        "input_data": [
                            {
                                "from_transition_id": "source_transition_1",
                                "handle_mappings": [
                                    {
                                        "source_transition_id": "source_transition_1",
                                        "source_handle_id": "output1",
                                        "target_handle_id": "field1",
                                        "edge_id": "edge-1",
                                    }
                                ],
                            },
                            {
                                "from_transition_id": "source_transition_2",
                                "handle_mappings": [
                                    {
                                        "source_transition_id": "source_transition_2",
                                        "source_handle_id": "output2",
                                        "target_handle_id": "field2",
                                        "edge_id": "edge-2",
                                    }
                                ],
                            },
                        ],
                    },
                }
            ],
        }

        params = {"payload": {"user_dependent_fields": [], "user_payload_template": {}}}

        # Should raise ValueError only for field3 (no handle mapping)
        with pytest.raises(ValueError) as exc_info:
            initialize_workflow_with_params(workflow, params)
        assert "Missing value for required field: 'field3'" in str(exc_info.value)

        # Provide value for field3
        params["payload"]["user_payload_template"]["field3"] = "manual_value"

        # Should not raise any errors now
        result = initialize_workflow_with_params(workflow, params)

        tools = result["transitions"][0]["node_info"]["tools_to_use"][0]["tool_params"][
            "items"
        ]

        # field1 and field2 should remain None (have handle mappings)
        assert tools[0]["field_value"] is None
        assert tools[1]["field_value"] is None

        # field3 should have the provided value
        assert tools[2]["field_value"] == "manual_value"
