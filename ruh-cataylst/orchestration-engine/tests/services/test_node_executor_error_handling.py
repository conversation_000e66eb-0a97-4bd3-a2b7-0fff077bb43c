import pytest
import async<PERSON>
import json
from unittest.mock import <PERSON><PERSON>, AsyncMock, patch
from aiokafka import AIOKafkaProducer
from app.services.node_executor import <PERSON>de<PERSON>xecutor, NodeExecutionError


class TestNodeExecutorErrorHandling:
    """
    Test suite for NodeExecutor error handling.
    """

    @pytest.fixture
    def mock_producer(self):
        """
        Provides a mock Kafka producer.
        """
        mock = AsyncMock(spec=AIOKafkaProducer)
        mock._sender = Mock()
        mock._sender._running = True
        return mock

    @pytest.fixture
    async def node_executor(self, mock_producer):
        """
        Provides a NodeExecutor instance with mocked consumer.
        """
        executor = NodeExecutor(producer=mock_producer)
        
        # Mock the consumer and consumer task
        executor._consumer = Mock()
        executor._consumer_task = Mock()
        executor._consumer_task.done.return_value = False
        
        # Create a method to simulate receiving a message
        async def simulate_message(message):
            # Find the future for this request_id
            request_id = message.get("request_id")
            if request_id in executor._pending_requests:
                future = executor._pending_requests[request_id]
                
                # Process the message as if it came from Kafka
                result_data = message.get("result")
                error_data = message.get("error")
                
                if error_data:
                    future.set_exception(NodeExecutionError(f"Node execution failed: {error_data}"))
                elif message.get("status") == "error":
                    error_msg = "Unknown error"
                    if isinstance(result_data, dict) and "error" in result_data:
                        error_msg = result_data["error"]
                        if "data" in result_data and isinstance(result_data["data"], dict):
                            detail = result_data["data"].get("detail")
                            if detail:
                                error_msg = f"{error_msg} - {detail}"
                    future.set_exception(NodeExecutionError(f"Node execution failed: {error_msg}"))
                elif isinstance(result_data, dict) and "error" in result_data and result_data["error"]:
                    error_msg = result_data["error"]
                    if "data" in result_data and isinstance(result_data["data"], dict):
                        detail = result_data["data"].get("detail")
                        if detail:
                            error_msg = f"{error_msg} - {detail}"
                    future.set_exception(NodeExecutionError(f"Node execution failed: {error_msg}"))
                else:
                    future.set_result(result_data)
        
        executor.simulate_message = simulate_message
        
        return executor

    @pytest.mark.asyncio
    async def test_direct_error_field(self, node_executor):
        """
        Test handling of responses with a direct error field.
        """
        # Arrange
        tool_name = "test_tool"
        tool_parameters = {"param1": "value1"}
        
        # Set up the producer to simulate sending a message
        node_executor.producer.send.return_value = None
        
        # Create a task to execute the tool
        execute_task = asyncio.create_task(
            node_executor.execute_tool("test/path", tool_name, tool_parameters)
        )
        
        # Wait a bit to ensure the future is created
        await asyncio.sleep(0.1)
        
        # Simulate receiving an error response
        error_response = {
            "request_id": list(node_executor._pending_requests.keys())[0],
            "error": "Direct error message",
            "status": "error"
        }
        
        # Act
        await node_executor.simulate_message(error_response)
        
        # Assert
        with pytest.raises(NodeExecutionError) as exc_info:
            await execute_task
        
        assert "Node execution failed: Direct error message" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_error_status_with_nested_error(self, node_executor):
        """
        Test handling of responses with error status and nested error in result.
        """
        # Arrange
        tool_name = "test_tool"
        tool_parameters = {"param1": "value1"}
        
        # Set up the producer to simulate sending a message
        node_executor.producer.send.return_value = None
        
        # Create a task to execute the tool
        execute_task = asyncio.create_task(
            node_executor.execute_tool("test/path", tool_name, tool_parameters)
        )
        
        # Wait a bit to ensure the future is created
        await asyncio.sleep(0.1)
        
        # Simulate receiving an error response with nested error
        error_response = {
            "request_id": list(node_executor._pending_requests.keys())[0],
            "status": "error",
            "result": {
                "error": "API request failed with status 500 (Internal Server Error)",
                "status_code": 500,
                "data": {
                    "detail": "This endpoint always returns a 500 error"
                }
            }
        }
        
        # Act
        await node_executor.simulate_message(error_response)
        
        # Assert
        with pytest.raises(NodeExecutionError) as exc_info:
            await execute_task
        
        expected_error = "Node execution failed: API request failed with status 500 (Internal Server Error) - This endpoint always returns a 500 error"
        assert expected_error in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_success_status_with_error_in_result(self, node_executor):
        """
        Test handling of responses with success status but error in result.
        """
        # Arrange
        tool_name = "test_tool"
        tool_parameters = {"param1": "value1"}
        
        # Set up the producer to simulate sending a message
        node_executor.producer.send.return_value = None
        
        # Create a task to execute the tool
        execute_task = asyncio.create_task(
            node_executor.execute_tool("test/path", tool_name, tool_parameters)
        )
        
        # Wait a bit to ensure the future is created
        await asyncio.sleep(0.1)
        
        # Simulate receiving a response with success status but error in result
        error_response = {
            "request_id": list(node_executor._pending_requests.keys())[0],
            "status": "success",  # Note: status is success
            "result": {
                "error": "API request failed with status 500 (Internal Server Error)",
                "status_code": 500,
                "data": {
                    "detail": "This endpoint always returns a 500 error"
                }
            }
        }
        
        # Act
        await node_executor.simulate_message(error_response)
        
        # Assert
        with pytest.raises(NodeExecutionError) as exc_info:
            await execute_task
        
        expected_error = "Node execution failed: API request failed with status 500 (Internal Server Error) - This endpoint always returns a 500 error"
        assert expected_error in str(exc_info.value)
