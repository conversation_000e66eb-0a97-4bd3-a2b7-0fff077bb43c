"""
Test script for PostgreSQL implementation of workflow state and transition result persistence.
This script tests the functionality of archiving Redis data to PostgreSQL when Redis keys expire.

Usage:
    poetry run python test_postgres_implementation.py
"""

import json
import time
import uuid
import sys
import logging
from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.services.db_connections.redis_event_listener import get_redis_event_listener
from app.core_.state_manager import WorkflowStateManager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("PostgresTest")

def test_redis_to_postgres_archiving():
    """
    Test the Redis to PostgreSQL archiving functionality.
    
    This test:
    1. Creates a unique workflow ID
    2. Stores workflow state and transition results in Redis
    3. Manually deletes the Redis keys to simulate expiration
    4. Verifies the data is archived to PostgreSQL
    """
    # Generate a unique workflow ID for this test
    workflow_id = f"test-workflow-{uuid.uuid4()}"
    correlation_id = workflow_id  # Using the same ID for simplicity
    
    logger.info(f"Starting test with workflow ID: {workflow_id}")
    
    # Initialize Redis managers
    results_redis_manager = RedisManager(db_index=int(settings.redis_results_db_index))
    state_redis_manager = RedisManager(db_index=int(settings.redis_state_db_index))
    
    # Initialize PostgreSQL manager
    postgres_manager = get_postgres_manager()
    
    # Initialize workflow state manager
    state_manager = WorkflowStateManager(workflow_id=workflow_id)
    state_manager.results_redis_manager = results_redis_manager
    state_manager.state_redis_manager = state_redis_manager
    state_manager.postgres_manager = postgres_manager
    
    # Initialize Redis event listener with the state manager
    redis_event_listener = get_redis_event_listener(state_manager)
    
    # Create test data
    workflow_state = {
        "pending_transitions": ["transition1", "transition2"],
        "completed_transitions": [],
        "waiting_transitions": [],
        "terminated": False,
        "paused": False
    }
    
    transition_result = {
        "status": "success",
        "result": {
            "data": "Test result data",
            "timestamp": time.time()
        }
    }
    
    # Store data in Redis
    logger.info("Storing test data in Redis...")
    
    # Store workflow state
    workflow_state_key = f"workflow_state:{workflow_id}"
    state_redis_manager.set_value(workflow_state_key, json.dumps(workflow_state))
    
    # Store transition result
    transition_id = f"transition1-{uuid.uuid4()}"
    transition_result_key = f"result:{transition_id}"
    results_redis_manager.set_value(transition_result_key, json.dumps(transition_result))
    
    logger.info(f"Stored workflow state with key: {workflow_state_key}")
    logger.info(f"Stored transition result with key: {transition_result_key}")
    
    # Verify data is in Redis
    stored_state = state_redis_manager.get_value(workflow_state_key)
    stored_result = results_redis_manager.get_value(transition_result_key)
    
    if stored_state and stored_result:
        logger.info("Data successfully stored in Redis")
    else:
        logger.error("Failed to store data in Redis")
        return False
    
    # Wait a moment to ensure the Redis event listener is ready
    logger.info("Waiting for Redis event listener to initialize...")
    time.sleep(2)
    
    # Delete the Redis keys to simulate expiration
    logger.info("Deleting Redis keys to simulate expiration...")
    
    # Delete transition result first
    results_redis_manager.delete_value(transition_result_key)
    logger.info(f"Deleted transition result key: {transition_result_key}")
    
    # Wait for the event to be processed
    time.sleep(2)
    
    # Delete workflow state
    state_redis_manager.delete_value(workflow_state_key)
    logger.info(f"Deleted workflow state key: {workflow_state_key}")
    
    # Wait for the event to be processed
    time.sleep(2)
    
    # Verify data was archived to PostgreSQL
    logger.info("Checking if data was archived to PostgreSQL...")
    
    # Check for transition result in PostgreSQL
    pg_transition_result = postgres_manager.get_transition_result(correlation_id, transition_id)
    
    if pg_transition_result:
        logger.info("Transition result successfully archived to PostgreSQL:")
        logger.info(json.dumps(pg_transition_result, indent=2))
    else:
        logger.error("Failed to find transition result in PostgreSQL")
        return False
    
    # Check for workflow state in PostgreSQL
    pg_workflow_state = postgres_manager.get_workflow_state(correlation_id)
    
    if pg_workflow_state:
        logger.info("Workflow state successfully archived to PostgreSQL:")
        logger.info(json.dumps(pg_workflow_state, indent=2))
    else:
        logger.error("Failed to find workflow state in PostgreSQL")
        return False
    
    logger.info("Test completed successfully!")
    return True

if __name__ == "__main__":
    logger.info("Starting PostgreSQL archiving test...")
    
    try:
        success = test_redis_to_postgres_archiving()
        if success:
            logger.info("✅ Test passed: Redis to PostgreSQL archiving works correctly")
            sys.exit(0)
        else:
            logger.error("❌ Test failed: Redis to PostgreSQL archiving did not work as expected")
            sys.exit(1)
    except Exception as e:
        logger.exception(f"❌ Test failed with exception: {e}")
        sys.exit(1)
