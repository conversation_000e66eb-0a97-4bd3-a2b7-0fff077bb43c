# arc2-orchestration-python

## Overview

`arc2-orchestration-python` is a Python library for orchestrating complex workflows and tasks.  It simplifies the process of managing dependencies, parallel execution, and error handling for multiple tasks within a larger process.  This system is designed for [Target use case, e.g., data processing pipelines, microservice interactions, automated deployments].

## Key Features

* **Workflow Orchestration:**  Orchestrates complex workflows defined using a JSON-based schema.  Manages task dependencies and execution order.
* **gRPC Communication:** Uses gRPC for seamless communication with external tools and services via a server (`executor_server.py`).
* **Parallel Task Execution:** Leverages `asyncio` for concurrent execution of tasks, maximizing efficiency.
* **Conditional Routing:** Supports conditional logic within workflows, enabling dynamic branching based on intermediate results.
* **Reflection/Iteration:**  Handles iterative workflows where tasks can refer back to previous steps, allowing for looping and refinement.
* **Schema Validation:**  Enforces a defined JSON schema for workflow definitions, ensuring data integrity and preventing errors.
* **Extensible Tool Integration:** Designed to integrate with various tools and services through a pluggable architecture (specified in the workflow definition).
* **Robust Error Handling:** Includes mechanisms for handling errors during task execution and reporting them appropriately.
* **Result Callback:** Allows for custom processing of task results via a callback function.

## Architecture

The `arc2-orchestration-python` system is built around a core workflow engine (`executor_core.py`) that interacts with external tools via gRPC. The architecture can be broken down into the following key components:

1. **Workflow Definition (JSON):**  Workflows are defined using a JSON schema that specifies tasks, dependencies, conditional logic, and tool integrations.  This schema is validated before execution.

2. **gRPC Server (`executor_server.py`):** A gRPC server acts as an intermediary, receiving workflow definitions and requests from clients.  It manages the execution lifecycle and communicates with external tools.

3. **Workflow Engine (`executor_core.py`):** The core of the system, responsible for:
    * Parsing and validating the workflow definition.
    * Scheduling and executing tasks in parallel using `asyncio`.
    * Resolving task dependencies.
    * Handling conditional routing and reflection logic.
    * Collecting and reporting results.

4. **External Tools:**  The workflow engine interacts with various external tools or services (likely microservices or server scripts) via gRPC.  These tools perform specific tasks within the workflow.

5. **Result Callback (Optional):** The engine allows defining a callback function for customized processing of the workflow results after execution.

## Setup and Installation

1. **Clone the Repository:**

   ```bash
   git clone https://gitlab.rapidinnovation.tech/arcadia-2/arc2-orchestration-python.git
   cd arc2-orchestration-python
   ```

2. **Create a Virtual Environment (Recommended):**

 ```bash
    python3 -m venv venv
    source .venv/bin/activate  # Linux/macOS
    .venv\Scripts\activate  # Windows
```

3. **Install Required Packages:**
Install Dependencies:

    ```bash
    pip install -r requirements.txt
    ```

## Usage

**Start the gRPC Server:**

Navigate to the arc2-orchestration-python directory and run the gRPC server:

```bash
python executor_server.py
```

This starts the server, listening for incoming task requests. Ensure it's running before proceeding.

**Run the gRPC Client:**

Navigate to the arc2-orchestration-python\testing directory. Run the gRPC client.

```bash
python executor_client.py
```

The client will send a task definition to the server, receive the results, and print them.
