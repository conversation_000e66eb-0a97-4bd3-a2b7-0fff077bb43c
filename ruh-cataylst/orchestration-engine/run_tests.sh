#!/bin/bash

# Ensure we're in the project root directory
cd "$(dirname "$0")"

echo "Running tests for Orchestration Engine..."

# Clean up previous test results
rm -rf htmlcov
rm -f .coverage

# Install dependencies
echo "Installing dependencies..."
poetry install --no-root    

# Run tests with coverage
echo "Running tests with coverage..."
poetry run pytest --cov=app --cov-report=term-missing --cov-report=html

# The coverage report will be available in htmlcov/index.html
echo "Coverage report generated in htmlcov/index.html"

# Check if coverage is below threshold (80%)
coverage_score=$(poetry run coverage report | grep "TOTAL" | awk '{print $4}' | sed 's/%//')
if (( $(echo "$coverage_score < 80" | bc -l) )); then
    echo "Coverage is below 80% ($coverage_score%)"
    exit 1
else
    echo "Coverage is $coverage_score%"
fi