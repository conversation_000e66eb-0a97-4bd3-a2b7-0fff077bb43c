import json
import re
import logging

# Try to import enhanced logger, fall back to standard logging for testing
try:
    from app.utils.enhanced_logger import get_logger

    logger = get_logger("HelperFunctions")
except Exception:
    logger = logging.getLogger("HelperFunctions")

from app.utils.semantic_type_extractor import (
    extract_semantic_type,
    extract_semantic_type_for_nested,
)


def load_schema(file_path):
    """
    Loads the JSON schema from the specified file path.
    """
    with open(file_path, "r") as file:
        return json.load(file)


def fix_invalid_escapes(json_str):
    return re.sub(r'\\(?!["\\/bfnrt])', r"\\\\", json_str)


def safe_json_loads(s: str):
    try:
        # First, attempt to parse normally
        return json.loads(s)
    except json.JSONDecodeError as e:
        if "Invalid \\escape" in str(e):
            # Escape all invalid backslashes that are not part of valid escape sequences
            # This pattern replaces \ with \\ only if it's not followed by a valid escape character
            s = re.sub(r'\\(?!["\\/bfnrtu])', r"\\\\", s)
            try:
                return json.loads(s)
            except json.JSONDecodeError as e2:
                raise ValueError(f"JSON decoding failed even after sanitizing: {e2}")
        else:
            raise


def format_execution_result(output_schema, execution_result):
    """
    Formats the execution result based on the simplified output schema.
    Includes detailed debugging prints to track data types.
    """
    formatted_result = []
    # logger.debug("--- START format_execution_result ---")
    # logger.debug("output_schema:", output_schema)
    # logger.debug(f"execution_result (at function start):{execution_result}")
    # logger.debug(
    #     f"Type of execution_result (at function start):{type(execution_result)}"
    # )
    if not execution_result:
        logger.debug("execution_result is empty, returning empty list")
        return formatted_result

    field_name_to_description = {}
    if "predefined_fields" in output_schema:
        # logger.debug("predefined_fields found in output_schema")
        for field_def in output_schema["predefined_fields"]:
            if "field_name" in field_def:
                field_name_to_description[field_def["field_name"]] = field_def
        # logger.debug(f"field_name_to_description:, {field_name_to_description}")
    else:
        logger.warning("WARNING: predefined_fields NOT found in output_schema!")

    def handle_data_type(data_value, data_type):
        if data_type == "string":
            return str(data_value)
        elif data_type == "array":
            return data_value if isinstance(data_value, list) else [data_value]
        elif data_type == "object":
            if isinstance(data_value, dict):
                return data_value
            elif isinstance(data_value, str):
                # Try to parse JSON string for object type
                try:
                    import json

                    parsed_value = json.loads(data_value)
                    if isinstance(parsed_value, dict):
                        return parsed_value
                    else:
                        # If parsed value is not a dict, wrap it
                        return {"value": data_value}
                except (json.JSONDecodeError, ValueError):
                    # If JSON parsing fails, wrap the original string
                    return {"value": data_value}
            else:
                # For non-dict, non-string values, wrap them
                return {"value": data_value}
        elif data_type == "number":
            return float(data_value)
        else:
            return data_value

    def process_item(property_name, data_value, field_def):
        data_type = "text"
        if "data_type" in field_def and "type" in field_def["data_type"]:
            data_type = field_def["data_type"]["type"]
            logger.debug(f"Data type set to '{data_type}' based on schema")
        else:
            logger.warning(
                f"WARNING: Data type for '{property_name}' not found in schema description"
            )

        if data_type == "array" and isinstance(data_value, list):
            nested_items = []
            item_type = field_def["data_type"].get("items", {}).get("type", "string")
            for item in data_value:
                if item_type == "object" and isinstance(item, dict):
                    nested_properties = field_def["data_type"]["items"].get(
                        "properties", {}
                    )
                    nested_field_defs = [
                        {"field_name": k, "data_type": v}
                        for k, v in nested_properties.items()
                    ]
                    nested_items.append(
                        {
                            "data": [
                                process_item(
                                    nested_property["field_name"],
                                    item[nested_property["field_name"]],
                                    nested_property,
                                )
                                for nested_property in nested_field_defs
                                if nested_property["field_name"] in item
                            ],
                            "data_type": item_type,
                            "property_name": property_name,
                            "semantic_type": "object",  # nested object in array
                        }
                    )
                else:
                    # For array items, extract semantic type from the array's item definition
                    item_semantic_type = "string"  # default
                    items_info = field_def["data_type"].get("items", {})
                    if isinstance(items_info, dict) and "format" in items_info:
                        item_format = items_info.get("format")
                        if item_format and isinstance(item_format, str):
                            item_semantic_type = item_format.strip().lower()

                    nested_items.append(
                        {
                            "data": handle_data_type(item, item_type),
                            "data_type": item_type,
                            "property_name": property_name,
                            "semantic_type": item_semantic_type,
                        }
                    )

            # Extract semantic type for the array itself
            array_semantic_type = extract_semantic_type_for_nested(field_def, data_type)
            formatted_item = {
                "data": nested_items,
                "data_type": data_type,
                "property_name": property_name,
                "semantic_type": array_semantic_type,
            }
        elif data_type == "object" and isinstance(data_value, dict):
            nested_items = []
            properties = field_def["data_type"].get("properties", {})
            for nested_property_name, nested_data_value in data_value.items():
                if nested_property_name in properties:
                    nested_field_def = {
                        "field_name": nested_property_name,
                        "data_type": properties[nested_property_name],
                    }
                    nested_items.append(
                        process_item(
                            nested_property_name, nested_data_value, nested_field_def
                        )
                    )
                else:
                    nested_items.append(
                        {
                            "data": nested_data_value,
                            "data_type": "unknown",
                            "property_name": nested_property_name,
                            "semantic_type": "string",  # default for unknown properties
                        }
                    )

            # Extract semantic type for the object itself
            object_semantic_type = extract_semantic_type_for_nested(
                field_def, data_type
            )
            formatted_item = {
                "data": nested_items,
                "data_type": data_type,
                "property_name": property_name,
                "semantic_type": object_semantic_type,
            }
        else:
            # Extract semantic type for simple data types
            semantic_type = extract_semantic_type(field_def)
            formatted_item = {
                "data": handle_data_type(data_value, data_type),
                "data_type": data_type,
                "property_name": property_name,
                "semantic_type": semantic_type,
            }
        # logger.debug(f"Formatted item: {formatted_item}")
        return formatted_item

    # logger.debug("Starting to process execution_result items...")
    for item in execution_result:
        if isinstance(item, dict):
            for property_name, data_value in item.items():
                logger.debug(f"Property: {property_name} Value: {data_value}")
                if property_name in field_name_to_description:
                    logger.debug(
                        f"Property '{property_name}' found in schema description"
                    )
                    field_def = field_name_to_description[property_name]
                    formatted_result.append(
                        process_item(property_name, data_value, field_def)
                    )
                else:
                    logger.debug(
                        f"WARNING: Property '{property_name}' NOT found in schema description"
                    )
                    formatted_result.append(
                        {
                            "data": data_value,
                            "data_type": "unknown",
                            "property_name": property_name,
                            "semantic_type": "string",  # default for unknown properties
                        }
                    )
        else:
            # Handle non-dictionary items (strings, numbers, etc.)
            logger.debug(
                f"Processing non-dict item: {type(item)} - {str(item)[:100]}..."
            )

            # If no schema is defined or item doesn't match schema, create a generic result
            if not field_name_to_description:
                # No schema defined, create a generic content field
                formatted_result.append(
                    {
                        "data": item,
                        "data_type": "string" if isinstance(item, str) else "unknown",
                        "property_name": "content",
                        "semantic_type": "string",
                    }
                )
            else:
                # Schema exists but item doesn't match, try to map to first field or create generic
                first_field_name = next(
                    iter(field_name_to_description.keys()), "content"
                )
                formatted_result.append(
                    {
                        "data": item,
                        "data_type": "string" if isinstance(item, str) else "unknown",
                        "property_name": first_field_name,
                        "semantic_type": "string",
                    }
                )

    # logger.debug("--- END format_execution_result ---")
    # logger.debug(f"Final formatted_result: {formatted_result}")
    return formatted_result
