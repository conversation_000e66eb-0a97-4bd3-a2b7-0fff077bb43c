import re
import json
from app.config.config import settings
from autogen_agentchat.agents import AssistantAgent  # type: ignore
from autogen_agentchat.messages import TextMessage  # type: ignore
from autogen_core import CancellationToken  # type: ignore
from autogen_ext.models.openai import OpenAIChatCompletionClient  # type: ignore
from app.utils.helper_functions import safe_json_loads


async def format_schema(
    input_schema, output_schema, previous_results, current_tool_params
):

    SYSTEM_MESSAGE = """
    You will be given:
    1. The **input schema** of the tool that will be called.
    2. The **output schema** of the previous tool that was called.
    3. The **output from the previous server** (if available).
    4. The **explicit params** from the JSON schema, which are absolute and must be used as is.

    Your task is to construct the **params** for the next tool call by following these rules:
    - **Absolute adherence to the JSON schema**: Any params explicitly defined in the schema must be used as given and cannot be modified.
    - **Handling placeholders**: If the JSON schema contains placeholders (e.g., `"field_value": "${user_name}"`), replace them using available data from the previous server output.
    - **Filling missing values**: If the input schema has required fields that are not provided explicitly in the JSON schema, check the previous server's output for matching fields and use them if available.
    - **No unnecessary modifications**: Do not change, add, or remove explicitly provided values from the schema. Only assist in completing missing params based on available data.
    - **Use output schema as a guide**: Examine the output schema to understand the structure and format of the data returned by the previous tool. This will help you identify the correct fields to extract when replacing placeholders or filling missing values.
    - **Match data types correctly**: Ensure that parameter values match the expected types defined in the input schema. Reference the output schema to understand the data types of available fields.

    Output only the final **params** in JSON format, ready for direct use. No explanations or additional comments.
    """
    # Create an agent that uses the OpenAI GPT-4o model.
    model_client = OpenAIChatCompletionClient(
        model=settings.model_name,
        api_key=settings.openai_api_key.get_secret_value(),
    )
    agent = AssistantAgent(
        name="assistant",
        model_client=model_client,
        system_message=SYSTEM_MESSAGE,
    )
    response = await agent.on_messages(
        [
            TextMessage(
                content=f"""1. the input schema for tool is {input_schema}.
                     2. the output schema from the previous server is {output_schema}.
                     3. the result output from the previous server is {previous_results}.
                     4. Explicit params from the JSON schema are {current_tool_params}""",
                source="user",
            )
        ],
        cancellation_token=CancellationToken(),
    )

    # Extract content from chat_message
    message_text = response.chat_message.content  # This contains the JSON string

    # Remove ```json\n and ``` at the beginning and end
    message_text = re.sub(r"^```json\n|\n```$", "", message_text)

    # Parse JSON
    result = safe_json_loads(message_text)
    return result


from typing import Any


async def resolve_switch_case(actual_value: Any, expected_value: Any) -> bool:
    """
    Uses an AI agent to evaluate if the actual_value meaningfully corresponds
    to the expected_value based on context. Returns True if they correspond, False otherwise.
    """
    SYSTEM_MESSAGE = """
    You are an expert evaluation agent. Your task is to determine if an 'actual value' meaningfully corresponds to an 'expected value'.

    You will be given:
    1.  The **Actual Value**: This is the data obtained from a previous step or context.
    2.  The **Expected Value**: This is the value defined in a condition that we are checking against.

    Your objective is to analyze both values and decide if the **Actual Value** satisfies or logically matches the **Expected Value**. Consider the context and potential variations. For example:
    - If Actual is a detailed error message and Expected is "error", they correspond.
    - If Actual is a user sentiment score of 0.8 and Expected is "positive", they correspond.
    - If Actual is `{"status": "completed", "items": 5}` and Expected is "completed", they correspond.
    - If Actual is `["apple", "banana"]` and Expected is "banana", they correspond (contains).

    Output only the word "True" if the Actual Value meaningfully corresponds to the Expected Value, otherwise output only the word "False". Do not provide any explanations or additional comments.
    """

    # Agent for switch-case evaluation
    model_client = OpenAIChatCompletionClient(
        model=settings.model_name,
        api_key=settings.openai_api_key.get_secret_value(),
    )
    agent = AssistantAgent(
        name="switch_case_agent",
        model_client=model_client,
        system_message=SYSTEM_MESSAGE,
    )

    response = await agent.on_messages(
        [
            TextMessage(
                content=f"""Actual Value: {json.dumps(actual_value)}
                     Expected Value: {json.dumps(expected_value)}""",
                source="user",
            )
        ],
        cancellation_token=CancellationToken(),
    )

    message_text = response.chat_message.content
    # Parse the boolean response
    result_str = message_text.strip().lower()
    if result_str == "true":
        return True
    elif result_str == "false":
        return False
    else:
        # Log warning here for unexpected response from agent
        return False
