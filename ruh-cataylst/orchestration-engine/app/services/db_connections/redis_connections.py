import redis  # type: ignore
from app.config.config import settings
from app.utils.enhanced_logger import get_logger

logger = get_logger("RedisManager")


class RedisManager:
    """
    Manages connections and operations with a Redis server, now supporting multiple databases.
    """

    def __init__(self, db_index=0):
        """
        Initializes the RedisManager by loading environment variables and
        attempting to establish a connection to Redis on the specified database.

        Args:
            db_index (int): The database index to connect to in Redis. Defaults to 0.
        """

        self.logger = logger
        self.redis_host = settings.redis_host
        self.redis_port = settings.redis_port
        self.redis_password = (
            settings.redis_password.get_secret_value()
            if settings.redis_password
            else None
        )
        self.db_index = db_index

        if not self.redis_host or not self.redis_port or self.redis_password is None:
            raise ValueError(
                "Redis connection details are missing in .env file. "
                "Please ensure REDIS_HOST, REDIS_PORT, and REDIS_PASSWORD are set."
            )

        self.redis_client = None
        self._connect()

    def _connect(self):
        try:
            # Log connection attempt
            self.logger.info(
                f"Connecting to Redis: host={self.redis_host}, port={self.redis_port}, "
                f"password={'provided' if self.redis_password else 'not provided'}, db_index={self.db_index}"
            )

            # Only include password if it's not empty
            connection_params = {
                "host": self.redis_host,
                "port": int(self.redis_port),
                "password": self.redis_password,
                "db": self.db_index,
                "decode_responses": True,
            }

            self.redis_client = redis.Redis(**connection_params)
            self.redis_client.ping()
            self.logger.info(
                f"Successfully connected to Redis on DB index: {self.db_index}!"
            )
        except redis.exceptions.ConnectionError as e:
            self.logger.error(
                f"Error connecting to Redis on DB index {self.db_index}: {e}"
            )
            self.redis_client = None  # Reset client to None in case of error
            raise  # Re-raise the exception to be handled by the user if needed

    def is_connected(self):
        """
        Checks if the Redis client is currently connected to the server.
        """
        return self.redis_client is not None and self.redis_client.ping() is True

    def set_value(self, key, value, ttl=None):
        """
        Sets a key-value pair in Redis with an optional TTL.

        Args:
            key (str): The key to store.
            value (str): The value to store. Consider serializing objects to strings (e.g., JSON) if needed.
            ttl (int, optional): Time-to-live in seconds. If None, uses default TTL based on key prefix.

        Returns:
            bool: True if the value was set successfully, False otherwise.
        """
        if not self.is_connected():
            self.logger.error("Redis is not connected. Please check your connection.")
            return False
        try:
            # Determine TTL based on key prefix if not explicitly provided
            if ttl is None:
                from app.config.config import settings

                if key.startswith("result:"):
                    ttl = settings.redis_results_ttl
                    self.logger.debug(
                        f"Using default results TTL: {ttl} seconds for key '{key}'"
                    )
                elif key.startswith("workflow_state:"):
                    ttl = settings.redis_state_ttl
                    self.logger.debug(
                        f"Using default state TTL: {ttl} seconds for key '{key}'"
                    )
                else:
                    # No TTL for other keys
                    ttl = None
                    self.logger.debug(f"No TTL set for key '{key}' (unknown prefix)")

            # Set the value with TTL if provided
            if ttl is not None:
                self.redis_client.setex(key, ttl, value)
                self.logger.debug(f"Set key '{key}' with TTL of {ttl} seconds")
            else:
                self.redis_client.set(key, value)
                self.logger.debug(f"Set key '{key}' with no TTL")

            return True
        except redis.exceptions.RedisError as e:
            self.logger.error(f"Error setting value for key '{key}': {e}")
            return False

    def get_value(self, key):
        """
        Retrieves the value associated with a key from Redis.

        Args:
            key (str): The key to retrieve.

        Returns:
            str or None: The value associated with the key, or None if the key does not exist or an error occurred.
        """
        if not self.is_connected():
            self.logger.error("Redis is not connected. Please check your connection.")
            return None
        try:
            value = self.redis_client.get(key)
            return value
        except redis.exceptions.RedisError as e:
            self.logger.error(f"Error getting value for key '{key}': {e}")
            return None

    def update_value(self, key, value):
        """
        Updates the value of an existing key in Redis (in Redis, set operation overwrites if key exists).
        If the key does not exist, it will create it.

        Args:
            key (str): The key to update.
            value (str): The new value.

        Returns:
            bool: True if the value was updated/set successfully, False otherwise.
        """
        return self.set_value(key, value)

    def delete_value(self, key):
        """
        Deletes a key-value pair from Redis.

        Args:
            key (str): The key to delete.

        Returns:
            bool: True if the key was deleted successfully, False otherwise.
        """
        if not self.is_connected():
            self.logger.error("Redis is not connected. Please check your connection.")
            return False
        try:
            self.redis_client.delete(key)
            return True
        except redis.exceptions.RedisError as e:
            self.logger.error(f"Error deleting key '{key}': {e}")
            return False

    def close_connection(self):
        """
        Closes the connection to the Redis server explicitly.
        While not strictly necessary in many cases due to Python's garbage collection,
        it's good practice to close connections when you are done with them, especially in long-running applications.
        """
        if self.redis_client:
            self.redis_client.close()
            self.redis_client = None
            self.logger.info("Redis connection closed.")


# Example Usage:
# if __name__ == "__main__":
#     try:
#         redis_manager = RedisManager()

#         # Basic CRUD operations
#         if redis_manager.is_connected():
#             self.logger.info("\n--- Setting values ---")
#             redis_manager.set_value("result:job1",
#                                     "Job 1 completed successfully")
#             redis_manager.set_value("result:job2", "Job 2 is still running...")

#             self.logger.info("\n--- Getting values ---")
#             job1_result = redis_manager.get_value("result:job1")
#             job2_result = redis_manager.get_value("result:job2")
#             self.logger.info(f"Result for job1: {job1_result}")
#             self.logger.info(f"Result for job2: {job2_result}")

#             self.logger.info("\n--- Updating value ---")
#             redis_manager.update_value("result:job2",
#                                        "Job 2 completed with errors")
#             updated_job2_result = redis_manager.get_value("result:job2")
#             self.logger.info(f"Updated result for job2: {updated_job2_result}")

#             self.logger.info("\n--- Deleting value ---")
#             redis_manager.delete_value("result:job1")
#             job1_result_after_delete = redis_manager.get_value("result:job1")
#             self.logger.info(
#                 f"Result for job1 after deletion: {job1_result_after_delete}"
#             )  # Should be None

#             redis_manager.close_connection()
#         else:
#             self.logger.error(
#                 "Failed to connect to Redis. Check connection details and Redis server status."
#             )

#     except ValueError as ve:
#         self.logger.error(f"Configuration Error: {ve}")
#     except redis.exceptions.ConnectionError as ce:
#         self.logger.error(f"Connection Error (during initialization): {ce}")
#     except Exception as e:
#         self.logger.error(f"An unexpected error occurred: {e}")
