import json


def _has_handle_mapping_for_field(field_name, transition, workflow):
    """
    Check if a field has a corresponding handle mapping defined in the workflow.

    Args:
        field_name: The name of the field to check
        transition: The transition containing the field
        workflow: The workflow containing handle mapping information

    Returns:
        bool: True if the field has a handle mapping, False otherwise
    """
    # Get input_data configurations from the transition
    input_data_configs = transition.get("node_info", {}).get("input_data", [])

    # Check each input_data configuration for handle mappings
    for input_config in input_data_configs:
        handle_mappings = input_config.get("handle_mappings", [])

        # Check if any handle mapping targets this field
        for mapping in handle_mappings:
            target_handle_id = mapping.get("target_handle_id")
            if target_handle_id == field_name:
                return True

    return False


def _parse_json_if_needed(value, field_name, transition, workflow):
    """
    Parse JSON string if the field is expected to be an object type.

    Args:
        value: The field value to potentially parse
        field_name: The name of the field
        transition: The transition containing the field
        workflow: The workflow containing schema information

    Returns:
        Parsed value if it was a JSON string for an object field, otherwise original value
    """
    # Only process string values that look like JSON
    if not isinstance(value, str) or not (
        value.strip().startswith("{") or value.strip().startswith("[")
    ):
        return value

    # Get the node_id to find the corresponding input schema
    node_id = transition.get("node_info", {}).get("node_id")

    # Find the node in the nodes array
    for node in workflow.get("nodes", []):
        if node.get("id") == node_id:
            # Check each server tool's input schema
            for server_tool in node.get("server_tools", []):
                # Look for the field in predefined_fields
                for field in server_tool.get("input_schema", {}).get(
                    "predefined_fields", []
                ):
                    if field.get("field_name") == field_name:
                        # Check if the field type is object
                        field_data_type = field.get("data_type", {})
                        if isinstance(field_data_type, dict):
                            field_type = field_data_type.get("type", "")
                        else:
                            field_type = str(field_data_type)

                        # If it's an object type, try to parse the JSON
                        if field_type == "object":
                            try:
                                parsed_value = json.loads(value)
                                print(
                                    f"[DUAL_PURPOSE_INIT_FIX] Parsed JSON for field {field_name}: {value} -> {parsed_value}"
                                )
                                return parsed_value
                            except (json.JSONDecodeError, ValueError) as e:
                                print(
                                    f"[DUAL_PURPOSE_INIT_FIX] Failed to parse JSON for field {field_name}: {e}"
                                )
                                # Return original value if parsing fails
                                return value

    # Return original value if field not found or not object type
    return value


def initialize_workflow_with_params(workflow, params):
    """
    Updates the workflow structure by replacing field values based on user-dependent fields and handling None values with proper validation.
    Also initializes global context definition values from the payload.

    Args:
        workflow (dict): The original workflow dictionary.
        params (dict): The parameters containing user-dependent fields, values, and global context definitions.

    Returns:
        dict: The updated workflow with parameter values and global context values inserted.

    Raises:
        ValueError: If a required field is None and the user hasn't provided a value
                   or if a required global context variable is None and not provided in payload
    """

    user_dependent_fields = params["payload"]["user_dependent_fields"]
    user_payload_template = params["payload"]["user_payload_template"]
    global_context_values = user_payload_template.get("global_context_defs", {})

    # Create a mapping of transition IDs to transitions for quick lookup
    transitions_by_id = {
        transition.get("id"): transition
        for transition in workflow.get("transitions", [])
    }

    # Process tool parameters
    for transition in workflow.get("transitions", []):
        transition_id = transition.get("id")
        # Handle tool parameters
        for tool in transition.get("node_info", {}).get("tools_to_use", []):
            for item in tool.get("tool_params", {}).get("items", []):
                field_name = item.get("field_name")
                field_value = item.get("field_value")

                # Check if this field has a value with a specific transition ID
                field_updated = False

                # Check for field name match
                if field_name in user_payload_template:
                    payload_value = user_payload_template[field_name]

                    # Check if the payload value is a dictionary with transition_id
                    if (
                        isinstance(payload_value, dict)
                        and "value" in payload_value
                        and "transition_id" in payload_value
                    ):
                        # If this is the specified transition, update the field
                        if payload_value["transition_id"] == transition_id:
                            extracted_value = payload_value["value"]

                            # Check if this is a JSON string that should be parsed for object fields
                            parsed_value = _parse_json_if_needed(
                                extracted_value, field_name, transition, workflow
                            )

                            item["field_value"] = parsed_value
                            field_updated = True
                            print(
                                f"[DEBUG] Applied transition-specific value for field '{field_name}' to transition '{transition_id}'"
                            )
                        else:
                            print(
                                f"[DEBUG] Skipping field '{field_name}' for transition '{transition_id}' (intended for '{payload_value['transition_id']}')"
                            )
                        # If transition_id doesn't match, don't apply this value to current transition
                        # The value is intended for a different transition, so skip it
                    else:
                        # For simple values (not transition-specific), update as before
                        # Check if this is a JSON string that should be parsed for object fields
                        parsed_value = _parse_json_if_needed(
                            payload_value, field_name, transition, workflow
                        )

                        item["field_value"] = parsed_value
                        field_updated = True

                # Handle user-dependent fields that weren't updated by transition ID
                if not field_updated and field_name in user_dependent_fields:
                    print(
                        f"[DEBUG] Processing user-dependent field '{field_name}' for transition '{transition_id}'"
                    )
                    # Try to find a value for this field that might be in a different format
                    if field_name in user_payload_template:
                        payload_value = user_payload_template[field_name]

                        # If it's a dict with transition_id, only apply if transition doesn't exist
                        # (fallback case) or if it's a simple value (not transition-specific)
                        if (
                            isinstance(payload_value, dict)
                            and "value" in payload_value
                            and "transition_id" in payload_value
                        ):
                            # Check if the specified transition exists
                            target_transition_id = payload_value.get("transition_id")
                            print(
                                f"[DEBUG] Target transition for field '{field_name}': '{target_transition_id}'"
                            )
                            if target_transition_id not in transitions_by_id:
                                # If transition doesn't exist, use the value as fallback
                                print(
                                    f"[DEBUG] Target transition '{target_transition_id}' doesn't exist, using as fallback for '{transition_id}'"
                                )
                                item["field_value"] = payload_value["value"]
                                field_updated = True
                            else:
                                print(
                                    f"[DEBUG] Target transition '{target_transition_id}' exists, NOT applying to '{transition_id}'"
                                )
                            # If transition exists but doesn't match current transition, don't apply
                        else:
                            # For simple values (not transition-specific), apply to all matching fields
                            print(
                                f"[DEBUG] Simple value for field '{field_name}', applying to transition '{transition_id}'"
                            )
                            parsed_value = _parse_json_if_needed(
                                payload_value, field_name, transition, workflow
                            )
                            item["field_value"] = parsed_value
                            field_updated = True

                    # If still not updated and it's a required field, raise error
                    # Only raise error if we've checked all transitions and none have been updated
                    # We'll track this separately and check at the end of processing all transitions
                    if not field_updated and field_name not in user_payload_template:
                        raise ValueError(
                            f"Missing value for required user-dependent field: '{field_name}'"
                        )

                # Handle fields with None values that weren't updated yet
                elif not field_updated and field_value is None:
                    if field_name in user_payload_template:
                        payload_value = user_payload_template[field_name]

                        # If it's a dict with transition_id, only apply if transition doesn't exist
                        # (fallback case) or if it's a simple value (not transition-specific)
                        if (
                            isinstance(payload_value, dict)
                            and "value" in payload_value
                            and "transition_id" in payload_value
                        ):
                            # Check if the specified transition exists
                            target_transition_id = payload_value.get("transition_id")
                            if target_transition_id not in transitions_by_id:
                                # If transition doesn't exist, use the value as fallback
                                extracted_value = payload_value["value"]
                                parsed_value = _parse_json_if_needed(
                                    extracted_value, field_name, transition, workflow
                                )
                                item["field_value"] = parsed_value
                            # If transition exists but doesn't match current transition, don't apply
                        else:
                            # For simple values (not transition-specific), apply to all matching fields
                            parsed_value = _parse_json_if_needed(
                                payload_value, field_name, transition, workflow
                            )
                            item["field_value"] = parsed_value
                    else:
                        # Get the node_id to find the corresponding input schema
                        node_id = transition.get("node_info", {}).get("node_id")
                        # Check if the field is required in the input schema
                        is_required = False

                        # Find the node in the nodes array
                        for node in workflow.get("nodes", []):
                            if node.get("id") == node_id:
                                # Check each server tool's input schema
                                for server_tool in node.get("server_tools", []):
                                    # Look for the field in predefined_fields
                                    for field in server_tool.get(
                                        "input_schema", {}
                                    ).get("predefined_fields", []):
                                        if field.get(
                                            "field_name"
                                        ) == field_name and field.get(
                                            "required", False
                                        ):
                                            is_required = True
                                            break
                                    if is_required:
                                        break
                            if is_required:
                                break

                        # Only throw error if the field is required AND doesn't have a handle mapping
                        if is_required:
                            # Check if this field has a handle mapping that will resolve it later
                            has_handle_mapping = _has_handle_mapping_for_field(
                                field_name, transition, workflow
                            )

                            if not has_handle_mapping:
                                raise ValueError(
                                    f"Missing value for required field: '{field_name}'"
                                )

        conditional_routing = transition.get("conditional_routing", {})
        global_context_defs = conditional_routing.get("global_context_definitions", {})

        if "variables" in global_context_defs:
            for variable in global_context_defs["variables"]:
                var_name = variable.get("name")
                current_value = variable.get("value")

                if var_name in global_context_values:
                    # Check if the global context value is a dictionary with transition_id
                    gc_value = global_context_values[var_name]
                    if (
                        isinstance(gc_value, dict)
                        and "value" in gc_value
                        and "transition_id" in gc_value
                    ):
                        # If this is the specified transition, update the variable
                        if gc_value["transition_id"] == transition_id:
                            variable["value"] = gc_value["value"]
                        # If the specified transition doesn't exist, use as fallback
                        elif gc_value["transition_id"] not in transitions_by_id:
                            variable["value"] = gc_value["value"]
                    else:
                        # For simple values, update as before
                        variable["value"] = gc_value

                elif current_value is None:
                    raise ValueError(
                        f"Missing value for required global context variable: '{var_name}'"
                    )

    return workflow
