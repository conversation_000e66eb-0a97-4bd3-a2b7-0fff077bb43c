import uuid
from datetime import datetime, timezone
from sqlalchemy import <PERSON>umn, Foreign<PERSON>ey, String, DateTime, Enum, Integer, Float, func
from sqlalchemy.dialects.postgresql import ARRAY, JSON  # Ensure JSON is imported if not already
from sqlalchemy.orm import relationship, declarative_base
from app.utils.constants.constants import (
    EnvCredentialStatus,
    McpVisibility,
    McpStatus,
    McpOwnerType,
    DeploymentStatus,
)
from app.utils.constants.table_names import MCP_CONFIG_TABLE

Base = declarative_base()


class McpDeployment(Base):
    __tablename__ = "mcp_deployments-stdio"

    id = Column(String, primary_key=True, index=True)
    container_name = Column(String, nullable=True)
    status = Column(String, index=True)
    user_id = Column(String, index=True, nullable=True)
    mcp_id = Column(String, ForeignKey("mcp-configs.id"), nullable=False, index=True)
    image_name = Column(String, nullable=True)
    env_vars = Column(JSON, nullable=True)

    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationship to the McpConfig table (optional, but good for ORM features)
    mcp = relationship(
        "McpConfig", back_populates="deployments"
    )  # Assumes McpConfig has a 'deployments' relationship

    def __repr__(self):
        return f"<McpDeployment(id='{self.id}', mcp_id='{self.mcp_id}', status='{self.status}')>"


# New McpToolConfig class
class McpToolConfig(Base):
    __tablename__ = "mcp-tools-config"  # As requested

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()), index=True)

    # Foreign Key to link back to McpConfig
    mcp_config_id = Column(String, ForeignKey(f"{MCP_CONFIG_TABLE}.id"), nullable=False, index=True)

    # Tool-specific fields based on the provided JSON structure
    name = Column(String, nullable=False, index=True)  # e.g., "git_directory_structure"
    description = Column(String, nullable=True)
    input_schema = Column(JSON, nullable=True)  # The whole input_schema JSON object
    output_schema = Column(JSON, nullable=True)  # The whole output_schema JSON object

    # 'required' field extracted from input_schema for convenience, as requested
    # This will store the list of required parameter names from the input_schema
    required = Column(ARRAY(String), nullable=True)

    annotations = Column(JSON, nullable=True)  # Corresponds to the 'annotations' field in each tool

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    # Relationship to McpConfig
    mcp_config = relationship("McpConfig", back_populates="tools")

    def __repr__(self):
        return f"<McpToolConfig(id='{self.id}', name='{self.name}', mcp_config_id='{self.mcp_config_id}')>"


class McpConfig(Base):
    __tablename__ = MCP_CONFIG_TABLE

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Basic information
    name = Column(String(255), nullable=False)
    description = Column(String, nullable=True)

    image_name = Column(String, nullable=True)

    env_keys = Column(JSON, nullable=True)

    logo = Column(String, nullable=True)

    # --- REMOVE THIS FIELD ---
    # mcp_tools_config = Column(JSON, nullable=True)
    # --- THIS FIELD IS REMOVED ---

    # Access control
    visibility = Column(Enum(McpVisibility), nullable=False, default=McpVisibility.PRIVATE)
    owner_id = Column(String, nullable=False)
    owner_type = Column(Enum(McpOwnerType), nullable=False)

    user_ids = Column(ARRAY(String), nullable=True)

    organization_user_ids = Column(
        ARRAY(String), nullable=True
    )  # TODO : for organization access control

    use_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)

    # Classification
    category = Column(String, nullable=False)

    component_category = Column(String, nullable=True)
    tags = Column(ARRAY(String), nullable=True)
    status = Column(Enum(McpStatus), nullable=False, default=McpStatus.ACTIVE)

    config = Column(JSON, nullable=True)
    deployment_status = Column(Enum(DeploymentStatus), nullable=True)
    # Metadata
    git_url = Column(String, nullable=True)
    git_branch = Column(String, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    deployments = relationship("McpDeployment", back_populates="mcp", cascade="all, delete-orphan")

    tools = relationship("McpToolConfig", back_populates="mcp_config", cascade="all, delete-orphan")

    user_assignments = relationship(
        "UserMcpAssignment", back_populates="mcp_config", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<McpConfig(id={self.id}, name='{self.name}')>"


class UserMcpAssignment(Base):
    __tablename__ = "user_mcp_assignments-stdio"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False)
    mcp_id = Column(ForeignKey(f"{MCP_CONFIG_TABLE}.id"), nullable=False)

    env_variables = Column(String, nullable=True)

    env_credential_status = Column(
        Enum(
            EnvCredentialStatus,
            name="env_credential_status_enum",
            native_enum=False,
            create_constraint=True,
        ),
        nullable=False,
        default=EnvCredentialStatus.PENDING_INPUT,
        index=True,
    )
    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    mcp_config = relationship("McpConfig", back_populates="user_assignments")

    def __repr__(self):
        return (
            f"<UserMcpAssignment(id={self.id}, user_id='{self.user_id}', mcp_id='{self.mcp_id}')>"
        )
