# app/services/mcp_config_service.py
import asyncio
from datetime import datetime, timezone
import shlex
import uuid
import grpc
import json
import structlog
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, or_
from app.db.session import SessionLocal
from app.models.mcp_schema import McpConfig, McpDeployment, UserMcpAssignment
from app.models.mcp_rating import McpRating
from app.grpc import mcp_pb2, mcp_pb2_grpc
from app.utils.constants.constants import (
    DeploymentStatus,
    EnvCredentialStatus,
    McpStatus,
    McpVisibility,
    McpCategory,
    UrlType,
)
from app.utils.kafka.kafka_service import KafkaProducer

from app.utils.MCP.fetch_tools import get_mcp_tools, tools_to_json_response

from app.utils.google_pubsub import publish_deployment_message

from app.core.config import settings


from app.utils.secret_manager.secret_manager import EncryptionManager
from app.utils.stdio_deployment import SSHDockerService

from app.utils.MCP.fetch_tools_stdio import SSHMCPClient

secret_manager = EncryptionManager()
logger = structlog.get_logger(__name__)


class ContainerFunctionService(mcp_pb2_grpc.MCPServiceServicer):
    def __init__(self):
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def CreateContainer(
        self, request: mcp_pb2.CreateContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.CreateContainerResponse:
        db = self.get_db()
        logger.info(
            f"gRPC CreateContainer request for MCP ID: {request.mcp_id}, User ID: {request.user_id}, Type: {request.type}"
        )

        if request.type.lower() != "stdio":
            message = f"Unsupported deployment type: {request.type}. Only 'stdio' is supported by this endpoint."
            logger.warning(message)
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(message)
            return mcp_pb2.CreateContainerResponse(success=False, message=message)

        db_deployment = None

        try:
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp_config:
                message = f"MCP with ID {request.mcp_id} not found."
                logger.warning(message)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return mcp_pb2.CreateContainerResponse(success=False, message=message)

            if not mcp_config.image_name:
                message = f"MCP with ID {request.mcp_id} does not have an image_name configured."
                logger.warning(message)
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(message)
                return mcp_pb2.CreateContainerResponse(success=False, message=message)

            image_name_to_use = mcp_config.image_name
            deployment_id = str(uuid.uuid4())
            docker_container_name = f"{request.mcp_id}_{request.user_id}"

            required_env_keys = []
            if mcp_config.env_keys:
                required_env_keys = [item["key"] for item in mcp_config.env_keys]

            assignment = (
                db.query(UserMcpAssignment)
                .filter_by(user_id=request.user_id, mcp_id=request.mcp_id)
                .first()
            )

            if request.env_vars:
                # Save/update UserMcpAssignment with provided env_vars
                env_dict = {kv.key: kv.value for kv in request.env_vars}
                env_json_string = json.dumps(env_dict)

                try:
                    encrypted_blob = secret_manager.encrypt(env_json_string, request.user_id)
                except ValueError as encryption_error:
                    if "No encryption key found" in str(encryption_error):
                        logger.warning(
                            f"No encryption key for user {request.user_id}, creating one..."
                        )
                        secret_id = secret_manager._get_secret_name(request.user_id)
                        secret_manager.create_and_store_user_key(secret_id)
                        encrypted_blob = secret_manager.encrypt(env_json_string, request.user_id)
                    else:
                        raise encryption_error

                if not assignment:
                    assignment = UserMcpAssignment(
                        user_id=request.user_id,
                        mcp_id=request.mcp_id,
                        env_variables=encrypted_blob,
                        env_credential_status=EnvCredentialStatus.PROVIDED.value,
                    )
                    db.add(assignment)
                else:
                    logger.info(f"updating existing environment variables")
                    assignment.env_variables = encrypted_blob
                    assignment.env_credential_status = EnvCredentialStatus.PROVIDED.value

                db.commit()

                env_vars_for_db = [{"key": k, "value": v} for k, v in env_dict.items()]
                env_vars_for_docker_list = [f"{k}={v}" for k, v in env_dict.items()]

            else:
                # request.env_vars is empty
                logger.debug(f"request.env_vars is empty. Checking required_env_keys: {required_env_keys}")
                if not required_env_keys:
                    # No env keys required by MCP config and none provided in request
                    logger.info(f"No environment variables required by MCP {request.mcp_id} and none provided in request. Proceeding without env vars.")
                    env_vars_for_db = []
                    env_vars_for_docker_list = []
                else:
                    # Env keys are required by MCP config, but none provided in request.
                    # Fallback: fetch and decrypt env vars from UserMcpAssignment
                    if not assignment or not assignment.env_variables:
                        message = "Environment variables required by MCP config but not found in request or user assignment."
                        logger.warning(message)
                        context.set_code(grpc.StatusCode.NOT_FOUND)
                        context.set_details(message)
                        return mcp_pb2.CreateContainerResponse(success=False, message=message)

                    try:
                        decrypted_json = secret_manager.decrypt(
                            cipher_text=assignment.env_variables, user_id=request.user_id
                        )
                        decrypted_json = json.loads(decrypted_json)

                        filtered_env = {
                            k: v for k, v in decrypted_json.items() if k in required_env_keys
                        }

                        env_vars_for_db = [{"key": k, "value": v} for k, v in filtered_env.items()]
                        env_vars_for_docker_list = [f"{k}={v}" for k, v in filtered_env.items()]

                    except Exception as decryption_error:
                        error_message = (
                            f"Failed to decrypt env vars for user assignment: {decryption_error}"
                        )
                        logger.error(error_message, exc_info=True)
                        context.set_code(grpc.StatusCode.INTERNAL)
                        context.set_details("Failed to retrieve environment variables.")
                        return mcp_pb2.CreateContainerResponse(
                            success=False,
                            message="Failed to retrieve environment variables from assignment.",
                        )

            db_deployment = McpDeployment(
                id=deployment_id,
                mcp_id=request.mcp_id,
                user_id=request.user_id,
                container_name=docker_container_name,
                image_name=image_name_to_use,
                env_vars=env_vars_for_db,
                status="STARTING",
            )
            db.add(db_deployment)
            db.commit()
            db.refresh(db_deployment)
            logger.info(f"McpDeployment record {deployment_id} created with status 'STARTING'.")

            actual_docker_container_id = None
            try:
                ssh_config_dict = {
                    "ssh_host": settings.DEFAULT_SSH_HOST,
                    "ssh_user": settings.DEFAULT_SSH_USER,
                    "ssh_key_content": settings.DEFAULT_SSH_KEY_CONTENT,
                    "container_name": docker_container_name,
                }

                async def _perform_create():
                    async with SSHDockerService(ssh_config_dict) as ssh_service:
                        logger.info(
                            f"Attempting to create Docker container '{ssh_service.container_name}' "
                            f"with image '{image_name_to_use}'."
                        )

                        container_options_str = None
                        if env_vars_for_docker_list:
                            env_option_parts = [
                                f"-e {shlex.quote(env_str)}" for env_str in env_vars_for_docker_list
                            ]
                            container_options_str = " ".join(env_option_parts)
                            logger.debug(f"Formatted container options: {container_options_str}")

                        return await ssh_service.create_container(
                            image_tag=image_name_to_use, container_options=container_options_str
                        )

                actual_docker_container_id = asyncio.run(_perform_create())

                logger.info(
                    f"Docker container '{docker_container_name}' created successfully "
                    f"(Docker ID: {actual_docker_container_id})."
                )

                db_deployment.status = "RUNNING"
                db_deployment.updated_at = datetime.now(timezone.utc)
                db.commit()
                db.refresh(db_deployment)

                return mcp_pb2.CreateContainerResponse(
                    success=True,
                    message=f"Container '{docker_container_name}' created successfully.",
                    container_id=deployment_id,
                )

            except Exception as launch_exc:
                error_message = (
                    f"Failed to launch container '{docker_container_name}': {str(launch_exc)}"
                )
                logger.error(error_message, exc_info=True)
                if db_deployment:
                    db_deployment.status = "ERROR"
                    db_deployment.updated_at = datetime.now(timezone.utc)
                    db.commit()
                    db.refresh(db_deployment)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(error_message)
                return mcp_pb2.CreateContainerResponse(
                    success=False,
                    message=error_message,
                    container_id=docker_container_name,
                )

        except Exception as e:
            if "db" in locals() and db.is_active:
                db.rollback()
            error_message = f"Internal server error during container creation: {str(e)}"
            logger.error(error_message, exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal server error occurred.")
            return mcp_pb2.CreateContainerResponse(
                success=False, message="An internal server error occurred."
            )

        finally:
            if "db" in locals() and db:
                db.close()

    def StopContainer(
        self, request: mcp_pb2.StopContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.StopContainerResponse:
        db = self.get_db()
        docker_container_name_from_request = request.container_id
        user_id_from_request = request.user_id  # You might not use these if not filtering DB query
        mcp_id_from_request = request.mcp_id  # You might not use these

        logger.info(
            f"gRPC StopContainer request for Docker Container Name: {docker_container_name_from_request}, "
            f"User ID: {user_id_from_request}, MCP ID: {mcp_id_from_request}"
        )

        try:
            # Fetch McpDeployment record... (your existing logic)
            db_deployment = (
                db.query(McpDeployment)
                .filter(McpDeployment.container_name == docker_container_name_from_request)
                .first()
            )

            if not db_deployment:
                # ... (handle not found)
                message = f"No McpDeployment record found for Docker container '{docker_container_name_from_request}'"
                logger.warning(message)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return mcp_pb2.StopContainerResponse(success=False, message=message)

            # ... (check if already stopped, your existing logic) ...
            non_active_status = "STOPPED"
            if db_deployment.status == non_active_status:
                message = f"Container '{docker_container_name_from_request}' (Deployment DB ID: {db_deployment.id}) is already in DB status '{db_deployment.status}'."
                logger.info(message)
                return mcp_pb2.StopContainerResponse(success=True, message=message)

            force_stop = False  # Default
            stop_timeout = 30  # Default

            ssh_config_dict = {
                "ssh_host": settings.DEFAULT_SSH_HOST,
                "ssh_user": settings.DEFAULT_SSH_USER,
                "ssh_key_content": settings.DEFAULT_SSH_KEY_CONTENT,
                "container_name": docker_container_name_from_request,
            }

            try:
                # Use an inner async function to manage the context
                async def _perform_stop():
                    async with SSHDockerService(ssh_config_dict) as ssh_service:
                        logger.info(
                            f"Attempting to stop Docker container '{ssh_service.container_name}' "
                            f"(Deployment DB ID: {db_deployment.id}). Force: {force_stop}, Timeout: {stop_timeout}s"
                        )
                        return await ssh_service.stop_container(
                            force=force_stop, timeout=stop_timeout
                        )

                stopped_successfully = asyncio.run(_perform_stop())

                current_time = datetime.now(timezone.utc)
                if stopped_successfully:
                    db_deployment.status = "STOPPED"
                    # ... (update db, commit, refresh, success response) ...
                    message = (
                        f"Container '{docker_container_name_from_request}' (Deployment DB ID: {db_deployment.id}) "
                        f"stopped successfully. DB status updated to STOPPED."
                    )
                else:
                    # Container was not running on host
                    db_deployment.status = "STOPPED"
                    # ... (update db, commit, refresh, success response) ...
                    message = (
                        f"Container '{docker_container_name_from_request}' (Deployment DB ID: {db_deployment.id}) "
                        f"was not running on the host. DB status has been updated to STOPPED."
                    )

                db_deployment.updated_at = current_time
                db.commit()
                db.refresh(db_deployment)
                logger.info(message)
                return mcp_pb2.StopContainerResponse(success=True, message=message)

            except RuntimeError as runtime_exc:
                # ... (handle runtime_exc, update DB to ERROR, response) ...
                error_message = (
                    f"Failed to stop container '{docker_container_name_from_request}' "
                    f"(Deployment DB ID: {db_deployment.id}): {str(runtime_exc)}"
                )
                logger.error(error_message, exc_info=True)
                db.commit()
                db.refresh(db_deployment)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(error_message)
                return mcp_pb2.StopContainerResponse(success=False, message=error_message)

        except Exception as e:
            # ... (general exception handling, db.rollback, response) ...
            if "db" in locals() and db.is_active:
                db.rollback()
            error_message = f"Internal server error during StopContainer for Docker container '{request.container_id if request else 'unknown'}': {str(e)}"
            logger.error(error_message, exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal server error occurred.")
            return mcp_pb2.StopContainerResponse(
                success=False, message="An internal server error occurred."
            )
        finally:
            if "db" in locals() and db:
                db.close()

    def DeleteContainer(
        self, request: mcp_pb2.DeleteContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.DeleteContainerResponse:
        db = self.get_db()
        docker_container_name_from_request = request.container_id

        logger.info(
            f"gRPC DeleteContainer request for Docker Container Name: {docker_container_name_from_request}, "
        )

        try:
            db_deployment = (
                db.query(McpDeployment)
                .filter(McpDeployment.container_name == docker_container_name_from_request)
                .first()
            )
            deployment_id_for_logging = db_deployment.id if db_deployment else "N/A"

            if not db_deployment:
                message = f"No McpDeployment record found for Docker container '{docker_container_name_from_request}'"
                logger.warning(message)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return mcp_pb2.DeleteContainerResponse(success=False, message=message)

            ssh_config_dict = {
                "ssh_host": settings.DEFAULT_SSH_HOST,
                "ssh_user": settings.DEFAULT_SSH_USER,
                "ssh_key_content": settings.DEFAULT_SSH_KEY_CONTENT,
                "container_name": docker_container_name_from_request,
            }

            stop_message = ""
            try:
                # Inner async function for stopping
                async def _perform_stop():
                    async with SSHDockerService(ssh_config_dict) as ssh_service:
                        logger.info(
                            f"Attempting to stop Docker container '{ssh_service.container_name}' "
                            f"(Deployment DB ID: {deployment_id_for_logging})"
                        )
                        return await ssh_service.stop_container(force=False, timeout=30)

                stopped_successfully = asyncio.run(_perform_stop())

                current_time = datetime.now(timezone.utc)
                db_deployment.status = "STOPPED"
                db_deployment.updated_at = current_time
                db.commit()
                db.refresh(db_deployment)

                stop_message = (
                    f"Container '{docker_container_name_from_request}' stopped successfully. "
                    f"Deployment DB status updated to STOPPED."
                    if stopped_successfully
                    else f"Container '{docker_container_name_from_request}' was already stopped or not running. "
                    f"Deployment DB status still updated to STOPPED."
                )
                logger.info(stop_message)

            except RuntimeError as stop_error:
                stop_error_msg = (
                    f"Failed to stop container '{docker_container_name_from_request}' before deletion "
                    f"(Deployment DB ID: {deployment_id_for_logging}): {str(stop_error)}"
                )
                logger.error(stop_error_msg, exc_info=True)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(stop_error_msg)
                return mcp_pb2.DeleteContainerResponse(success=False, message=stop_error_msg)

            # Proceed with deletion
            try:

                async def _perform_delete():
                    async with SSHDockerService(ssh_config_dict) as ssh_service:
                        logger.info(
                            f"Attempting to delete Docker container '{ssh_service.container_name}' "
                            f"(Deployment DB ID: {deployment_id_for_logging})"
                        )
                        return await ssh_service.delete_container(force=False)

                deleted_successfully = asyncio.run(_perform_delete())

                current_time = datetime.now(timezone.utc)
                db_deployment.status = "DELETED"
                db_deployment.updated_at = current_time
                db.commit()
                db.refresh(db_deployment)

                delete_msg = (
                    f"Container '{docker_container_name_from_request}' deleted from host. "
                    f"Deployment DB status updated to DELETED."
                    if deleted_successfully
                    else f"Container '{docker_container_name_from_request}' not found on host. "
                    f"Deployment DB status still updated to DELETED."
                )
                logger.info(delete_msg)
                final_message = f"{stop_message} {delete_msg}"
                return mcp_pb2.DeleteContainerResponse(success=True, message=final_message)

            except RuntimeError as delete_error:
                delete_error_msg = (
                    f"Failed to delete container '{docker_container_name_from_request}' "
                    f"(Deployment DB ID: {deployment_id_for_logging}): {str(delete_error)}"
                )
                logger.error(delete_error_msg, exc_info=True)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(delete_error_msg)
                return mcp_pb2.DeleteContainerResponse(success=False, message=delete_error_msg)

        except Exception as e:
            if "db" in locals() and db.is_active:
                db.rollback()
            error_message = f"Internal server error during DeleteContainer for Docker container '{request.container_id if request else 'unknown'}': {str(e)}"
            logger.error(error_message, exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal server error occurred.")
            return mcp_pb2.DeleteContainerResponse(
                success=False, message="An internal server error occurred."
            )
        finally:
            if "db" in locals() and db:
                db.close()
