# kafka_client.py
import logging
from typing import Optional
from kafka import <PERSON>f<PERSON>Producer, KafkaAdminClient
from app.core.config import settings
import json

class KafkaClient:
    """
    Simple helper class for Kafka connection management.
    Provides methods to establish and close connections.
    """

    def __init__(self):
        """
        Initialize Kafka client with connection parameters from settings.
        """
        self.bootstrap_servers = settings.BOOTSTRAP_SERVERS
        self._producer: Optional[KafkaProducer] = None
        self._admin_client: Optional[KafkaAdminClient] = None
        self.logger = logging.getLogger(__name__)

    def connect_producer(self) -> KafkaProducer:
        """
        Establish a Kafka producer connection.

        Returns:
            KafkaProducer: Kafka producer instance
        """
        if self._producer is None:
            self._producer = KafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
                key_serializer=lambda k: str(k).encode("utf-8"),
                acks="all",  # Wait for all replicas
                retries=3,  # Retry on failure
                linger_ms=5,  # Small batching delay
            )
        return self._producer

    def close(self) -> None:
        """Close the Kafka producer and admin client if they exist."""
        if self._producer is not None:
            self._producer.close()
            self._producer = None

        if self._admin_client is not None:
            self._admin_client.close()
            self._admin_client = None
