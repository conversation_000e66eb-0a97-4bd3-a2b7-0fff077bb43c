import asyncio
from typing import Any, Dict, List, Optional
import logging

from app.utils.MCP.mcp_client import MCPClient

DEFAULT_REQUEST_TIMEOUT_SECONDS = 5.0
DEFAULT_MAX_RETRIES = 2
DEFAULT_RETRY_DELAY_SECONDS = 3.0

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MCPToolRetrievalError(Exception):
    """Custom exception for when MCP tool retrieval fails after retries."""

    pass


async def _get_tools_async_with_retry(
    server_url: str, timeout_seconds: float, max_retries: int, retry_delay_seconds: float
) -> Optional[Any]:
    """
    Internal async function to connect to the MCP server and retrieve available tools,
    with timeout and retry mechanisms.

    Args:
        server_url: The URL of the MCP server.
        timeout_seconds: Timeout for each attempt.
        max_retries: Maximum number of retries.
        retry_delay_seconds: Delay between retries.

    Returns:
        List of available tools, or None if all attempts fail.
    """
    last_exception = None
    for attempt in range(max_retries + 1):  # +1 for the initial attempt
        try:
            logger.info(f"Attempt {attempt + 1}/{max_retries + 1} to get tools from {server_url}")

            # This inner function is what we'll apply the timeout to
            async def _fetch_operation():
                async with MCPClient(server_url) as client:
                    tools = await client.list_tools()
                    return tools

            # Apply timeout to the entire operation (connection + list_tools)
            tools_result = await asyncio.wait_for(_fetch_operation(), timeout=timeout_seconds)
            logger.info(f"Successfully retrieved tools from {server_url} on attempt {attempt + 1}")
            return tools_result

        except asyncio.TimeoutError:
            last_exception = asyncio.TimeoutError(
                f"Timeout after {timeout_seconds}s on attempt {attempt + 1} for {server_url}"
            )
            logger.warning(str(last_exception))
        except ValueError as ve:  # MCPClient raises ValueError for invalid URL scheme
            logger.error(f"Invalid server URL '{server_url}': {ve}. Not retrying.")
            # This is a configuration error, re-raise or return None immediately
            # For consistency with your gRPC service check `if not tools:`, returning None is simpler.
            return None  # Or raise MCPToolRetrievalError(f"Invalid server URL: {ve}")
        except Exception as e:  # Catch other potential errors (connection refused, DNS error, etc.)
            last_exception = e
            logger.error(
                f"Error getting tools from MCP server {server_url} on attempt {attempt + 1}: {e}"
            )

        if attempt < max_retries:
            logger.info(f"Retrying in {retry_delay_seconds} seconds...")
            await asyncio.sleep(retry_delay_seconds)
        else:
            logger.error(
                f"Failed to get tools from {server_url} after {max_retries + 1} attempts. "
                f"Last error: {last_exception}"
            )
            return None

    return None  # Should be unreachable if logic is correct, but as a fallback


def get_mcp_tools(
    server_url: str,
    timeout_seconds: float = DEFAULT_REQUEST_TIMEOUT_SECONDS,
    max_retries: int = DEFAULT_MAX_RETRIES,
    retry_delay_seconds: float = DEFAULT_RETRY_DELAY_SECONDS,
) -> Optional[Any]:
    """
    Connect to the MCP server and return the list of available tools.
    Implements timeout and retry mechanisms.

    Args:
        server_url: The URL of the MCP server (e.g., "http://localhost:8080/sse").
        timeout_seconds: Timeout for each connection/retrieval attempt.
        max_retries: Maximum number of retries.
        retry_delay_seconds: Delay between retries.

    Returns:
        List of available tools, or None if retrieval fails after all retries.
    """
    try:
        return asyncio.run(
            _get_tools_async_with_retry(
                server_url, timeout_seconds, max_retries, retry_delay_seconds
            )
        )
    except Exception as e:
        logger.error(f"Critical error in get_mcp_tools for {server_url}: {e}", exc_info=True)
        return None


def tools_to_json_response(tools_result: Any) -> Optional[dict]:
    """
    Convert MCP tool result into structured JSON with meta and nextCursor fields.

    Args:
        tools_result: The raw result returned from `get_mcp_tools(server_url)`,
                      typically an object with `meta`, `nextCursor`, and `tools` attributes.
                      Can be None if tool retrieval failed.

    Returns:
        A dictionary that matches the desired JSON structure, or None if input is None.
    """
    if tools_result is None:
        return None

    # If `tools_result` is a class with attributes
    return {
        "meta": getattr(tools_result, "meta", None),
        "nextCursor": getattr(tools_result, "nextCursor", None),
        "tools": [
            {
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.inputSchema,  # Assuming already dict-like
                "annotations": tool.annotations,
            }
            for tool in getattr(tools_result, "tools", [])
        ],
    }
    

def tools_to_json_response_stdio(tools_data: Any) -> Optional[Dict[str, Any]]:
    """
    Convert MCP tool data (either a list of Tool objects or an object
    containing tools, meta, and nextCursor) into a structured JSON dictionary.

    Args:
        tools_data: Can be:
                      1. A list of Tool objects.
                      2. An object with 'tools' (list of Tool objects),
                         'meta', and 'nextCursor' attributes.
                      3. None.

    Returns:
        A dictionary that matches the desired JSON structure (with meta,
        nextCursor, and tools list), or None if input tools_data is None.
    """
    if tools_data is None:
        return None

    actual_tools_list: List[Any] = [] # Should ideally be List[Tool] if type hints are strict
    meta_info: Any = None
    next_cursor_info: Any = None

    if isinstance(tools_data, list):
        # Case 1: Input is directly a list of Tool objects
        actual_tools_list = tools_data
        # meta and nextCursor are typically None if the input is just a list of tools
        # and these fields are not provided separately.
    elif hasattr(tools_data, "tools") and isinstance(getattr(tools_data, "tools", None), list):
        # Case 2: Input is an object with a .tools attribute (list),
        # and potentially .meta, .nextCursor
        actual_tools_list = getattr(tools_data, "tools")
        meta_info = getattr(tools_data, "meta", None)
        next_cursor_info = getattr(tools_data, "nextCursor", None)
    else:
        # If tools_data is neither a list of tools, nor an object containing a list
        # under a .tools attribute, we assume there are no processable tools.
        # This could also be a point to log a warning if an unexpected format is received.
        # For now, it will result in an empty "tools" list in the output.
        pass # actual_tools_list remains empty, meta_info/next_cursor_info remain None

    processed_tools_json_list = []
    for tool_obj in actual_tools_list:
        # Basic check to ensure tool_obj is somewhat structured like a Tool
        # In a more robust system, you might use isinstance(tool_obj, Tool)
        if not hasattr(tool_obj, 'name') or not hasattr(tool_obj, 'description') or not hasattr(tool_obj, 'inputSchema'):
            # Optionally log a warning for malformed tool objects
            # logger.warning(f"Skipping malformed tool object: {tool_obj}")
            continue

        processed_tools_json_list.append({
            "name": tool_obj.name,
            "description": tool_obj.description,
            "input_schema": tool_obj.inputSchema,  # Assuming inputSchema is already a dict
            "output_schema": getattr(tool_obj, 'outputSchema', None), # Add output_schema
            "annotations": getattr(tool_obj, 'annotations', None)
        })

    return {
        "meta": meta_info,
        "nextCursor": next_cursor_info,
        "tools": processed_tools_json_list,
    }
