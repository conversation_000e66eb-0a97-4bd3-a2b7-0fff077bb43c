from asyncio.log import logger
import base64
import os
import tempfile
from mcp import ClientSession, StdioServerParameters, stdio_client


class SSHMCPClient:
    """Client that connects to MCP server via SSH and Docker using MCP stdio SDK."""

    def __init__(
        self,
        ssh_host: str,
        ssh_user: str,
        ssh_key_content: str,
        container_name: str = "mcp-text-server",
    ):
        """
        Initialize SSH MCP client.

        Args:
            ssh_host: Remote server hostname or IP
            ssh_user: SSH username
            ssh_key_content: SSH private key content as string
            container_name: Docker container name (existing running container)
        """
        self.ssh_host = ssh_host
        self.ssh_user = ssh_user
        ssh_key_content_encrypted = ssh_key_content
        try:
            # Decrypt base64-encoded SSH key
            self.ssh_key_content = base64.b64decode(ssh_key_content_encrypted).decode("utf-8")
            logger.info("SSH key decrypted successfully")
        except Exception as e:
            # If decryption fails, assume it's already plain text
            self.ssh_key_content = ssh_key_content_encrypted
            logger.warning("SSH key decryption failed, using as plain text", error=str(e))
        self.container_name = container_name
        self.temp_key_file = None
        self.container_command = None
        print(f"SSH MCP Client initialized with host: {ssh_host}, user: {ssh_user}")

    def _create_temp_key_file(self):
        """Create temporary file with SSH key content."""
        if self.temp_key_file is None:
            # Create temporary file for SSH key
            temp_fd, temp_path = tempfile.mkstemp(prefix="ssh_key_", suffix=".pem")

            # Write key content to temporary file
            with os.fdopen(temp_fd, "w") as f:
                f.write(self.ssh_key_content)

            # Set proper permissions (600)
            os.chmod(temp_path, 0o600)

            self.temp_key_file = temp_path

        return self.temp_key_file

    def _cleanup_temp_key_file(self):
        """Clean up temporary SSH key file."""
        if self.temp_key_file and os.path.exists(self.temp_key_file):
            os.unlink(self.temp_key_file)
            self.temp_key_file = None

    async def _get_container_command(self):
        """Get the command that the container is running."""
        if self.container_command is not None:
            return self.container_command

        try:
            import subprocess

            key_file = self._create_temp_key_file()

            # Use docker inspect to get the container's command
            inspect_cmd = [
                "ssh",
                "-i", key_file,
                "-o", "StrictHostKeyChecking=no",
                "-o", "UserKnownHostsFile=/dev/null",
                "-o", "ConnectTimeout=30",
                "-o", "IdentitiesOnly=yes",
                f"{self.ssh_user}@{self.ssh_host}",
                f"docker inspect {self.container_name} --format='{{{{json .Config.Cmd}}}}'",
            ]

            result = subprocess.run(inspect_cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                import json

                cmd_json = result.stdout.strip().strip("'\"")
                if cmd_json and cmd_json != "null":
                    cmd_list = json.loads(cmd_json)
                    if cmd_list:
                        self.container_command = " ".join(cmd_list)
                        return self.container_command

            # Fallback: try to get entrypoint + cmd
            entrypoint_cmd = [
                "ssh",
                "-i", key_file,
                "-o", "StrictHostKeyChecking=no",
                "-o", "UserKnownHostsFile=/dev/null",
                "-o", "ConnectTimeout=30",
                "-o", "IdentitiesOnly=yes",
                f"{self.ssh_user}@{self.ssh_host}",
                f"docker inspect {self.container_name} --format='{{{{json .Config.Entrypoint}}}} {{{{json .Config.Cmd}}}}'",
            ]

            result = subprocess.run(entrypoint_cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                import json

                parts = result.stdout.strip().split(" ", 1)
                entrypoint_json = parts[0].strip("'\"")
                cmd_json = parts[1].strip("'\"") if len(parts) > 1 else "null"

                command_parts = []

                # Add entrypoint if it exists
                if entrypoint_json and entrypoint_json != "null":
                    entrypoint = json.loads(entrypoint_json)
                    if entrypoint:
                        command_parts.extend(entrypoint)

                # Add cmd if it exists
                if cmd_json and cmd_json != "null":
                    cmd = json.loads(cmd_json)
                    if cmd:
                        command_parts.extend(cmd)

                if command_parts:
                    self.container_command = " ".join(command_parts)
                    return self.container_command

            # Final fallback
            print("⚠️  Could not determine container command, using default")
            self.container_command = "python server.py"
            return self.container_command

        except Exception as e:
            print(f"⚠️  Error getting container command: {e}")
            print("   Using default command: python server.py")
            self.container_command = "python server.py"
            return self.container_command

    def _build_ssh_docker_command(self, container_command: str):
        """Build SSH command to execute command in existing Docker container."""
        print(f"[DEBUG] Building SSH Docker command for {container_command}...]")
        key_file = self._create_temp_key_file()
        return [
            "ssh",
            "-i", key_file,
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "ConnectTimeout=30",
            "-o", "IdentitiesOnly=yes",
            f"{self.ssh_user}@{self.ssh_host}",
            f"docker exec -i {self.container_name} {container_command}",
        ]

    async def fetch_tools(self):
        """Fetch tools list using MCP stdio SDK."""
        print(f"[DEBUG] Fetching tools from {self.ssh_host}...")
        try:
            # Get the container's command
            container_command = await self._get_container_command()

            # Build SSH + Docker command
            ssh_command = self._build_ssh_docker_command(container_command)

            print(f"🔗 Connecting to {self.ssh_user}@{self.ssh_host}")
            print(f"📦 Using container: {self.container_name}")
            print("🔧 Establishing MCP connection via SSH...")
            print("=" * 60)

            # Create server parameters for SSH + Docker
            server_params = StdioServerParameters(
                command=ssh_command[0],  # "ssh"
                args=ssh_command[1:],  # All SSH arguments and Docker command
                env=None,
            )

            # Connect to the remote containerized server using MCP stdio
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    # Initialize the MCP session
                    await session.initialize()
                    print("✅ MCP session initialized successfully!")

                    # List available tools using MCP SDK
                    tools_response = await session.list_tools()
                    tools = tools_response.tools

                    return tools

        except Exception as e:
            print(f"❌ Failed to connect to MCP server: {e}")
            print("💡 Troubleshooting tips:")
            print(f"   - Ensure SSH access to {self.ssh_user}@{self.ssh_host}")
            print(f"   - Verify Docker is installed on remote server")
            print(f"   - Check if container '{self.container_name}' exists")
            print(f"   - Verify SSH key content is valid")
            return []
        finally:
            # Clean up temporary key file
            self._cleanup_temp_key_file()
