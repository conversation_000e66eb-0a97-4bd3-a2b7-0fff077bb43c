#!/usr/bin/env python3
"""Script to publish test deployment messages to Pub/Sub."""

import base64
import json
import os
import sys
from pathlib import Path
import tempfile
from app.core.config import settings
from dotenv import load_dotenv
from google.cloud import pubsub_v1
from app.core.config import settings

# Load environment variables from .env file
load_dotenv()


def setup_google_credentials() -> None:
    """Setup Google Cloud credentials from environment variables."""
    credentials_base64 = settings.GOOGLE_APPLICATION_CREDENTIALS_BASE64
    if credentials_base64:
        try:
            # Decode base64 credentials
            credentials_json_str = base64.b64decode(credentials_base64).decode("utf-8")

            # Create temporary file with credentials
            with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as temp_file:
                temp_file.write(credentials_json_str)
                temp_credentials_path = temp_file.name

            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = temp_credentials_path
            print(f"🔑 Using base64 encoded credentials (temporary file created)")

        except Exception as e:
            print(f"❌ Error decoding base64 credentials: {e}")
            sys.exit(1)
    else:
        print("⚠️  No GOOGLE_APPLICATION_CREDENTIALS found in environment")
        print(
            "   Make sure to set this in your .env file or use 'gcloud auth application-default login'"
        )


def publish_deployment_message(
    topic_name: str,
    payload: dict,
    project_id=settings.GOOGLE_CLOUD_PROJECT_ID,
) -> None:
    """
    Publish a deployment message to Pub/Sub topic.

    Args:
        project_id: Google Cloud project ID
        topic_name: Pub/Sub topic name
        payload_file: Path to JSON payload file
    """
    setup_google_credentials()

    # Create publisher client
    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path(project_id, topic_name)

    # Convert payload to bytes
    message_data = json.dumps(payload).encode("utf-8")

    # Publish message
    try:
        print(f"Publishing message")
        future = publisher.publish(topic_path, message_data)
        message_id = future.result()

        print(f"✅ Published message {message_id} to {topic_path}")
        print(f"📦 Payload: {json.dumps(payload, indent=2)}")

    except Exception as e:
        print(f"❌ Failed to publish message: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Setup Google Cloud credentials
    setup_google_credentials()

    # Get values from environment variables or command line arguments
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT_ID")
    topic_name = None
    payload_file = "example-payload.json"

    # Parse command line arguments
    if len(sys.argv) >= 2:
        project_id = sys.argv[1]
    if len(sys.argv) >= 3:
        topic_name = sys.argv[2]
    if len(sys.argv) >= 4:
        payload_file = sys.argv[3]

    # If no topic name provided, try to derive from subscription name
    if not topic_name:
        subscription_name = os.getenv("PUBSUB_SUBSCRIPTION_NAME")
        if subscription_name:
            # Remove common subscription suffixes to get topic name
            topic_name = subscription_name.replace("-subscription", "").replace("_subscription", "")
        else:
            print("❌ Topic name not provided and cannot be derived from environment")
            print("Usage: python publish_test_message.py [project_id] [topic_name] [payload_file]")
            print("Or set GOOGLE_CLOUD_PROJECT_ID and PUBSUB_SUBSCRIPTION_NAME in .env")
            print(
                "Example: python publish_test_message.py my-project deployment-tasks example-payload.json"
            )
            sys.exit(1)

    if not project_id:
        print("❌ Project ID not found in environment or command line")
        print("Set GOOGLE_CLOUD_PROJECT_ID in .env or provide as first argument")
        sys.exit(1)

    print(f"📡 Publishing to project: {project_id}")
    print(f"📢 Topic: {topic_name}")
    print(f"📦 Payload file: {payload_file}")
    print()

    publish_deployment_message(project_id, topic_name, payload_file)
