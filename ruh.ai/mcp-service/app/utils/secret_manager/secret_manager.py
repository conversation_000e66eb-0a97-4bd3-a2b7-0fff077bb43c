import os
import base64
import tempfile
import logging
from app.core.config import settings
from cryptography.fernet import Fernet
from google.cloud import secretmanager
from google.api_core.exceptions import NotFound, AlreadyExists
from dotenv import load_dotenv
load_dotenv()

class EncryptionManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._load_credentials_from_base64()
        self.project_id = settings.GOOGLE_PROJECT_ID
        self.secret_client = secretmanager.SecretManagerServiceClient()

    def _load_credentials_from_base64(self):
        """
        Decodes the base64 service account key and sets GOOGLE_APPLICATION_CREDENTIALS.
        """
        creds_b64 = settings.GOOGLE_APPLICATION_CREDENTIALS
        
        if not creds_b64:
            raise EnvironmentError("GOOGLE_APPLICATION_CREDENTIALS is not set")

        decoded = base64.b64decode(creds_b64)

        # Save to a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        temp_file.write(decoded)
        temp_file.close()

        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = temp_file.name
        self.logger.info("Decoded GCP credentials and set GOOGLE_APPLICATION_CREDENTIALS")

    # Rest of your methods: create_and_store_user_key, encrypt, decrypt, etc.


    def _get_secret_name(self, user_id: str) -> str:
        return f"user-encryption-key-{user_id}"

    def create_and_store_user_key(self,secret_id: str) -> str:
        """
        Generates a Fernet key and stores it in GSM as a new secret.
        """
        parent = f"projects/{self.project_id}"
        key = Fernet.generate_key()  # Generate the key directly without re-encoding
        

        try:
            # Create the secret (if it doesn't already exist)
            self.secret_client.create_secret(
                request={
                    "parent": parent,
                    "secret_id": secret_id,
                    "secret": {"replication": {"automatic": {}}},
                }
            )
            self.logger.info(f"Secret {secret_id} created successfully.")
        except AlreadyExists:
            self.logger.warning(f"Secret {secret_id} already exists.")
        except Exception as e:
            self.logger.error(f"Failed to create secret {secret_id}: {e}")
            raise

        # Add key as a new version
        self.secret_client.add_secret_version(
            request={
                "parent": f"{parent}/secrets/{secret_id}",
                "payload": {"data": key},
            }
        )
        self.logger.info(f"Key added as a new version for secret {secret_id}.")
        print(f"[EncryptionManager] Key added as a new version for secret {secret_id}")
        return key.decode()

    def get_user_encryption_key(self, user_id: str) -> str:
        """
        Retrieves the latest encryption key for a user from GSM.
        """
        secret_id = self._get_secret_name(user_id)
        name = f"projects/{self.project_id}/secrets/{secret_id}/versions/latest"

        try:
            response = self.secret_client.access_secret_version(request={"name": name})
            self.logger.info(f"Retrieved encryption key for user {user_id}.")
            return response.payload.data.decode("UTF-8")
        except NotFound:
            self.logger.error(f"No encryption key found for user: {user_id}")
            raise ValueError(f"No encryption key found for user: {user_id}")

    def encrypt(self, plain_text: str, user_id: str) -> str:
        """
        Encrypts the input string using the user's Fernet key from GSM.
        """
        key = self.get_user_encryption_key(user_id)
        f = Fernet(key.encode())
        encrypted_text = f.encrypt(plain_text.encode()).decode()
        self.logger.info(f"Data encrypted for user {user_id}.")
        return encrypted_text

    def decrypt(self, cipher_text: str, user_id: str) -> str:
        """
        Decrypts the input string using the user's Fernet key from GSM.
        """
        key = self.get_user_encryption_key(user_id)
        f = Fernet(key.encode())
        decrypted_text = f.decrypt(cipher_text.encode()).decode()
        self.logger.info(f"Data decrypted for user {user_id}.")
        return decrypted_text
