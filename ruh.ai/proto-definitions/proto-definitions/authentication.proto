syntax = "proto3";

package authentication;

// OAuth Provider enumeration
enum OAuthProvider {
  OAUTH_PROVIDER_UNSPECIFIED = 0;
  OAUTH_PROVIDER_GOOGLE = 1;
  OAUTH_PROVIDER_MICROSOFT = 2;
  OAUTH_PROVIDER_GITHUB = 3;
  OAUTH_PROVIDER_CUSTOM = 4;
}

// OAuth Authorization Request
message OAuthAuthorizeRequest {
  string user_id = 1;
  string mcp_id = 2;
  string tool_name = 3;
  OAuthProvider provider = 4;
  repeated string scopes = 5;
  string redirect_uri = 6;
}

// OAuth Authorization Response
message OAuthAuthorizeResponse {
  bool success = 1;
  string message = 2;
  string authorization_url = 3;
  string state = 4;
}

// OAuth Callback Request
message OAuthCallbackRequest {
  string code = 1;
  string state = 2;
  string error = 3;
}

// OAuth Callback Response
message OAuthCallbackResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
  string mcp_id = 4;
  string tool_name = 5;
  string provider = 6;
}

// OAuth Credential Request
message OAuthCredentialRequest {
  string user_id = 1;
  string mcp_id = 2;
  string tool_name = 3;
  OAuthProvider provider = 4;
}

// OAuth Credential Response
message OAuthCredentialResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
  string mcp_id = 4;
  string tool_name = 5;
  string provider = 6;
  string access_token = 7;
  string refresh_token = 8;
  string token_type = 9;
  int32 expires_in = 10;
  string scope = 11;
}

// Server OAuth Credential Request (for server-to-server access)
message ServerOAuthCredentialRequest {
  string server_auth_key = 1;
  string user_id = 2;
  string mcp_id = 3;
  string tool_name = 4;
  OAuthProvider provider = 5;
}

// OAuth Providers List Request
message OAuthProvidersListRequest {
  // Empty request - returns all available providers
}

// OAuth Provider Info
message OAuthProviderInfo {
  OAuthProvider provider = 1;
  string name = 2;
  string display_name = 3;
  repeated string supported_tools = 4;
  bool is_configured = 5;
}

// OAuth Providers List Response
message OAuthProvidersListResponse {
  bool success = 1;
  string message = 2;
  repeated OAuthProviderInfo providers = 3;
}

// Tool Scopes Request
message OAuthToolScopesRequest {
  string tool_name = 1;
  OAuthProvider provider = 2;
}

// Tool Scopes Response
message OAuthToolScopesResponse {
  bool success = 1;
  string message = 2;
  string tool_name = 3;
  OAuthProvider provider = 4;
  repeated string scopes = 5;
  string description = 6;
}

// Delete Credential Request
message DeleteOAuthCredentialRequest {
  string user_id = 1;
  string mcp_id = 2;
  string tool_name = 3;
  OAuthProvider provider = 4;
}

// Delete Credential Response
message DeleteOAuthCredentialResponse {
  bool success = 1;
  string message = 2;
}

// OAuth Refresh Request
message OAuthRefreshRequest {
  string user_id = 1;
  string mcp_id = 2;
  string tool_name = 3;
  OAuthProvider provider = 4;
}

// OAuth Refresh Response
message OAuthRefreshResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
  string token_type = 4;
  int32 expires_in = 5;
  string scope = 6;
}

// Health Check Request
message HealthCheckRequest {
  // Empty request
}

// Health Check Response
message HealthCheckResponse {
  bool healthy = 1;
  string status = 2;
  string version = 3;
  map<string, string> dependencies = 4;
}

// Authentication Service
service AuthenticationService {
  // OAuth Flow Methods
  rpc InitiateOAuth(OAuthAuthorizeRequest) returns (OAuthAuthorizeResponse);
  rpc HandleOAuthCallback(OAuthCallbackRequest) returns (OAuthCallbackResponse);
  rpc RefreshOAuthTokens(OAuthRefreshRequest) returns (OAuthRefreshResponse);

  // Credential Management
  rpc GetOAuthCredentials(OAuthCredentialRequest) returns (OAuthCredentialResponse);
  rpc GetServerOAuthCredentials(ServerOAuthCredentialRequest) returns (OAuthCredentialResponse);
  rpc DeleteOAuthCredentials(DeleteOAuthCredentialRequest) returns (DeleteOAuthCredentialResponse);

  // Provider Information
  rpc ListOAuthProviders(OAuthProvidersListRequest) returns (OAuthProvidersListResponse);
  rpc GetToolScopes(OAuthToolScopesRequest) returns (OAuthToolScopesResponse);

  // Health Check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}
