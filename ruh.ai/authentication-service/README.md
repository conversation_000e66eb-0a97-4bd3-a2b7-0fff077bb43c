# Authentication Service

A dedicated microservice for handling OAuth authentication flows across multiple providers in the RUH.AI platform.

## 🎯 Overview

The Authentication Service centralizes all OAuth authentication logic, providing a unified interface for multiple OAuth providers (Google, Microsoft, GitHub, and custom providers) with configurable scope selection and secure credential management.

## 🏗️ Architecture

### Core Components

- **OAuth Provider Manager**: Multi-provider configuration and management
- **Generalized OAuth Service**: Provider-agnostic authentication flows
- **Credential Manager**: Secure storage using Google Secret Manager
- **State Manager**: Redis-based OAuth state management
- **Security Layer**: JWT validation and API key authentication

### External Dependencies

- **PostgreSQL**: Metadata and reference storage
- **Redis**: Temporary state management for OAuth flows
- **Google Secret Manager**: Secure credential storage
- **API Gateway**: Service routing and load balancing

## 🚀 Features

### Multi-Provider OAuth Support

- ✅ Google OAuth 2.0
- ✅ Microsoft OAuth 2.0 (Azure AD)
- ✅ GitHub OAuth
- ✅ Custom OAuth provider configuration
- ✅ Dynamic scope resolution per tool

### Security Features

- 🔒 Encrypted credential storage
- 🔒 JWT token validation
- 🔒 API key authentication for service-to-service calls
- 🔒 PKCE (Proof Key for Code Exchange) support
- 🔒 State parameter validation with cryptographic tokens
- 🔒 Rate limiting and DDoS protection

### Performance & Scalability

- ⚡ <200ms average response time
- ⚡ Horizontal scaling support
- ⚡ Connection pooling for database and Redis
- ⚡ Credential caching for frequently accessed tokens
- ⚡ Circuit breaker pattern for external dependencies

## 📋 Migration Status

This service is currently being migrated from the existing api-gateway OAuth implementation. See [TASKLIST.md](./TASKLIST.md) for detailed migration progress and [PRD.md](./PRD.md) for comprehensive requirements.

### Migration Progress

- [ ] **Phase 1**: Foundation & Infrastructure (Week 1-2)
- [ ] **Phase 2**: Core OAuth Functionality (Week 3-4)
- [ ] **Phase 3**: API Development & Integration (Week 5-6)
- [ ] **Phase 4**: Testing & Quality Assurance (Week 7)
- [ ] **Phase 5**: Deployment & Migration (Week 8)

## 🛠️ Development Setup

### Prerequisites

- Python 3.11+
- Poetry for dependency management
- PostgreSQL 13+
- Redis 6+
- Google Cloud account with Secret Manager enabled

### Installation

1. **Clone and setup the project**:

```bash
cd ruh.ai/authentication-service
poetry install
```

2. **Configure environment variables**:

```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Set up the database**:

```bash
# Create database
createdb authentication

# Run migrations
poetry run alembic upgrade head
```

4. **Start Redis**:

```bash
redis-server
```

5. **Run the service**:

```bash
poetry run python -m app.main
```

### Testing

Run the test suite using Poetry:

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=app --cov-report=term-missing

# Run specific test categories
poetry run pytest tests/test_oauth_service.py -v
poetry run pytest -m integration  # Integration tests
poetry run pytest -m security     # Security tests
```

## 📚 API Documentation

### OAuth Endpoints

#### List Available Providers

```http
GET /api/v1/oauth/providers
```

#### Get Tool Scope Requirements

```http
GET /api/v1/oauth/tools/{tool_name}/scopes
```

#### Initiate OAuth Flow

```http
POST /api/v1/oauth/authorize
Content-Type: application/json

{
  "provider": "google",
  "mcp_id": "mcp_123",
  "tool_name": "google_calendar",
  "scopes": ["custom", "scopes"]  // optional
}
```

#### OAuth Callback

```http
GET /api/v1/oauth/callback?code=...&state=...
```

#### Retrieve Credentials

```http
GET /api/v1/oauth/credentials?mcp_id=123&tool_name=google_calendar&provider=google
Authorization: Bearer {jwt_token}
```

#### Server Credential Access

```http
GET /api/v1/oauth/server/credentials?user_id=123&mcp_id=456&tool_name=google_calendar&provider=google
X-Server-Auth-Key: {server_auth_key}
```

## 🤝 Contributing

### Development Workflow

1. Follow Test-Driven Development (TDD)
2. Write tests first, then implement features
3. Maintain >90% test coverage
4. Use Poetry for dependency management
5. Follow the existing code structure and patterns

### Code Quality

- Black for code formatting
- isort for import sorting
- mypy for type checking
- pylint for code analysis
- pytest for testing

### Security Guidelines

- Never commit secrets or credentials
- Use environment variables for configuration
- Validate all inputs and sanitize outputs
- Follow OAuth 2.0 security best practices
- Implement proper error handling without information leakage

## 📄 License

This project is part of the RUH.AI platform and is proprietary software.

## 📞 Support

For questions or issues related to the Authentication Service:

- Create an issue in the project repository
- Contact the development team
- Refer to the [PRD.md](./PRD.md) for detailed requirements
- Check the [TASKLIST.md](./TASKLIST.md) for implementation progress
