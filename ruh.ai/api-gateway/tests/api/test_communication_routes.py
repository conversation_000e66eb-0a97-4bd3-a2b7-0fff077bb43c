import pytest
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient

class TestCommunicationRoutes:
    @pytest.fixture(autouse=True)
    def setup(self, test_client: TestClient, user_headers):
        self.client = test_client
        self.headers = user_headers

    @patch('app.services.communication_service.CommunicationServiceClient')
    def test_send_email_success(self, mock_comm_service):
        # Arrange
        mock_comm_service.return_value.send_email.return_value = Mock(
            success=True,
            message="Email sent successfully"
        )
        email_data = {
            "to": "<EMAIL>",
            "subject": "Test Email",
            "body": "This is a test email",
            "template_id": "test_template"
        }

        # Act
        response = self.client.post(
            "/api/v1/communications/email",
            json=email_data,
            headers=self.headers
        )

        # Assert
        assert response.status_code == 200
        assert response.json()["success"] is True

    @patch('app.services.communication_service.CommunicationServiceClient')
    def test_send_sms_success(self, mock_comm_service):
        mock_comm_service.return_value.send_sms.return_value = Mock(
            success=True,
            message="SMS sent successfully"
        )
        sms_data = {
            "phone_number": "+1234567890",
            "message": "Test SMS"
        }
        
        response = self.client.post(
            "/api/v1/communications/sms",
            json=sms_data,
            headers=self.headers
        )
        
        assert response.status_code == 200
        assert response.json()["success"] is True

    @patch('app.services.communication_service.CommunicationServiceClient')
    def test_add_message_success(self, mock_comm_service):
        mock_comm_service.return_value.add_message.return_value = {
            "id": "msg_123",
            "success": True
        }
        
        message_data = {
            "conversationId": "conv_123",
            "senderId": "user_123",
            "senderType": 1,
            "messageType": 1,
            "mediaType": 0,
            "mediaUrl": "",
            "content": "Test message",
            "workflowId": "workflow_123"
        }
        
        response = self.client.post(
            "/api/v1/communications/messages",
            json=message_data,
            headers=self.headers
        )
        
        assert response.status_code == 200
        assert "id" in response.json()
