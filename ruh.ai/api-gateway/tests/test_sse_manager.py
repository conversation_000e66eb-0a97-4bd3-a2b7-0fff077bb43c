import pytest
from app.helper.sse_manager import SseManager
import queue
import json


class TestSseManager:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.sse_manager = SseManager()
        self.client_id = "test_client"
        self.client_queue = queue.Queue()

    def test_add_client(self):
        self.sse_manager.add_client(self.client_id, self.client_queue)
        assert self.client_id in self.sse_manager.clients

    def test_remove_client(self):
        self.sse_manager.add_client(self.client_id, self.client_queue)
        self.sse_manager.remove_client(self.client_id)
        assert self.client_id not in self.sse_manager.clients

    def test_send_update_to_specific_client(self):
        self.sse_manager.add_client(self.client_id, self.client_queue)
        test_data = {"message": "test"}
        self.sse_manager.send_update("test_event", test_data, self.client_id)

        event = self.client_queue.get_nowait()
        assert event["event"] == "test_event"
        assert event["data"] == test_data

    def test_broadcast_update(self):
        client_id2 = "test_client2"
        client_queue2 = queue.Queue()

        self.sse_manager.add_client(self.client_id, self.client_queue)
        self.sse_manager.add_client(client_id2, client_queue2)

        test_data = {"message": "broadcast"}
        self.sse_manager.send_update("broadcast_event", test_data)

        for q in [self.client_queue, client_queue2]:
            event = q.get_nowait()
            assert event["event"] == "broadcast_event"
            assert event["data"] == test_data
