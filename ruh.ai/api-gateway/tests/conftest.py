import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import Mock, patch
from app.main import app
from app.core.config import settings
from app.services.kafka_service import KafkaService

# Create a mock KafkaService for testing
class MockKafkaService(KafkaService):
    async def initialize(self):
        self.producer = Mock()
        self.consumer = Mock()
        self.initialized = True
        self.init_lock = asyncio.Lock()
        self.sse_manager = Mo<PERSON>()
        self._initialized = True

    async def _start_services(self):
        pass

    async def stop_services(self):
        pass

@pytest.fixture(autouse=True)
async def mock_kafka_service():
    with patch('app.services.kafka_service.kafka_service', MockKafkaService()):
        yield

@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.fixture
def mock_user_data():
    return {
        "email": "<EMAIL>",
        "password": "Test123!",
        "full_name": "Test User"
    }


@pytest.fixture
def user_token():
    return "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0X3VzZXIiLCJleHAiOjE3MDg2NTY0MDB9"


@pytest.fixture
def user_headers(user_token):
    return {"Authorization": f"Bearer {user_token}"}


@pytest.fixture
def mock_grpc_stub():
    return Mock()


