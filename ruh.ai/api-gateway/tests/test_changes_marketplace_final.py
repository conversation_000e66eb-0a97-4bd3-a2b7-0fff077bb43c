"""
Final test to verify is_changes_marketplace functionality works correctly.
"""

import sys
import os
from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

# Add the current directory to the path so we can import the app modules
sys.path.append(os.getcwd())

from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum


def test_change_detection():
    """Test the _detect_workflow_changes method"""
    
    workflow_service = WorkflowFunctions()
    
    # Test 1: Timestamp-based change detection
    source_workflow = Workflow(
        id="source-id",
        description="Updated description",
        updated_at=datetime(2024, 1, 2, 12, 0, 0, tzinfo=timezone.utc)
    )
    
    derived_workflow = Workflow(
        id="derived-id", 
        description="Original description",
        updated_at=datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
    )
    
    # Should detect changes due to timestamp difference
    has_changes = workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
    assert has_changes == True
    print("✅ Change detection correctly identifies timestamp differences")
    
    # Test 2: Content-based change detection
    source_workflow.updated_at = datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)  # Same timestamp
    source_workflow.description = "Different description"  # But different content
    
    has_changes = workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
    assert has_changes == True
    print("✅ Change detection correctly identifies content differences")
    
    # Test 3: No changes scenario
    source_workflow.description = "Original description"  # Same content
    
    has_changes = workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
    assert has_changes == False
    print("✅ Change detection correctly identifies no changes")


def test_content_hash_generation():
    """Test the _generate_content_hash method"""
    
    workflow_service = WorkflowFunctions()
    
    # Create two identical workflows
    workflow1 = Workflow(
        id="workflow-1",
        description="Test description",
        workflow_url="http://example.com/workflow",
        builder_url="http://example.com/builder",
        start_nodes=["node1", "node2"],
        category=None,
        tags=["tag1", "tag2"]
    )
    
    workflow2 = Workflow(
        id="workflow-2",  # Different ID
        description="Test description",  # Same content
        workflow_url="http://example.com/workflow",
        builder_url="http://example.com/builder",
        start_nodes=["node1", "node2"],
        category=None,
        tags=["tag1", "tag2"]
    )
    
    # Generate hashes
    hash1 = workflow_service._generate_content_hash(workflow1)
    hash2 = workflow_service._generate_content_hash(workflow2)
    
    # Should be the same since content is identical
    assert hash1 == hash2
    print("✅ Content hash generation works correctly for identical content")
    
    # Change content and verify hash changes
    workflow2.description = "Different description"
    hash3 = workflow_service._generate_content_hash(workflow2)
    
    assert hash1 != hash3
    print("✅ Content hash changes when content changes")


def test_basic_service_methods():
    """Test that the new service methods exist and can be called"""
    
    workflow_service = WorkflowFunctions()
    
    # Test that methods exist
    assert hasattr(workflow_service, 'pullUpdatesFromSource')
    assert hasattr(workflow_service, 'checkForUpdates')
    assert hasattr(workflow_service, '_detect_workflow_changes')
    assert hasattr(workflow_service, '_generate_content_hash')
    
    print("✅ All new service methods exist")
    
    # Test that protobuf messages exist
    assert hasattr(workflow_pb2, 'PullUpdatesFromSourceRequest')
    assert hasattr(workflow_pb2, 'PullUpdatesFromSourceResponse')
    assert hasattr(workflow_pb2, 'CheckForUpdatesRequest')
    assert hasattr(workflow_pb2, 'CheckForUpdatesResponse')
    
    print("✅ All new protobuf messages exist")


if __name__ == "__main__":
    print("Testing is_changes_marketplace functionality...")
    test_change_detection()
    test_content_hash_generation()
    test_basic_service_methods()
    print("🎉 All tests passed!")
