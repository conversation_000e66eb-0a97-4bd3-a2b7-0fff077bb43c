import grpc
from typing import Optional
from app.core.config import settings
from app.grpc_ import admin_pb2, admin_pb2_grpc


class AdminServiceClient:

    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.ADMIN_SERVICE_HOST}:{settings.ADMIN_SERVICE_PORT}"
        )
        self.stub = admin_pb2_grpc.AdminServiceStub(self.channel)

    async def login(self, email: str, password: str):
        request = admin_pb2.LoginRequest(email=email, password=password)
        try:
            response = self.stub.login(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def access_token(self, refresh_token: str):
        request = admin_pb2.AccessTokenRequest(refreshToken=refresh_token)
        try:
            response = self.stub.accessToken(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_admin(
        self, email: str, password: str, full_name: str, role_ids: Optional[list[str]] = None
    ):
        request = admin_pb2.CreateAdminRequest(
            email=email, password=password, fullName=full_name, roleIds=role_ids or []
        )
        try:
            response = self.stub.createAdmin(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_admin(self, admin_id: str):
        request = admin_pb2.GetAdminRequest(adminId=admin_id)
        try:
            response = self.stub.getAdmin(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_admin(
        self,
        admin_id: str,
        full_name: Optional[str] = None,
        email: Optional[str] = None,
        password: Optional[str] = None,
        role_ids: Optional[list[str]] = None,
    ):
        request = admin_pb2.UpdateAdminRequest(
            adminId=admin_id,
            fullName=full_name,
            email=email,
            password=password,
            roleIds=role_ids or [],
        )
        try:
            response = self.stub.updateAdmin(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_admin(self, admin_id: str):
        request = admin_pb2.DeleteAdminRequest(adminId=admin_id)
        try:
            response = self.stub.deleteAdmin(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_admins(self, page: int = 1, page_size: int = 10):
        request = admin_pb2.ListAdminsRequest(page=page, pageSize=page_size)
        try:
            response = self.stub.listAdmins(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_role(self, name: str, description: str, permissions: list[str]):
        request = admin_pb2.CreateRoleRequest(
            name=name, description=description, permissions=permissions
        )
        try:
            response = self.stub.createRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_role(self, role_id: str):
        request = admin_pb2.GetRoleRequest(roleId=role_id)
        try:
            response = self.stub.getRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_role(
        self,
        role_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[list[str]] = None,
    ):
        request = admin_pb2.UpdateRoleRequest(
            roleId=role_id, name=name, description=description, permissions=permissions or []
        )
        try:
            response = self.stub.updateRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_role(self, role_id: str):
        request = admin_pb2.DeleteRoleRequest(roleId=role_id)
        try:
            response = self.stub.deleteRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_roles(self, page: int = 1, page_size: int = 10):
        request = admin_pb2.ListRolesRequest(page=page, pageSize=page_size)
        try:
            response = self.stub.listRoles(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def assign_role(self, admin_id: str, role_ids: list[str]):
        request = admin_pb2.AssignRoleRequest(adminId=admin_id, roleIds=role_ids)
        try:
            response = self.stub.assignRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            from fastapi import HTTPException

            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            from fastapi import HTTPException

            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            from fastapi import HTTPException

            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            from fastapi import HTTPException

            raise HTTPException(status_code=403, detail=details)
        else:
            from fastapi import HTTPException

            raise HTTPException(status_code=500, detail="Internal server error")
