from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from app.services.user_service import UserServiceClient
from app.core.auth_guard import role_required
from app.schemas.user import (
    APIKeyCreate,
    APIKeyDelete,
    APIKeyDeleteResponse,
    APIKeyDetailsResponse,
    APIKeyInfo,
    APIKeyListResponse,
    APIKeyResponse,
)
from app.utils.parse_error import parse_error

api_key_router = APIRouter(prefix="/api-keys", tags=["api keys"])
user_service = UserServiceClient()


@api_key_router.post("/add-key", response_model=APIKeyResponse)
async def generate_api_key(
    key_data: APIKeyCreate, current_user: dict = Depends(role_required(["user"]))
):
    """
    Generate a new API key for the current user.

    Args:
        key_data (APIKeyCreate): Data required to create the API key, including name and project.
        current_user (dict): Current user information.

    Returns:
        APIKeyResponse: Response containing the generated API key details.

    Raises:
        HTTPException: If the API key generation fails or an internal error occurs.
    """

    try:
        response = await user_service.generate_api_key(
            user_id=current_user["user_id"], name=key_data.name, project=key_data.project
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        return APIKeyResponse(
            success=response.success,
            message=response.message,
            public_key=response.public_key,
            private_key=response.private_key,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@api_key_router.get("/api-keys", response_model=APIKeyListResponse)
async def list_api_keys(current_user: dict = Depends(role_required(["user"]))):
    """
    List all API keys of the current user.

    Args:
        current_user (dict): Current user information

    Returns:
        APIKeyListResponse: Response containing the list of API keys

    Raises:
        HTTPException: If the user is not found or an internal error occurs
    """
    try:
        response = await user_service.list_api_keys(user_id=current_user["user_id"])
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        api_keys = [
            APIKeyInfo(
                id=key.id,
                name=key.name,
                public_key=key.public_key,
                private_key=key.private_key,
                project=key.project,
                created_at=key.created_at,
            )
            for key in response.api_keys
        ]

        return APIKeyListResponse(
            success=response.success, message=response.message, api_keys=api_keys
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@api_key_router.delete("/{key_id}", response_model=APIKeyDeleteResponse)
async def delete_api_key(key_id: str, current_user: dict = Depends(role_required(["user"]))):
    """
    Delete an API key by its ID.

    Args:
        key_id (str): ID of the API key to delete
        current_user (dict): Current user information

    Returns:
        APIKeyDeleteResponse: Response containing the deletion status
    """

    try:
        response = await user_service.delete_api_key(user_id=current_user["user_id"], key_id=key_id)

        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        return APIKeyDeleteResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@api_key_router.get("/get-key/{api_key_id}", response_model=APIKeyDetailsResponse)
async def get_api_key_by_id(api_key_id: str, current_user: dict = Depends(role_required(["user"]))):
    """
    Get an API key by its ID.

    Args:
        api_key_id (str): ID of the API key to retrieve
        current_user (dict): Current user information

    Returns:
        APIKeyDetailsResponse: Response containing the API key information

    Raises:
        HTTPException: If the API key is not found or an internal error occurs
    """
    try:
        response = await user_service.get_api_key_by_id(
            api_key_id=api_key_id, user_id=current_user["user_id"]
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        api_key = None
        if response.success and response.api_key:
            api_key = APIKeyInfo(
                id=response.api_key.id,
                name=response.api_key.name,
                public_key=response.api_key.public_key,
                private_key=response.api_key.private_key,
                project=response.api_key.project,
                created_at=response.api_key.created_at,
            )

        return APIKeyDetailsResponse(
            success=response.success, message=response.message, api_key=api_key
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
