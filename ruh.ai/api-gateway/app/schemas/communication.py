from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel


# Enum for channel types
class ChannelType(str, Enum):
    CHANNEL_TYPE_UNSPECIFIED = "CHANNEL_TYPE_UNSPECIFIED"
    CHANNEL_TYPE_WEB = "CHANNEL_TYPE_WEB"


# Enum for chat types
class ChatType(str, Enum):
    CHAT_TYPE_UNSPECIFIED = "CHAT_TYPE_UNSPECIFIED"
    CHAT_TYPE_SINGLE = "CHAT_TYPE_SINGLE"
    CHAT_TYPE_MULTI = "CHAT_TYPE_MULTI"


# Enum for sender types
class SenderType(str, Enum):
    SENDER_TYPE_UNSPECIFIED = "SENDER_TYPE_UNSPECIFIED"
    SENDER_TYPE_USER = "SENDER_TYPE_USER"
    SENDER_TYPE_ASSISTANT = "SENDER_TYPE_ASSISTANT"


# Conversation base model
class ConversationBase(BaseModel):
    title: str
    channel: ChannelType
    chatType: ChatType
    summary: Optional[str] = None


# Conversation create model
class ConversationCreate(ConversationBase):
    agentId: str


# Conversation response model
class ConversationResponse(ConversationBase):
    id: str
    userId: str
    agentId: str
    createdAt: datetime
    updatedAt: datetime


# Pagination metadata
class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool


# List of conversations
class ConversationList(BaseModel):
    data: List[ConversationResponse]
    metadata: PaginationMetadata


# Message base model
class MessageBase(BaseModel):
    conversationId: str
    senderType: SenderType
    content: Optional[str] = None
    workflowId: Optional[str] = None
    workflowResponse: Optional[Dict[str, dict]] = None


# Message create model
class MessageCreate(MessageBase):
    pass


# Message response model
class MessageResponse(MessageBase):
    id: str
    createdAt: datetime
    updatedAt: datetime


# Message list model
class MessageList(BaseModel):
    data: List[MessageResponse]
    metadata: PaginationMetadata
