from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator, validator
from datetime import datetime
import json


class WorkflowVisibilityEnum(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"


class WorkflowStatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"


class WorkflowCategoryEnum(str, Enum):
    AUTOMATION = "automation"
    DATA_PIPELINE = "data_pipeline"
    INTEGRATION = "integration"
    WEB_SCRAPING = "web_scraping"
    API = "api"
    EMAIL = "email"
    LLM_ORCHESTRATION = "llm_orchestration"
    DATABASE = "database"
    FILE_MANAGEMENT = "file_management"
    SCHEDULING = "scheduling"
    MONITORING = "monitoring"
    CRM = "crm"
    NOTIFICATIONS = "notifications"
    DOCUMENT_PROCESSING = "document_processing"
    DEVOPS = "devops"
    GENERAL = "general"


class WorkflowBase(BaseModel):
    name: str
    workflow_data: Dict[str, Any]
    start_node_data: List[Dict[str, Any]]


class WorkflowCreate(WorkflowBase):
    pass


class WorkflowInDB(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    workflow_url: str
    builder_url: str
    start_nodes: List[Dict[str, Any]] = Field(default_factory=list)
    owner_id: str
    user_ids: List[str] = Field(default_factory=list)
    owner_type: str
    workflow_template_id: Optional[str] = None
    template_owner_id: Optional[str] = None
    is_imported: bool = False
    version: str
    visibility: Optional[WorkflowVisibilityEnum] = None
    category: Optional[WorkflowCategoryEnum] = None
    tags: Optional[Dict[str, Any]] = None
    status: Optional[WorkflowStatusEnum] = None
    is_changes_marketplace: Optional[bool] = False
    is_customizable: Optional[bool] = False
    auto_version_on_update: Optional[bool] = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    available_nodes: List[Dict[str, Any]] = Field(default_factory=list)
    is_updated: Optional[bool] = False

    class Config:
        from_attributes = True

    @validator("tags", pre=True)
    def validate_tags(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    @validator("user_ids", pre=True)
    def validate_list_fields(cls, v):
        if v is None:
            return []
        return v

    @field_validator("start_nodes", mode="before")
    @classmethod
    def parse_start_nodes(cls, v):
        if not v:
            return []

        parsed = []
        for item in v:
            if isinstance(item, str):
                try:
                    parsed.append(json.loads(item))
                except json.JSONDecodeError:
                    continue
            elif isinstance(item, dict):
                parsed.append(item)
        return parsed

    # @validator("start_nodes", pre=True)
    # def validate_start_nodes(cls, v):
    #     if not v:
    #         return []
    #     return [json.loads(item) if isinstance(item, str) else item for item in v]


class CreateWorkflowResponse(BaseModel):
    success: bool
    message: str
    workflow_id: Optional[str] = None
    # workflow: WorkflowInDB


class WorkflowResponse(BaseModel):
    success: bool
    message: str
    workflow: WorkflowInDB


class DeleteWorkflowResponse(BaseModel):
    success: bool
    message: str


class WorkflowPatchPayload(BaseModel):
    name: Optional[str] = Field(
        None, min_length=1, description="Optional new name for the workflow"
    )
    description: Optional[str] = Field(None, description="Optional new description")
    workflow_data: Optional[Dict[str, Any]] = Field(
        None, description="Optional updated workflow definition data"
    )
    start_node_data: Optional[List[Dict[str, Any]]] = Field(
        None, description="Optional updated start node data"
    )
    user_ids: Optional[List[str]] = Field(
        None, description="Optional list of user IDs with access to the workflow"
    )
    visibility: Optional[WorkflowVisibilityEnum] = Field(
        None, description="Optional visibility setting (public/private)"
    )
    category: Optional[WorkflowCategoryEnum] = Field(None, description="Optional workflow category")
    tags: Optional[Dict[str, Any]] = Field(
        None, description="Optional key-value pairs for tagging the workflow"
    )
    status: Optional[WorkflowStatusEnum] = Field(None, description="Optional workflow status")
    version: Optional[str] = Field(None, description="Optional version string")
    is_changes_marketplace: Optional[bool] = Field(None, description="Optional marketplace flag")
    is_customizable: Optional[bool] = Field(None, description="Optional customizable flag")
    auto_version_on_update: Optional[bool] = Field(
        None, description="Optional auto-versioning flag"
    )
    toggle_visibility: Optional[bool] = Field(
        None, description="If true, toggles the workflow visibility between public and private"
    )


class UpdateWorkflowResponse(BaseModel):
    success: bool
    message: str


class WorkflowsByIdsRequest(BaseModel):
    ids: List[str]


class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool


class PaginatedWorkflowResponse(BaseModel):
    data: List[WorkflowInDB]
    metadata: PaginationMetadata


class WorkflowsByIdsRequestPayload(BaseModel):
    ids: List[str] = Field(..., min_items=1)


class WorkflowsByIdsApiResponse(BaseModel):
    success: bool
    message: str
    workflows: List[WorkflowInDB]
    total: int


class RateWorkflowRequest(BaseModel):
    workflow_id: str
    user_id: str
    rating: float = Field(..., ge=1.0, le=5.0, description="Rating value between 1.0 and 5.0")


class RateWorkflowResponse(BaseModel):
    success: bool
    message: str
    workflow_id: str
    rating: float
    average_rating: Optional[float] = None


# Add these template-related models to your existing workflow.py file


class WorkflowTemplateBase(BaseModel):
    name: str
    description: str
    workflow_data: Dict[str, Any]
    category: Optional[WorkflowCategoryEnum] = None
    tags: Optional[Dict[str, Any]] = None
    status: WorkflowStatusEnum = WorkflowStatusEnum.ACTIVE
    start_node_data: Optional[List[Dict[str, Any]]] = None


class WorkflowTemplateCreate(WorkflowTemplateBase):
    pass


class WorkflowTemplateUpdate(WorkflowTemplateBase):
    pass


class WorkflowTemplatePatchPayload(BaseModel):
    name: Optional[str] = Field(None, min_length=1)
    description: Optional[str] = Field(None, min_length=1)
    workflow_data: Optional[Dict[str, Any]] = None
    start_node_data: Optional[List[Dict[str, Any]]] = None
    category: Optional[WorkflowCategoryEnum] = None
    tags: Optional[Dict[str, Any]] = None
    version: Optional[str] = None
    status: Optional[WorkflowStatusEnum] = None


class WorkflowTemplateInDB(BaseModel):
    id: str
    name: str
    description: str
    workflow_url: str
    builder_url: str
    execution_count: int = 0
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    version: str
    status: str
    use_count: int = 0
    owner_id: str
    start_nodes: List[Dict[str, Any]] = Field(default_factory=list)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    available_nodes: List[Dict[str, Any]] = Field(default_factory=list)
    source_workflow_id: Optional[str] = None  # ID of the original workflow this was created from

    class Config:
        from_attributes = True

    @validator("tags", pre=True)
    def validate_tags(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v


class WorkflowTemplateResponse(BaseModel):
    success: bool
    message: str
    template: WorkflowTemplateInDB


class CreateTemplateResponse(BaseModel):
    success: bool
    message: str


class UpdateTemplateResponse(BaseModel):
    success: bool
    message: str


class DeleteTemplateResponse(BaseModel):
    success: bool
    message: str


class ListTemplatesResponse(BaseModel):
    success: bool
    message: str
    templates: List[WorkflowTemplateInDB]
    total: int
    page: int
    total_pages: int


class CreateWorkflowFromTemplateResponse(BaseModel):
    success: bool
    message: str


class ToggleVisibilityResponseAPI(BaseModel):
    success: bool
    message: str
    workflow: Optional[WorkflowInDB] = None


class UseWorkflowRequest(BaseModel):
    workflow_id: str
    user_id: str


class UseWorkflowResponse(BaseModel):
    success: bool
    message: str
    workflow_id: str
    use_count: int


class WorkflowSettingsPayload(BaseModel):
    status: Optional[WorkflowStatusEnum] = None
    is_changes_marketplace: Optional[bool] = None


# --- Version Management Models ---
from typing import Optional, List


class WorkflowVersionInDB(BaseModel):
    id: str
    workflow_id: str
    version_number: str
    name: Optional[str] = None
    description: Optional[str] = None
    workflow_url: Optional[str] = None
    builder_url: Optional[str] = None
    start_nodes: Optional[List[dict]] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    changelog: Optional[str] = None
    status: Optional[str] = None
    is_customizable: Optional[bool] = None
    created_at: Optional[str] = None
    is_current: Optional[bool] = None


class ListWorkflowVersionsResponse(BaseModel):
    success: bool
    message: str
    versions: List[WorkflowVersionInDB]
    total: int
    page: int
    total_pages: int
    current_version_id: Optional[str] = None


class GetWorkflowVersionResponse(BaseModel):
    success: bool
    message: str
    version: WorkflowVersionInDB


class SwitchWorkflowVersionResponse(BaseModel):
    success: bool
    message: str
    new_current_version: WorkflowVersionInDB


class CreateWorkflowVersionRequest(BaseModel):
    workflow_id: str
    user_id: str
    name: Optional[str] = None
    description: Optional[str] = None
    changelog: Optional[str] = None
    auto_increment: bool = True
    version_number: Optional[str] = None


class CreateWorkflowVersionResponse(BaseModel):
    success: bool
    message: str
    version: WorkflowVersionInDB


class SyncWorkflowToMarketplaceResponse(BaseModel):
    success: bool
    message: str
    marketplace_listing_id: Optional[str] = None
