from typing import Literal, Optional

from pydantic import BaseModel, Field


class LiveKitRoomCreateRequest(BaseModel):
    agent_id: str = Field(
        ...,
        description="ID of the agent (for single chat) or agent group ID (for multi chat)",
    )
    chat_type: Literal["single", "multi"] = Field(
        ..., description="Type of chat: single (one agent) or multi (agent group)"
    )
    conversation_id: Optional[str] = Field(
        None, description="ID of the conversation. If not provided, will be generated"
    )


class LiveKitRoomCreateResponse(BaseModel):
    success: bool = Field(..., description="Whether the room was created successfully")
    token: str = Field(..., description="The token for the room")
