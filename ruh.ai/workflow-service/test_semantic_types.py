#!/usr/bin/env python3
"""
Test script to verify semantic_type implementation in component outputs.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.components.ai.agentic_ai import Agentic<PERSON><PERSON>
from app.components.ai.classifier import Classifier
from app.components.processing.select_data import SelectDataComponent
from app.components.control_flow.conditionalNode import ConditionalNode
from app.components.data_interaction.api_request import ApiRequestNode
from app.constants.semantic_types import (
    AI_RESPONSE, AI_ANALYSIS, AI_METADATA, AI_MEMORY, ERROR_INFO,
    SELECTED_DATA, ROUTING_DATA, API_RESPONSE, STATUS_INFO, METADATA
)

def test_component_semantic_types():
    """Test that components have proper semantic_type values in their outputs."""
    
    print("Testing semantic_type implementation...")
    print("=" * 50)
    
    # Test AgenticAI component
    print("\n1. Testing AgenticAI component:")
    agentic_ai = AgenticAI()
    for output in agentic_ai.outputs:
        print(f"   - {output.name}: {output.semantic_type}")
        assert output.semantic_type is not None, f"semantic_type is None for {output.name}"
    
    expected_semantic_types = {
        "final_answer": AI_RESPONSE,
        "intermediate_steps": AI_METADATA,
        "updated_memory": AI_MEMORY,
        "error": ERROR_INFO
    }
    
    for output in agentic_ai.outputs:
        expected = expected_semantic_types.get(output.name)
        if expected:
            assert output.semantic_type == expected, f"Expected {expected}, got {output.semantic_type} for {output.name}"
    
    print("   ✓ AgenticAI semantic types are correct")
    
    # Test Classifier component
    print("\n2. Testing Classifier component:")
    classifier = Classifier()
    for output in classifier.outputs:
        print(f"   - {output.name}: {output.semantic_type}")
        assert output.semantic_type is not None, f"semantic_type is None for {output.name}"
    
    expected_semantic_types = {
        "category": AI_ANALYSIS,
        "confidence": AI_METADATA,
        "error": ERROR_INFO
    }
    
    for output in classifier.outputs:
        expected = expected_semantic_types.get(output.name)
        if expected:
            assert output.semantic_type == expected, f"Expected {expected}, got {output.semantic_type} for {output.name}"
    
    print("   ✓ Classifier semantic types are correct")
    
    # Test SelectDataComponent
    print("\n3. Testing SelectDataComponent:")
    select_data = SelectDataComponent()
    for output in select_data.outputs:
        print(f"   - {output.name}: {output.semantic_type}")
        assert output.semantic_type is not None, f"semantic_type is None for {output.name}"
    
    expected_semantic_types = {
        "output_data": SELECTED_DATA,
        "error": ERROR_INFO
    }
    
    for output in select_data.outputs:
        expected = expected_semantic_types.get(output.name)
        if expected:
            assert output.semantic_type == expected, f"Expected {expected}, got {output.semantic_type} for {output.name}"
    
    print("   ✓ SelectDataComponent semantic types are correct")
    
    # Test ConditionalNode
    print("\n4. Testing ConditionalNode:")
    conditional_node = ConditionalNode()
    for output in conditional_node.outputs:
        print(f"   - {output.name}: {output.semantic_type}")
        assert output.semantic_type is not None, f"semantic_type is None for {output.name}"
    
    expected_semantic_types = {
        "default_output": ROUTING_DATA
    }
    
    for output in conditional_node.outputs:
        expected = expected_semantic_types.get(output.name)
        if expected:
            assert output.semantic_type == expected, f"Expected {expected}, got {output.semantic_type} for {output.name}"
    
    print("   ✓ ConditionalNode semantic types are correct")
    
    # Test ApiRequestNode
    print("\n5. Testing ApiRequestNode:")
    api_request = ApiRequestNode()
    for output in api_request.outputs:
        print(f"   - {output.name}: {output.semantic_type}")
        assert output.semantic_type is not None, f"semantic_type is None for {output.name}"
    
    expected_semantic_types = {
        "data": API_RESPONSE,
        "status_code": STATUS_INFO,
        "response_headers": METADATA,
        "error": ERROR_INFO
    }
    
    for output in api_request.outputs:
        expected = expected_semantic_types.get(output.name)
        if expected:
            assert output.semantic_type == expected, f"Expected {expected}, got {output.semantic_type} for {output.name}"
    
    print("   ✓ ApiRequestNode semantic types are correct")
    
    print("\n" + "=" * 50)
    print("✅ All semantic_type tests passed!")
    print("\nSemantic types are properly implemented and working correctly.")

if __name__ == "__main__":
    test_component_semantic_types()
