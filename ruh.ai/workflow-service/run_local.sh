
#!/bin/bash

echo "Installing dependencies..."
poetry install --no-root

echo "Generating gRPC code..."
poetry run python -m app.scripts.generate_grpc

# echo "Generating database migrations..."
# poetry run python -m app.db.create_auto_migration "auto update $(date +%Y%m%d%H%M%S)"

# echo "Applying database migrations..."
# poetry run python -m app.db.apply_migrations

echo "Initializing database..."
poetry run python -m app.db.init_db


echo "Starting Workflow Service..."
poetry run python -m app.main
