import sys
import os
import json
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add the current directory to the path so we can import the app modules
sys.path.append(os.getcwd())

from app.core.config import settings
from app.models.workflow import Workflow
from app.utils.constants.constants import (
    WorkflowOwnerTypeEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
)


def insert_dummy_workflows():
    # Connect to the database
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Clear existing data from the workflows-test table
        db.query(Workflow).delete()
        db.commit()
        print("Cleared existing data from workflows-test table")

        # Define the dummy workflows
        workflows = [
            {
                "id": "0c94cc8f-de71-4dbe-a5e7-d6c1725e41b5",
                "name": "Untitled Workflow",
                "description": None,
                "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/c2d3c2fb-4e73-46b6-b998-b43093e47e8a.json",
                "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/5b44da4c-45d2-4cb5-9068-c65e016377e2.json",
                "start_nodes": [],
                "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
                "user_ids": ["c1454e90-09ac-40f2-bde2-833387d7b645"],
                "owner_type": WorkflowOwnerTypeEnum.USER,
                "workflow_template_id": None,
                "template_owner_id": None,
                "url": None,
                "is_imported": False,
                "version": "1.0.0",
                "is_changes_marketplace": None,
                "visibility": WorkflowVisibilityEnum.PRIVATE,
                "category": None,
                "tags": None,
                "status": WorkflowStatusEnum.ACTIVE,
                "created_at": datetime.fromisoformat("2025-05-14 06:53:39.524345"),
                "updated_at": datetime.fromisoformat("2025-05-14 06:53:39.524349"),
            },
            {
                "id": "5b8ab822-0ac5-4793-a4aa-6e9218de1c63",
                "name": "Untitled Workflow",
                "description": None,
                "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/5a47b47b-3322-45ee-93ea-735df0e6c93c.json",
                "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/bd53f906-094f-4b15-b3da-100d0bef022e.json",
                "start_nodes": [],
                "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
                "user_ids": ["c1454e90-09ac-40f2-bde2-833387d7b645"],
                "owner_type": WorkflowOwnerTypeEnum.USER,
                "workflow_template_id": None,
                "template_owner_id": None,
                "url": None,
                "is_imported": False,
                "version": "1.0.0",
                "is_changes_marketplace": None,
                "visibility": WorkflowVisibilityEnum.PRIVATE,
                "category": None,
                "tags": None,
                "status": WorkflowStatusEnum.ACTIVE,
                "created_at": datetime.fromisoformat("2025-05-14 07:04:05.258143"),
                "updated_at": datetime.fromisoformat("2025-05-14 07:04:05.25815"),
            },
            {
                "id": "fe90ff49-3cc7-4a7f-8dff-7f2035595e26",
                "name": "Untitled Workflow",
                "description": None,
                "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/bee0c0c3-e946-4102-9345-248593e742e4.json",
                "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/e6efd953-fc8a-43ae-86f3-694f7837f713.json",
                "start_nodes": [],
                "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
                "user_ids": ["91a237fd-0225-4e02-9e9f-805eff073b07"],
                "owner_type": WorkflowOwnerTypeEnum.USER,
                "workflow_template_id": None,
                "template_owner_id": None,
                "url": None,
                "is_imported": False,
                "version": "1.0.0",
                "is_changes_marketplace": None,
                "visibility": WorkflowVisibilityEnum.PRIVATE,
                "category": None,
                "tags": None,
                "status": WorkflowStatusEnum.ACTIVE,
                "created_at": datetime.fromisoformat("2025-05-14 07:30:12.750203"),
                "updated_at": datetime.fromisoformat("2025-05-14 07:30:12.750212"),
            },
            {
                "id": "95ef19e9-124b-4fd7-b094-1ab2e2419741",
                "name": "Untitled Workflow",
                "description": "testing workflow ",
                "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/3287e640-d8be-42a0-a99f-84886c916e24.json",
                "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c0a48de1-cd4b-4c6f-adb2-f1bc1ecdc900.json",
                "start_nodes": ["topic"],
                "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
                "user_ids": ["91a237fd-0225-4e02-9e9f-805eff073b07"],
                "owner_type": WorkflowOwnerTypeEnum.USER,
                "workflow_template_id": None,
                "template_owner_id": None,
                "url": None,
                "is_imported": False,
                "version": "1.0.0",
                "is_changes_marketplace": None,
                "visibility": WorkflowVisibilityEnum.PRIVATE,
                "category": None,
                "tags": None,
                "status": WorkflowStatusEnum.ACTIVE,
                "created_at": datetime.fromisoformat("2025-05-14 07:33:21.55281"),
                "updated_at": datetime.fromisoformat("2025-05-14 07:51:09.305448"),
            },
            {
                "id": "15ed4fe7-35b6-4b46-93f8-a685310b1059",
                "name": "Untitled Workflow",
                "description": None,
                "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/d7619db7-123f-4127-8e48-e3cebb2dd4e2.json",
                "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/3e297e65-b1fc-4f03-8cb9-6a67317acda8.json",
                "start_nodes": [],
                "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
                "user_ids": ["c1454e90-09ac-40f2-bde2-833387d7b645"],
                "owner_type": WorkflowOwnerTypeEnum.USER,
                "workflow_template_id": None,
                "template_owner_id": None,
                "url": None,
                "is_imported": False,
                "version": "1.0.0",
                "is_changes_marketplace": None,
                "visibility": WorkflowVisibilityEnum.PRIVATE,
                "category": None,
                "tags": None,
                "status": WorkflowStatusEnum.ACTIVE,
                "created_at": datetime.fromisoformat("2025-05-14 12:19:23.233592"),
                "updated_at": datetime.fromisoformat("2025-05-14 12:19:23.233601"),
            },
            {
                "id": "0810c84a-abbc-4e4b-8f59-d70a65c6a447",
                "name": "string",
                "description": None,
                "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/bf912a00-d2eb-43d2-93c9-dafe7a7fb3f2.json",
                "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/8066ce07-2bc7-433a-9b7e-ecadacc2e0a8.json",
                "start_nodes": ["string"],
                "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
                "user_ids": ["91a237fd-0225-4e02-9e9f-805eff073b07"],
                "owner_type": WorkflowOwnerTypeEnum.USER,
                "workflow_template_id": None,
                "template_owner_id": None,
                "url": None,
                "is_imported": False,
                "version": "1.0.0",
                "is_changes_marketplace": None,
                "visibility": WorkflowVisibilityEnum.PRIVATE,
                "category": None,
                "tags": None,
                "status": WorkflowStatusEnum.ACTIVE,
                "created_at": datetime.fromisoformat("2025-05-14 12:57:55.756074"),
                "updated_at": datetime.fromisoformat("2025-05-14 12:57:55.75608"),
            },
            {
                "id": "f5d03ca0-61a9-4965-aad7-4f1cbaa78b04",
                "name": "Untitled Workflow",
                "description": "testing workflow ",
                "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/617229cb-d82e-4bbd-87d7-75a656be9db8.json",
                "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a99d26e3-5a24-4a30-b3c9-642278dc1add.json",
                "start_nodes": ["resume_s3_link", "job_description_s3_link"],
                "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
                "user_ids": ["c1454e90-09ac-40f2-bde2-833387d7b645"],
                "owner_type": WorkflowOwnerTypeEnum.USER,
                "workflow_template_id": None,
                "template_owner_id": None,
                "url": None,
                "is_imported": False,
                "version": "1.0.0",
                "is_changes_marketplace": None,
                "visibility": WorkflowVisibilityEnum.PRIVATE,
                "category": None,
                "tags": None,
                "status": WorkflowStatusEnum.ACTIVE,
                "created_at": datetime.fromisoformat("2025-05-14 13:13:32.521741"),
                "updated_at": datetime.fromisoformat("2025-05-14 13:31:17.809495"),
            },
        ]

        # Insert the workflows
        for workflow_data in workflows:
            workflow = Workflow(**workflow_data)
            db.add(workflow)

        # Commit the changes
        db.commit()

        print(f"Successfully inserted {len(workflows)} workflows")

        # Print the inserted workflows
        for i, workflow in enumerate(workflows, 1):
            print(f"\nWorkflow {i}:")
            print(f"  ID: {workflow['id']}")
            print(f"  Name: {workflow['name']}")
            print(f"  Owner ID: {workflow['owner_id']}")
            print(f"  User IDs: {workflow['user_ids']}")
            print(f"  Start Nodes: {workflow['start_nodes']}")

    except Exception as e:
        db.rollback()
        print(f"Error inserting workflows: {str(e)}")
    finally:
        db.close()


if __name__ == "__main__":
    insert_dummy_workflows()
