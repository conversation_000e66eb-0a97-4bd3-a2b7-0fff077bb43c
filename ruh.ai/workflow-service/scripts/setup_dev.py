#!/usr/bin/env python3
import os
from pathlib import Path

def setup_dev_environment():
    """Set up the development environment."""
    # Get the root directory of the project
    current_dir = Path(__file__).resolve().parent
    service_root = current_dir.parent
    workspace_root = service_root.parent

    # Create proto-definitions symbolic link
    proto_source = workspace_root / "proto-definitions"
    proto_target = service_root / "proto-definitions"

    if not proto_target.exists():
        if not proto_source.exists():
            raise FileNotFoundError(
                f"Proto definitions directory not found at {proto_source}. "
                "Please make sure it exists."
            )
        os.symlink(proto_source, proto_target, target_is_directory=True)
        print(f"Created symbolic link for proto files: {proto_target} -> {proto_source}")

    # Create necessary directories
    dirs_to_create = [
        service_root / "app" / "grpc",
        service_root / "app" / "db" / "migrations" / "versions",
    ]

    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {dir_path}")

    # Create __init__.py files
    init_paths = [
        service_root / "app" / "__init__.py",
        service_root / "app" / "api" / "__init__.py",
        service_root / "app" / "core" / "__init__.py",
        service_root / "app" / "db" / "__init__.py",
        service_root / "app" / "models" / "__init__.py",
        service_root / "app" / "services" / "__init__.py",
        service_root / "app" / "scripts" / "__init__.py",
    ]

    for init_path in init_paths:
        init_path.parent.mkdir(parents=True, exist_ok=True)
        if not init_path.exists():
            init_path.touch()
            print(f"Created __init__.py: {init_path}")

if __name__ == "__main__":
    setup_dev_environment() 