import uuid
import json
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os
import sys

# Add the current directory to the path so we can import the app modules
sys.path.append(os.getcwd())

from app.core.config import settings
from app.models.workflow import WorkflowTemplate
from app.utils.constants.constants import (
    WorkflowCategoryEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
)

# Sample workflow schema (simplified for this example)
SAMPLE_WORKFLOW_SCHEMA = {
    "connections": [
        {"from_transition_id": "start", "to_transition_id": "process", "conditional_routing": None},
        {"from_transition_id": "process", "to_transition_id": "end", "conditional_routing": None},
    ],
    "transitions": {
        "start": {
            "transition_id": "start",
            "node": {
                "node_id": "start_node",
                "tools_to_use": {
                    "tool1": {
                        "tool_id": "start_tool",
                        "tool_name": "Start Tool",
                        "tool_params": {"items": []},
                    }
                },
            },
        },
        "process": {
            "transition_id": "process",
            "node": {
                "node_id": "process_node",
                "tools_to_use": {
                    "tool1": {
                        "tool_id": "process_tool",
                        "tool_name": "Process Tool",
                        "tool_params": {"items": []},
                    }
                },
            },
        },
        "end": {
            "transition_id": "end",
            "node": {
                "node_id": "end_node",
                "tools_to_use": {
                    "tool1": {
                        "tool_id": "end_tool",
                        "tool_name": "End Tool",
                        "tool_params": {"items": []},
                    }
                },
            },
        },
    },
}

# Sample builder schema (simplified for this example)
SAMPLE_BUILDER_SCHEMA = {
    "nodes": {
        "start_node": {"position": {"x": 100, "y": 100}},
        "process_node": {"position": {"x": 300, "y": 100}},
        "end_node": {"position": {"x": 500, "y": 100}},
    },
    "edges": [
        {"source": "start_node", "target": "process_node"},
        {"source": "process_node", "target": "end_node"},
    ],
}


def create_dummy_templates():
    # Connect to the database
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Create 4 dummy workflow templates
        templates = [
            WorkflowTemplate(
                id=str(uuid.uuid4()),
                name="Data Processing Workflow",
                description="A workflow for processing and analyzing data from various sources",
                workflow_url=json.dumps(SAMPLE_WORKFLOW_SCHEMA),
                builder_url=json.dumps(SAMPLE_BUILDER_SCHEMA),
                start_nodes=["start"],
                owner_id="user123",
                use_count=15,
                execution_count=25,
                average_rating=4,
                category=WorkflowCategoryEnum.DATA_PIPELINE,
                tags={
                    "domain": "data",
                    "complexity": "medium",
                    "integrations": ["database", "api"],
                },
                version="1.0.0",
                status=WorkflowStatusEnum.ACTIVE,
                visibility=WorkflowVisibilityEnum.PUBLIC,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            ),
            WorkflowTemplate(
                id=str(uuid.uuid4()),
                name="Customer Support Automation",
                description="Automate customer support responses and ticket routing",
                workflow_url=json.dumps(SAMPLE_WORKFLOW_SCHEMA),
                builder_url=json.dumps(SAMPLE_BUILDER_SCHEMA),
                start_nodes=["start"],
                owner_id="user456",
                use_count=32,
                execution_count=48,
                average_rating=5,
                category=WorkflowCategoryEnum.AUTOMATION,
                tags={"domain": "support", "complexity": "high", "integrations": ["crm", "email"]},
                version="2.1.0",
                status=WorkflowStatusEnum.ACTIVE,
                visibility=WorkflowVisibilityEnum.PUBLIC,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            ),
            WorkflowTemplate(
                id=str(uuid.uuid4()),
                name="Document Processing Pipeline",
                description="Extract, transform, and analyze data from documents",
                workflow_url=json.dumps(SAMPLE_WORKFLOW_SCHEMA),
                builder_url=json.dumps(SAMPLE_BUILDER_SCHEMA),
                start_nodes=["start"],
                owner_id="user789",
                use_count=8,
                execution_count=12,
                average_rating=3,
                category=WorkflowCategoryEnum.DOCUMENT_PROCESSING,
                tags={
                    "domain": "documents",
                    "complexity": "medium",
                    "integrations": ["ocr", "nlp"],
                },
                version="1.2.0",
                status=WorkflowStatusEnum.ACTIVE,
                visibility=WorkflowVisibilityEnum.PUBLIC,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            ),
            WorkflowTemplate(
                id=str(uuid.uuid4()),
                name="API Integration Workflow",
                description="Connect and orchestrate multiple APIs for data exchange",
                workflow_url=json.dumps(SAMPLE_WORKFLOW_SCHEMA),
                builder_url=json.dumps(SAMPLE_BUILDER_SCHEMA),
                start_nodes=["start"],
                owner_id="user123",
                use_count=22,
                execution_count=30,
                average_rating=4,
                category=WorkflowCategoryEnum.INTEGRATION,
                tags={
                    "domain": "integration",
                    "complexity": "high",
                    "integrations": ["rest", "graphql"],
                },
                version="1.0.5",
                status=WorkflowStatusEnum.ACTIVE,
                visibility=WorkflowVisibilityEnum.PUBLIC,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            ),
        ]

        # Add templates to the database
        for template in templates:
            db.add(template)

        # Commit the changes
        db.commit()

        print(f"Successfully inserted {len(templates)} workflow templates")

        # Print the inserted templates
        for i, template in enumerate(templates, 1):
            print(f"\nTemplate {i}:")
            print(f"  ID: {template.id}")
            print(f"  Name: {template.name}")
            print(f"  Category: {template.category}")
            print(f"  Visibility: {template.visibility}")
            print(f"  Owner ID: {template.owner_id}")
            print(f"  Rating: {template.average_rating}")

    except Exception as e:
        db.rollback()
        print(f"Error inserting templates: {str(e)}")
    finally:
        db.close()


if __name__ == "__main__":
    create_dummy_templates()
