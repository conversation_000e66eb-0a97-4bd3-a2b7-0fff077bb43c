"""
Component loader utility.

This module provides functions for loading all components in the application.
"""

import importlib
import inspect
import logging
import os
import pkgutil
from pathlib import Path
from typing import List, Dict, Any, Type

from app.utils.logger import setup_logger

# Set up logging
logger = setup_logger("component-loader")


def load_all_components() -> List[str]:
    """
    Load all component modules in the application.

    This function imports all modules in the app/components directory and its subdirectories.

    Returns:
        A list of imported module names.
    """
    logger.info("Loading all components...")

    # Import the base component package
    try:
        import app.components

        base_path = Path(app.components.__file__).parent
        logger.info(f"Base component path: {base_path}")
    except ImportError as e:
        logger.error(f"Failed to import app.components: {e}")
        return []

    imported_modules = []

    # First, import all direct subpackages
    try:
        from app.components import (
            core,
            io,
            processing,
            ai,
            data_interaction,
            tools,
            hitl,
            control_flow,
        )

        logger.info("Imported main component packages")
    except ImportError as e:
        logger.error(f"Failed to import some component packages: {e}")

    # Walk through all modules in the components directory
    for root, dirs, files in os.walk(base_path):
        # Calculate the relative path from the workspace root
        rel_path = os.path.relpath(root, os.path.dirname(os.path.dirname(base_path)))
        # Convert path to package format (with dots)
        package_path = rel_path.replace(os.path.sep, ".")

        # Skip __pycache__ directories
        if "__pycache__" in root:
            continue

        # Import Python files
        for file in files:
            if file.endswith(".py") and not file.startswith("__"):
                module_name = file[:-3]  # Remove .py extension
                full_module_name = f"{package_path}.{module_name}"

                try:
                    module = importlib.import_module(full_module_name)
                    imported_modules.append(full_module_name)

                    # Log classes in the module
                    for name, obj in inspect.getmembers(module, inspect.isclass):
                        if obj.__module__ == full_module_name:
                            logger.info(f"Found class {name} in module {full_module_name}")

                except ImportError as e:
                    logger.error(f"Failed to import {full_module_name}: {e}")

    logger.info(f"Loaded {len(imported_modules)} component modules")
    return imported_modules


if __name__ == "__main__":
    # Test the component loader
    loaded_modules = load_all_components()
    print(f"Loaded {len(loaded_modules)} modules:")
    for module in loaded_modules:
        print(f"  - {module}")
