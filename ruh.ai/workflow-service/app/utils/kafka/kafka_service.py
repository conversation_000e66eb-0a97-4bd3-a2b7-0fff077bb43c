import logging
import json
from typing import Dict, Any, List, Optional
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.utils.kafka.kafka_client import KafkaClient
from app.core.config import settings


class KafkaProducer:
    def __init__(self):
        """Initialize the KafkaProducer with KafkaClient for connection management."""
        self.logger = logging.getLogger(__name__)
        self.kafka_client = KafkaClient()
        self.producer = self.kafka_client.connect_producer()

    def __del__(self):
        """Destructor to close the connection using KafkaClient."""
        self.kafka_client.close()

    def send_email_event_unified(
        self,
        email_type: SendEmailTypeEnum,
        data: Dict[str, Any],
        action: List[str]
    ) -> Dict[str, Any]:
        """
        Unified function to emit email events via Kafka.

        Args:
            email_type: Type of email to send (topic name)
            data: Dictionary containing email data
            action: Action type for the email event 

        Returns:
            Dict with success status and message
        """
        try:
            message = {
                "action": action,
                "data": data
            }

            # Convert message to JSON string
            message_bytes = json.dumps(message)

            # Send message to specified Kafka topic
            future = self.producer.send(topic=email_type, value=message_bytes)

            # Block until message is sent (or timeout)
            future.get(timeout=10)

            self.logger.info(f"Email event published to topic '{email_type}'.")
            return {"success": True, "message": f"Email event published to topic '{email_type}'."}
        except Exception as error:
            error_msg = f"send_email_event error - {str(error)}"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg}
