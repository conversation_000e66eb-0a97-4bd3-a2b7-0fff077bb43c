import logging
import sys
from typing import Optional

def setup_logger(name: Optional[str] = None, log_level: str = "INFO") -> logging.Logger:
    """
    Set up a basic logger.

    Args:
        name (Optional[str]): Name of the logger. Use None for root logger.
        log_level (str): Logging level (e.g., 'DEBUG', 'INFO').

    Returns:
        logging.Logger: Configured logger instance.
    """
    logger = logging.getLogger(name)

    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    logger.propagate = False
    return logger
