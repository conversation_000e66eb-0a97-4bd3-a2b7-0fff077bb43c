"""
Circuit breaker pattern implementation.

This module provides a circuit breaker implementation for making external service calls
more resilient to failures. It prevents cascading failures by failing fast when a service
is unavailable, and allows for automatic recovery when the service becomes available again.
"""

import time
import logging
import asyncio
from enum import Enum
from typing import Dict, Any, Callable, Awaitable, Optional, TypeVar, Generic, Union
from functools import wraps

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for the return type of the function
T = TypeVar("T")


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"  # Normal operation, requests are allowed
    OPEN = "open"  # Circuit is open, requests are not allowed
    HALF_OPEN = "half-open"  # Testing if the service is back online


class CircuitBreaker(Generic[T]):
    """
    Circuit breaker implementation.
    
    This class implements the circuit breaker pattern for making external service calls
    more resilient to failures. It prevents cascading failures by failing fast when a service
    is unavailable, and allows for automatic recovery when the service becomes available again.
    """
    
    def __init__(
        self,
        name: str,
        failure_threshold: int = 5,
        recovery_timeout: float = 30.0,
        timeout: float = 10.0,
        fallback_function: Optional[Callable[..., Awaitable[T]]] = None,
    ):
        """
        Initialize the circuit breaker.
        
        Args:
            name: The name of the circuit breaker.
            failure_threshold: The number of consecutive failures before opening the circuit.
            recovery_timeout: The time in seconds to wait before trying to close the circuit again.
            timeout: The timeout in seconds for the protected function.
            fallback_function: A function to call when the circuit is open.
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.timeout = timeout
        self.fallback_function = fallback_function
        
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0
        self.last_success_time = 0
    
    async def __call__(
        self, func: Callable[..., Awaitable[T]], *args: Any, **kwargs: Any
    ) -> T:
        """
        Call the protected function.
        
        Args:
            func: The function to call.
            *args: Positional arguments to pass to the function.
            **kwargs: Keyword arguments to pass to the function.
            
        Returns:
            The result of the function call.
            
        Raises:
            Exception: If the circuit is open and no fallback function is provided.
        """
        # Check if the circuit is open
        if self.state == CircuitState.OPEN:
            # Check if the recovery timeout has elapsed
            if time.time() - self.last_failure_time > self.recovery_timeout:
                logger.info(f"Circuit {self.name} is half-open, testing service...")
                self.state = CircuitState.HALF_OPEN
            else:
                logger.warning(f"Circuit {self.name} is open, failing fast...")
                if self.fallback_function:
                    return await self.fallback_function(*args, **kwargs)
                raise Exception(f"Circuit {self.name} is open")
        
        # Call the function with a timeout
        try:
            result = await asyncio.wait_for(func(*args, **kwargs), timeout=self.timeout)
            
            # If the circuit was half-open and the call succeeded, close the circuit
            if self.state == CircuitState.HALF_OPEN:
                logger.info(f"Circuit {self.name} is now closed")
                self.state = CircuitState.CLOSED
            
            # Reset the failure count
            self.failure_count = 0
            self.last_success_time = time.time()
            
            return result
        except Exception as e:
            # Increment the failure count
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            # If the failure threshold is reached, open the circuit
            if self.failure_count >= self.failure_threshold:
                logger.error(f"Circuit {self.name} is now open due to {self.failure_count} consecutive failures")
                self.state = CircuitState.OPEN
            
            # If the circuit was half-open, keep it open
            if self.state == CircuitState.HALF_OPEN:
                logger.warning(f"Circuit {self.name} remains open after failed test")
                self.state = CircuitState.OPEN
            
            # If a fallback function is provided, call it
            if self.fallback_function:
                return await self.fallback_function(*args, **kwargs)
            
            # Otherwise, re-raise the exception
            raise


# Global registry of circuit breakers
_circuit_breakers: Dict[str, CircuitBreaker] = {}


def get_circuit_breaker(name: str) -> CircuitBreaker:
    """
    Get a circuit breaker by name.
    
    Args:
        name: The name of the circuit breaker.
        
    Returns:
        The circuit breaker.
    """
    if name not in _circuit_breakers:
        _circuit_breakers[name] = CircuitBreaker(name)
    return _circuit_breakers[name]


def circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    recovery_timeout: float = 30.0,
    timeout: float = 10.0,
    fallback_function: Optional[Callable[..., Awaitable[T]]] = None,
) -> Callable[[Callable[..., Awaitable[T]]], Callable[..., Awaitable[T]]]:
    """
    Decorator for applying a circuit breaker to a function.
    
    Args:
        name: The name of the circuit breaker.
        failure_threshold: The number of consecutive failures before opening the circuit.
        recovery_timeout: The time in seconds to wait before trying to close the circuit again.
        timeout: The timeout in seconds for the protected function.
        fallback_function: A function to call when the circuit is open.
        
    Returns:
        A decorator function.
    """
    
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        """
        Decorator function.
        
        Args:
            func: The function to decorate.
            
        Returns:
            The decorated function.
        """
        cb = CircuitBreaker(
            name=name,
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            timeout=timeout,
            fallback_function=fallback_function,
        )
        
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            """
            Wrapper function.
            
            Args:
                *args: Positional arguments to pass to the function.
                **kwargs: Keyword arguments to pass to the function.
                
            Returns:
                The result of the function call.
            """
            return await cb(func, *args, **kwargs)
        
        return wrapper
    
    return decorator
