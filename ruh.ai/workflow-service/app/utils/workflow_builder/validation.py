from typing import Dict, Any, List, Optional, Union, Type
from pydantic import BaseModel, Field, ValidationError, validator
import re
import json
import logging

logger = logging.getLogger(__name__)

class InputValidationError(Exception):
    """Exception raised for input validation errors."""
    def __init__(self, field_name: str, message: str):
        self.field_name = field_name
        self.message = message
        super().__init__(f"Validation error for field '{field_name}': {message}")


class StringValidator(BaseModel):
    """Validator for string inputs."""
    value: str
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    pattern: Optional[str] = None
    
    @validator('value')
    def validate_string(cls, v, values):
        # Check min length
        if values.get('min_length') is not None and len(v) < values['min_length']:
            raise ValueError(f"String must be at least {values['min_length']} characters long")
        
        # Check max length
        if values.get('max_length') is not None and len(v) > values['max_length']:
            raise ValueError(f"String must be at most {values['max_length']} characters long")
        
        # Check pattern
        if values.get('pattern') is not None and not re.match(values['pattern'], v):
            raise ValueError(f"String must match pattern: {values['pattern']}")
        
        return v


class NumberValidator(BaseModel):
    """Validator for number inputs."""
    value: Union[int, float]
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    
    @validator('value')
    def validate_number(cls, v, values):
        # Check min value
        if values.get('min_value') is not None and v < values['min_value']:
            raise ValueError(f"Number must be at least {values['min_value']}")
        
        # Check max value
        if values.get('max_value') is not None and v > values['max_value']:
            raise ValueError(f"Number must be at most {values['max_value']}")
        
        return v


class ListValidator(BaseModel):
    """Validator for list inputs."""
    value: List[Any]
    min_items: Optional[int] = None
    max_items: Optional[int] = None
    item_type: Optional[str] = None
    
    @validator('value')
    def validate_list(cls, v, values):
        # Check min items
        if values.get('min_items') is not None and len(v) < values['min_items']:
            raise ValueError(f"List must have at least {values['min_items']} items")
        
        # Check max items
        if values.get('max_items') is not None and len(v) > values['max_items']:
            raise ValueError(f"List must have at most {values['max_items']} items")
        
        # Check item type
        if values.get('item_type') is not None:
            for i, item in enumerate(v):
                if values['item_type'] == 'string' and not isinstance(item, str):
                    raise ValueError(f"Item at index {i} must be a string")
                elif values['item_type'] == 'number' and not isinstance(item, (int, float)):
                    raise ValueError(f"Item at index {i} must be a number")
        
        return v


class DictValidator(BaseModel):
    """Validator for dictionary inputs."""
    value: Dict[str, Any]
    required_keys: Optional[List[str]] = None
    
    @validator('value')
    def validate_dict(cls, v, values):
        # Check required keys
        if values.get('required_keys') is not None:
            for key in values['required_keys']:
                if key not in v:
                    raise ValueError(f"Dictionary must contain key: {key}")
        
        return v


def validate_input(
    input_name: str,
    input_value: Any,
    input_type: str,
    validation_rules: Optional[Dict[str, Any]] = None
) -> Any:
    """
    Validate an input value based on its type and validation rules.
    
    Args:
        input_name: The name of the input
        input_value: The value to validate
        input_type: The type of the input
        validation_rules: Additional validation rules
        
    Returns:
        The validated value (possibly converted to the correct type)
        
    Raises:
        InputValidationError: If validation fails
    """
    validation_rules = validation_rules or {}
    
    try:
        # Handle different input types
        if input_type == 'string':
            # Try to parse JSON strings if they look like JSON
            if isinstance(input_value, str) and input_value.strip().startswith(('[', '{')):
                try:
                    # Only convert if it's valid JSON
                    json_value = json.loads(input_value)
                    # If we're expecting a string but got JSON, keep it as a string
                    # unless there's a specific rule to parse JSON
                    if validation_rules.get('parse_json', False):
                        input_value = json_value
                except json.JSONDecodeError:
                    # Not valid JSON, keep as string
                    pass
                    
            # Validate string
            validator = StringValidator(
                value=str(input_value),
                **{k: v for k, v in validation_rules.items() if k in ['min_length', 'max_length', 'pattern']}
            )
            return validator.value
            
        elif input_type in ['int', 'float', 'number']:
            # Convert to number
            try:
                if input_type == 'int':
                    input_value = int(input_value)
                else:
                    input_value = float(input_value)
            except (ValueError, TypeError):
                raise InputValidationError(input_name, f"Cannot convert '{input_value}' to a number")
                
            # Validate number
            validator = NumberValidator(
                value=input_value,
                **{k: v for k, v in validation_rules.items() if k in ['min_value', 'max_value']}
            )
            return validator.value
            
        elif input_type == 'bool':
            # Convert to boolean
            if isinstance(input_value, str):
                if input_value.lower() in ['true', 'yes', '1', 'y']:
                    return True
                elif input_value.lower() in ['false', 'no', '0', 'n']:
                    return False
                else:
                    raise InputValidationError(input_name, f"Cannot convert '{input_value}' to a boolean")
            return bool(input_value)
            
        elif input_type == 'list':
            # Convert to list if it's a string
            if isinstance(input_value, str):
                try:
                    input_value = json.loads(input_value)
                    if not isinstance(input_value, list):
                        input_value = [input_value]
                except json.JSONDecodeError:
                    # If it's not valid JSON, treat it as a single-item list
                    input_value = [input_value]
            elif not isinstance(input_value, list):
                input_value = [input_value]
                
            # Validate list
            validator = ListValidator(
                value=input_value,
                **{k: v for k, v in validation_rules.items() if k in ['min_items', 'max_items', 'item_type']}
            )
            return validator.value
            
        elif input_type == 'dict':
            # Convert to dict if it's a string
            if isinstance(input_value, str):
                try:
                    input_value = json.loads(input_value)
                    if not isinstance(input_value, dict):
                        raise InputValidationError(input_name, f"Expected a dictionary, got {type(input_value).__name__}")
                except json.JSONDecodeError:
                    raise InputValidationError(input_name, f"Cannot parse '{input_value}' as JSON")
            elif not isinstance(input_value, dict):
                raise InputValidationError(input_name, f"Expected a dictionary, got {type(input_value).__name__}")
                
            # Validate dict
            validator = DictValidator(
                value=input_value,
                **{k: v for k, v in validation_rules.items() if k in ['required_keys']}
            )
            return validator.value
            
        # Add more types as needed
        
        # If no specific validation, return as is
        return input_value
        
    except ValidationError as e:
        # Convert Pydantic validation error to our custom error
        error_msg = '; '.join([f"{err['msg']}" for err in e.errors()])
        raise InputValidationError(input_name, error_msg)
    except InputValidationError:
        # Re-raise our custom errors
        raise
    except Exception as e:
        # Catch any other errors and convert to our custom error
        raise InputValidationError(input_name, str(e))


def validate_component_inputs(
    component: Any,
    inputs: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Validate all inputs for a component.
    
    Args:
        component: The component with input definitions
        inputs: The input values to validate
        
    Returns:
        A dictionary of validated inputs
        
    Raises:
        InputValidationError: If validation fails for any input
    """
    validated_inputs = {}
    validation_errors = []
    
    # Get input definitions from the component
    input_defs = getattr(component, 'inputs', [])
    
    # Create a map of input names to definitions
    input_def_map = {inp.name: inp for inp in input_defs}
    
    # Validate each input
    for input_name, input_value in inputs.items():
        # Skip if the input is not defined
        if input_name not in input_def_map:
            validated_inputs[input_name] = input_value
            continue
            
        input_def = input_def_map[input_name]
        
        # Skip validation for handle inputs
        if getattr(input_def, 'is_handle', False):
            validated_inputs[input_name] = input_value
            continue
            
        # Get input type and validation rules
        input_type = getattr(input_def, 'input_type', 'string')
        validation_rules = getattr(input_def, 'validation_rules', {})
        
        try:
            # Validate the input
            validated_value = validate_input(
                input_name=input_name,
                input_value=input_value,
                input_type=input_type,
                validation_rules=validation_rules
            )
            
            validated_inputs[input_name] = validated_value
            
        except InputValidationError as e:
            # Collect validation errors
            validation_errors.append(e)
            
            # Use the original value for now
            validated_inputs[input_name] = input_value
    
    # If there are validation errors, raise an exception
    if validation_errors:
        error_msg = '; '.join([f"{e.field_name}: {e.message}" for e in validation_errors])
        raise InputValidationError('multiple_fields', error_msg)
    
    return validated_inputs
