import os
import grpc
import logging
from concurrent import futures

from app.core.config import settings
from app.services.workflow_service import WorkflowService
from app.grpc_ import workflow_pb2_grpc
from app.utils.logger import setup_logger

# Set up logging
logger = setup_logger("workflow-service")


def serve():
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

    # Load all components to ensure they're registered
    logger.info("Loading all components...")
    try:
        # Use the component loader to load all components
        from app.utils.component_loader import load_all_components

        loaded_modules = load_all_components()
        logger.info(f"Successfully loaded {len(loaded_modules)} component modules")
    except Exception as e:
        logger.error(f"Error loading components: {str(e)}")

    # Add workflow service to server
    workflow_service = WorkflowService()
    workflow_pb2_grpc.add_WorkflowServiceServicer_to_server(workflow_service, server)

    # Get port from environment or use default
    port = os.getenv("PORT", "50056")
    server.add_insecure_port(f"[::]:{port}")

    # Start server
    server.start()
    print(f"Workflow service started on port {port}")

    # Keep thread alive
    server.wait_for_termination()


if __name__ == "__main__":
    serve()
