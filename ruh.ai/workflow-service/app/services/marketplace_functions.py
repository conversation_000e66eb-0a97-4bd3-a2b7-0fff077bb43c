# app/services/marketplace_functions.py
import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.models.workflow_rating import WorkflowRating
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.kafka.kafka_service import KafkaProducer
from app.helpers.workflow_to_protobuf import (
    _marketplace_listing_to_protobuf,
    _listing_to_marketplace_workflow,
    _workflow_to_protobuf
)

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowMarketplaceFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def getTemplate(
        self, request: workflow_pb2.GetTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetTemplateResponse:
        """
        Retrieve a marketplace listing by its ID (backward compatibility for template API).

        Args:
            request: The request containing the template ID (marketplace listing ID).
            context: The gRPC context for handling the request.

        Returns:
            Response containing the marketplace listing details if found.
        """
        db = self.get_db()
        logger.info("get_marketplace_listing_request", listing_id=request.id)
        try:
            # Get marketplace listing instead of template
            marketplace_listing = (
                db.query(WorkflowMarketplaceListing)
                .filter(WorkflowMarketplaceListing.id == request.id)
                .first()
            )
            if marketplace_listing is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Marketplace listing not found")
                return workflow_pb2.GetTemplateResponse(
                    success=False, message="Marketplace listing not found"
                )

            logger.info("marketplace_listing_retrieved", listing_id=marketplace_listing.id)

            # Convert marketplace listing to protobuf template format for backward compatibility
            template_proto = _marketplace_listing_to_protobuf(marketplace_listing)

            # Check if user_id is provided and if the user has already used this marketplace listing
            is_added = False
            if request.HasField("user_id"):
                # Check if there are any workflows created from this marketplace listing by the user
                user_workflows = (
                    db.query(Workflow)
                    .filter(
                        Workflow.workflow_template_id == marketplace_listing.workflow_id,
                        Workflow.owner_id == request.user_id,
                    )
                    .first()
                )
                is_added = user_workflows is not None
                logger.info(
                    "checking_if_user_used_marketplace_listing",
                    user_id=request.user_id,
                    listing_id=marketplace_listing.id,
                    is_added=is_added,
                )

            # Set the is_added field
            template_proto.is_added = is_added

            print(f"[DEBUG] Marketplace listing retrieved: {template_proto}")
            return workflow_pb2.GetTemplateResponse(
                success=True,
                message=f"Marketplace listing {marketplace_listing.title} retrieved successfully",
                template=template_proto,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.GetTemplateResponse(success=False, message="Internal Server Error")
        finally:
            db.close()

    def getMarketplaceWorkflows(
        self, request: workflow_pb2.GetMarketplaceWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetMarketplaceWorkflowsResponse:
        """
        Retrieves a paginated list of public workflow templates for the marketplace.

        Args:
            request: The request containing pagination, search, and filter parameters
            context: The gRPC context for handling errors

        Returns:
            Response containing the list of marketplace workflows and pagination metadata
        """
        db = self.get_db()
        logger.info(
            "get_marketplace_workflows_request",
            request=request,
        )

        try:
            # Start with a base query for public marketplace listings
            query = db.query(WorkflowMarketplaceListing).filter(
                WorkflowMarketplaceListing.visibility == WorkflowVisibilityEnum.PUBLIC,
                WorkflowMarketplaceListing.status == WorkflowStatusEnum.ACTIVE,
            )

            # Apply search filter if provided
            if request.HasField("search") and request.search:
                search_term = f"%{request.search}%"
                query = query.filter(
                    (WorkflowMarketplaceListing.title.ilike(search_term))
                    | (WorkflowMarketplaceListing.description.ilike(search_term))
                )

            # Apply category filter if provided
            if request.HasField("category") and request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                query = query.filter(WorkflowMarketplaceListing.category == category_value)

            # Apply tags filter if provided
            if request.tags:
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(WorkflowMarketplaceListing.tags.contains(request.tags))

            # Apply sorting
            if request.HasField("sort_by") and request.sort_by:
                sort_by = request.sort_by
                if sort_by == "NEWEST":
                    query = query.order_by(WorkflowMarketplaceListing.created_at.desc())
                elif sort_by == "OLDEST":
                    query = query.order_by(WorkflowMarketplaceListing.created_at.asc())
                elif sort_by == "MOST_POPULAR":
                    query = query.order_by(WorkflowMarketplaceListing.use_count.desc())
                elif sort_by == "HIGHEST_RATED":
                    query = query.order_by(WorkflowMarketplaceListing.average_rating.desc())
                else:
                    # Default to newest
                    query = query.order_by(WorkflowMarketplaceListing.created_at.desc())
            else:
                # Default sorting by newest
                query = query.order_by(WorkflowMarketplaceListing.created_at.desc())

            # Get total count with filters applied
            total = query.count()

            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = min(request.page_size, 100) if request.page_size > 0 else 10

            listings = query.offset((page - 1) * page_size).limit(page_size).all()

            # Calculate pagination metadata
            total_pages = (total + page_size - 1) // page_size if total > 0 else 1
            has_next = page < total_pages
            has_prev = page > 1

            # Convert marketplace listings to protobuf format
            listing_list = [_listing_to_marketplace_workflow(listing) for listing in listings]

            logger.info(
                "marketplace_workflows_retrieved",
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
            )

            return workflow_pb2.GetMarketplaceWorkflowsResponse(
                success=True,
                message="Marketplace workflows retrieved successfully",
                workflows=listing_list,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=has_next,
                has_prev=has_prev,
                next_page=page + 1 if has_next else 0,
                prev_page=page - 1 if has_prev else 0,
            )

        except Exception as e:
            logger.error("get_marketplace_workflows_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to retrieve marketplace workflows: {str(e)}")
            return workflow_pb2.GetMarketplaceWorkflowsResponse(
                success=False,
                message=f"Failed to retrieve marketplace workflows: {str(e)}",
                workflows=[],
                total=0,
                page=request.page,
                page_size=request.page_size,
                total_pages=0,
                has_next=False,
                has_prev=False,
            )
        finally:
            db.close()

    def rateWorkflow(
        self, request: workflow_pb2.RateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.RateWorkflowResponse:
        """
        Rate a workflow and update its average rating.

        Args:
            request: Contains the workflow ID, user ID, and rating value
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated average rating
        """
        db = self.get_db()
        try:
            logger.info(
                "rate_workflow_request",
                workflow_id=request.workflow_id,
                user_id=request.user_id,
                rating=request.rating,
            )

            # Validate rating value (between 1.0 and 5.0)
            if request.rating < 1.0 or request.rating > 5.0:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Rating must be between 1.0 and 5.0")
                return workflow_pb2.RateWorkflowResponse(
                    success=False, message="Rating must be between 1.0 and 5.0", average_rating=0.0
                )

            # Check if workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.RateWorkflowResponse(
                    success=False,
                    message=f"Workflow with ID {request.workflow_id} not found",
                    average_rating=0.0,
                )

            # Check if user has already rated this workflow
            existing_rating = (
                db.query(WorkflowRating)
                .filter(
                    WorkflowRating.workflow_id == request.workflow_id,
                    WorkflowRating.user_id == request.user_id,
                )
                .first()
            )

            if existing_rating:
                # Update existing rating
                old_rating = existing_rating.rating
                existing_rating.rating = request.rating
                existing_rating.updated_at = datetime.now(timezone.utc)
                db.commit()
                logger.info(
                    "updated_workflow_rating",
                    workflow_id=request.workflow_id,
                    user_id=request.user_id,
                    old_rating=old_rating,
                    new_rating=request.rating,
                )
            else:
                # Create new rating
                new_rating = WorkflowRating(
                    workflow_id=request.workflow_id, user_id=request.user_id, rating=request.rating
                )
                db.add(new_rating)
                db.commit()
                logger.info(
                    "created_workflow_rating",
                    workflow_id=request.workflow_id,
                    user_id=request.user_id,
                    rating=request.rating,
                )

            # Calculate new average rating
            ratings = (
                db.query(WorkflowRating)
                .filter(WorkflowRating.workflow_id == request.workflow_id)
                .all()
            )
            total_rating = sum(r.rating for r in ratings)
            average_rating = total_rating / len(ratings) if ratings else 0.0

            # Update workflow with new average rating
            workflow.average_rating = average_rating
            workflow.updated_at = datetime.now(timezone.utc)
            db.commit()

            # If this workflow was created from a source workflow, update marketplace listings for that source
            if workflow.workflow_template_id:
                # Find marketplace listings for the source workflow
                marketplace_listings = (
                    db.query(WorkflowMarketplaceListing)
                    .filter(WorkflowMarketplaceListing.workflow_id == workflow.workflow_template_id)
                    .all()
                )

                for marketplace_listing in marketplace_listings:
                    # Get all ratings for all workflows created from this source workflow
                    listing_workflows = (
                        db.query(Workflow)
                        .filter(Workflow.workflow_template_id == workflow.workflow_template_id)
                        .all()
                    )

                    listing_workflow_ids = [w.id for w in listing_workflows]

                    if listing_workflow_ids:
                        listing_ratings = (
                            db.query(WorkflowRating)
                            .filter(WorkflowRating.workflow_id.in_(listing_workflow_ids))
                            .all()
                        )

                        if listing_ratings:
                            listing_total_rating = sum(r.rating for r in listing_ratings)
                            listing_average_rating = listing_total_rating / len(listing_ratings)

                            marketplace_listing.average_rating = listing_average_rating
                            marketplace_listing.updated_at = datetime.now(timezone.utc)
                            db.commit()

                            logger.info(
                                "updated_marketplace_listing_rating",
                                listing_id=marketplace_listing.id,
                                average_rating=listing_average_rating,
                            )

            return workflow_pb2.RateWorkflowResponse(
                success=True,
                message=f"Rating for workflow {workflow.name} updated successfully",
                average_rating=average_rating,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error rating workflow: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.RateWorkflowResponse(
                success=False, message="Failed to update workflow rating", average_rating=0.0
            )
        finally:
            db.close()

    def useWorkflow(
        self, request: workflow_pb2.UseWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UseWorkflowResponse:
        """
        Create a copy of a marketplace workflow for a user and increment the listing's use count.

        Args:
            request: Contains the marketplace listing ID and user ID
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated use count
        """
        db = self.get_db()
        try:
            logger.info(
                "use_workflow_request", listing_id=request.workflow_id, user_id=request.user_id
            )

            # Check if marketplace listing exists and is public/active
            marketplace_listing = (
                db.query(WorkflowMarketplaceListing)
                .filter(
                    WorkflowMarketplaceListing.id == request.workflow_id,
                    WorkflowMarketplaceListing.visibility == WorkflowVisibilityEnum.PUBLIC,
                    WorkflowMarketplaceListing.status == WorkflowStatusEnum.ACTIVE,
                )
                .first()
            )

            if not marketplace_listing:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Public marketplace listing with ID {request.workflow_id} not found"
                )
                return workflow_pb2.UseWorkflowResponse(
                    success=False,
                    message=f"Public marketplace listing with ID {request.workflow_id} not found",
                    use_count=0,
                )

            # Get the workflow version associated with this listing
            workflow_version = (
                db.query(WorkflowVersion)
                .filter(WorkflowVersion.id == marketplace_listing.workflow_version_id)
                .first()
            )

            if not workflow_version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Workflow version for marketplace listing {request.workflow_id} not found"
                )
                return workflow_pb2.UseWorkflowResponse(
                    success=False,
                    message=f"Workflow version for marketplace listing not found",
                    use_count=0,
                )

            # Get the source workflow to check its customizability
            source_workflow = (
                db.query(Workflow).filter(Workflow.id == marketplace_listing.workflow_id).first()
            )

            # Determine if the cloned workflow should be customizable
            # Only customizable if source workflow allows it
            is_customizable_for_clone = (
                source_workflow.is_customizable if source_workflow else False
            )

            # Create a new workflow based on the marketplace listing
            # Reference the source workflow instead of the marketplace listing
            new_workflow = Workflow(
                name=marketplace_listing.title,
                description=marketplace_listing.description,
                workflow_url=workflow_version.workflow_url,
                builder_url=workflow_version.builder_url,
                start_nodes=workflow_version.start_nodes if workflow_version.start_nodes else [],
                available_nodes=(
                    workflow_version.available_nodes if workflow_version.available_nodes else []
                ),
                owner_id=request.user_id,
                user_ids=[request.user_id],
                owner_type="user",  # Assuming the user is of type "user"
                workflow_template_id=marketplace_listing.workflow_id,  # Reference to source workflow
                template_owner_id=marketplace_listing.listed_by_user_id,
                is_imported=True,  # Mark as imported from marketplace
                visibility=WorkflowVisibilityEnum.PRIVATE,  # Default to private for the copy
                category=marketplace_listing.category,
                tags=marketplace_listing.tags if marketplace_listing.tags else [],
                # Task 1: is_updated - False for cloned workflows (no pending changes initially)
                is_updated=False,
                # Task 2: is_customizable - Check source workflow customizability
                is_customizable=is_customizable_for_clone,  # Based on source workflow
                status=WorkflowStatusEnum.ACTIVE,
            )

            db.add(new_workflow)
            db.flush()  # Get the workflow ID

            # Create v1 version for the new workflow
            v1_version = WorkflowVersion(
                workflow_id=new_workflow.id,
                version_number="1.0.0",
                name=new_workflow.name,
                description=new_workflow.description,
                workflow_url=workflow_version.workflow_url,
                builder_url=workflow_version.builder_url,
                start_nodes=workflow_version.start_nodes if workflow_version.start_nodes else [],
                available_nodes=(
                    workflow_version.available_nodes if workflow_version.available_nodes else []
                ),
                category=marketplace_listing.category,
                tags=marketplace_listing.tags if marketplace_listing.tags else [],
                changelog="Initial version created from marketplace listing",
                is_customizable=is_customizable_for_clone,
            )

            db.add(v1_version)
            db.flush()  # Get the version ID

            # Set the current_version_id in the workflow
            new_workflow.current_version_id = v1_version.id

            # Increment the marketplace listing's use count
            marketplace_listing.use_count += 1
            marketplace_listing.updated_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(new_workflow)
            db.refresh(marketplace_listing)

            logger.info(
                "workflow_created_from_marketplace",
                new_workflow_id=new_workflow.id,
                marketplace_listing_id=marketplace_listing.id,
                user_id=request.user_id,
                new_use_count=marketplace_listing.use_count,
            )

            return workflow_pb2.UseWorkflowResponse(
                success=True,
                message=f"New workflow created from marketplace listing '{marketplace_listing.title}'",
                use_count=marketplace_listing.use_count,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error using marketplace workflow: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.UseWorkflowResponse(
                success=False,
                message="Failed to create workflow from marketplace listing",
                use_count=0,
            )
        finally:
            db.close()

    def pullUpdatesFromSource(
        self, request: workflow_pb2.PullUpdatesFromSourceRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.PullUpdatesFromSourceResponse:
        """
        Pull updates from the source workflow for a cloned workflow.

        This method updates a cloned workflow with the latest changes from its source workflow
        and resets the is_changes_marketplace flag to False.

        Args:
            request: Contains workflow_id and user_id
            context: gRPC context for error handling

        Returns:
            Response with success status and updated workflow
        """
        db = self.get_db()
        logger.info(
            "pull_updates_from_source_request",
            workflow_id=request.workflow_id,
            user_id=request.user_id,
        )

        try:
            # Get the cloned workflow
            cloned_workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not cloned_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="Workflow not found"
                )

            # Check ownership
            if cloned_workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied. You are not the owner of this workflow.")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="Permission denied"
                )

            # Check if this is a cloned workflow
            if not cloned_workflow.is_imported or not cloned_workflow.workflow_template_id:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("This workflow is not cloned from a source workflow")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="This workflow is not cloned from a source workflow"
                )

            # Get the source workflow
            source_workflow = (
                db.query(Workflow)
                .filter(Workflow.id == cloned_workflow.workflow_template_id)
                .first()
            )
            if not source_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source workflow not found")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="Source workflow not found"
                )

            # Update the cloned workflow with source workflow data
            cloned_workflow.description = source_workflow.description
            cloned_workflow.workflow_url = source_workflow.workflow_url
            cloned_workflow.builder_url = source_workflow.builder_url
            cloned_workflow.start_nodes = source_workflow.start_nodes
            cloned_workflow.category = source_workflow.category
            cloned_workflow.tags = source_workflow.tags
            cloned_workflow.is_updated = False  # Reset the flag to indicate sync is complete
            cloned_workflow.updated_at = datetime.now(timezone.utc)

            db.add(cloned_workflow)
            db.commit()
            db.refresh(cloned_workflow)

            logger.info(
                f"Successfully pulled updates for workflow {cloned_workflow.id} from source {source_workflow.id}"
            )

            return workflow_pb2.PullUpdatesFromSourceResponse(
                success=True,
                message="Successfully pulled updates from source workflow. Your workflow is now in sync.",
                updated_workflow=_workflow_to_protobuf(cloned_workflow, db),
            )

        except Exception as e:
            db.rollback()
            logger.error("pull_updates_from_source_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.PullUpdatesFromSourceResponse(
                success=False, message=f"Failed to pull updates: {str(e)}"
            )
        finally:
            db.close()

    def checkForUpdates(
        self, request: workflow_pb2.CheckForUpdatesRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CheckForUpdatesResponse:
        """
        Check if a cloned workflow has updates available from its source workflow.

        Args:
            request: Contains workflow_id and user_id
            context: gRPC context for error handling

        Returns:
            Response with update availability information
        """
        db = self.get_db()
        logger.info(
            "check_for_updates_request", workflow_id=request.workflow_id, user_id=request.user_id
        )

        try:
            # Get the cloned workflow
            cloned_workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not cloned_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Workflow not found"
                )

            # Check ownership
            if cloned_workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied. You are not the owner of this workflow.")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Permission denied"
                )

            # Check if this is a cloned workflow
            if not cloned_workflow.is_imported or not cloned_workflow.workflow_template_id:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("This workflow is not cloned from a source workflow")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="This workflow is not cloned from a source workflow"
                )

            # Get the source workflow
            source_workflow = (
                db.query(Workflow)
                .filter(Workflow.id == cloned_workflow.workflow_template_id)
                .first()
            )
            if not source_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source workflow not found")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Source workflow not found"
                )

            has_updates = cloned_workflow.is_updated or False
            message = (
                "Updates available from source workflow"
                if has_updates
                else "Workflow is up to date"
            )

            return workflow_pb2.CheckForUpdatesResponse(
                success=True,
                message=message,
                has_updates=has_updates,
                source_workflow_id=cloned_workflow.workflow_template_id,
                last_updated=(
                    cloned_workflow.updated_at.isoformat() if cloned_workflow.updated_at else ""
                ),
                source_last_updated=(
                    source_workflow.updated_at.isoformat() if source_workflow.updated_at else ""
                ),
            )

        except Exception as e:
            logger.error("check_for_updates_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.CheckForUpdatesResponse(
                success=False, message=f"Failed to check for updates: {str(e)}"
            )
        finally:
            db.close()
