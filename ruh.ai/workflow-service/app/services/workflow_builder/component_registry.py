"""
Component registry system.

This module provides a registry for component definitions.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from app.models.workflow_builder.components import ComponentDefinition

# Set up logging
logger = logging.getLogger(__name__)


class ComponentRegistry:
    """
    Registry for component definitions.

    This class provides a singleton registry for component definitions.
    It handles registration, retrieval, and validation of components.
    """

    _instance = None

    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super(ComponentRegistry, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the registry."""
        if not getattr(self, "_initialized", False):
            self._components: Dict[str, Dict[str, ComponentDefinition]] = {}
            self._last_updated = time.time()
            self._initialized = True

    def register_component(self, component_def: ComponentDefinition) -> None:
        """
        Register a component definition.

        Args:
            component_def: The component definition to register.
        """
        # Validate the component definition
        self.validate_component_definition(component_def)

        # Get the category and name
        category = component_def.category
        name = component_def.name

        # Create the category if it doesn't exist
        if category not in self._components:
            self._components[category] = {}

        # Check if the component already exists
        if name in self._components[category]:
            print(
                f"DEBUG: Component '{name}' in category '{category}' already exists. Overwriting."
            )

        # Register the component
        self._components[category][name] = component_def
        self._last_updated = time.time()

        # Log the registration
        print(f"DEBUG: Component '{name}' in category '{category}' registered successfully")

        # Log the registration

    def get_component(self, category: str, name: str) -> ComponentDefinition:
        """
        Get a component definition.

        Args:
            category: The category of the component.
            name: The name of the component.

        Returns:
            The component definition.

        Raises:
            KeyError: If the component doesn't exist.
        """
        if category not in self._components or name not in self._components[category]:
            error_msg = f"Component '{name}' in category '{category}' not found"
            print(error_msg)
            raise KeyError(error_msg)

        return self._components[category][name]

    def get_all_components(self) -> Dict[str, Dict[str, ComponentDefinition]]:
        """
        Get all component definitions.

        Returns:
            A dictionary of component definitions by category and name.
        """
        return self._components

    def clear(self) -> None:
        """Clear the registry."""
        self._components = {}
        self._last_updated = time.time()

    def validate_component_definition(self, component_def: ComponentDefinition) -> bool:
        """
        Validate a component definition.

        Args:
            component_def: The component definition to validate.

        Returns:
            True if the component definition is valid.

        Raises:
            ValueError: If the component definition is invalid.
        """
        # Check required fields
        if not component_def.name:
            error_msg = "Component name is required"
            raise ValueError(error_msg)

        if not component_def.category:
            error_msg = f"Category is required for component '{component_def.name}'"
            raise ValueError(error_msg)

        # Validate inputs
        for i, input_def in enumerate(component_def.inputs):
            if not input_def.name:
                error_msg = (
                    f"Input name is required for input {i} in component '{component_def.name}'"
                )
                raise ValueError(error_msg)

            if not input_def.input_type:
                error_msg = f"Input type is required for input '{input_def.name}' in component '{component_def.name}'"
                raise ValueError(error_msg)

        # Validate outputs
        for i, output_def in enumerate(component_def.outputs):
            if not output_def.name:
                error_msg = (
                    f"Output name is required for output {i} in component '{component_def.name}'"
                )
                raise ValueError(error_msg)

            if not output_def.output_type:
                error_msg = f"Output type is required for output '{output_def.name}' in component '{component_def.name}'"
                raise ValueError(error_msg)

        return True

    def component_count(self, category: Optional[str] = None) -> int:
        """
        Get the number of components in the registry.

        Args:
            category: The category to count components for. If None, count all components.

        Returns:
            The number of components.
        """
        if category is None:
            # Count all components
            return sum(len(components) for components in self._components.values())

        # Count components in the specified category
        if category not in self._components:
            return 0

        return len(self._components[category])

    def get_last_updated(self) -> float:
        """
        Get the timestamp of the last update to the registry.

        Returns:
            The timestamp of the last update.
        """
        return self._last_updated
