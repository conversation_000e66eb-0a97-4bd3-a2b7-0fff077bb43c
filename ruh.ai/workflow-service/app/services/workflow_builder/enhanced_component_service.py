"""
Enhanced component service with improved caching.

This module provides an enhanced version of the component service with
improved caching, background refresh, and circuit breaker integration.
"""

import time
import logging
import asyncio
import traceback
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta

from app.services.workflow_builder.component_service import ComponentService
from app.services.workflow_builder.component_registry import ComponentRegistry
from app.models.workflow_builder.components import ComponentDefinition
from app.utils.workflow_builder.circuit_breaker import circuit_breaker

# Import tracing functions directly to avoid circular imports
import uuid
import contextvars

# Context variables for request tracing
request_id_var = contextvars.ContextVar("request_id", default=None)
correlation_id_var = contextvars.ContextVar("correlation_id", default=None)


def get_request_id() -> Optional[str]:
    """
    Get the current request ID.

    Returns:
        The current request ID, or None if not set.
    """
    return request_id_var.get()


def get_correlation_id() -> Optional[str]:
    """
    Get the current correlation ID.

    Returns:
        The current correlation ID, or None if not set.
    """
    return correlation_id_var.get()


class RequestContext:
    """
    Context manager for request tracing.

    This class provides a context manager for setting request tracing
    context variables in non-request contexts, such as background tasks.
    """

    def __init__(self, request_id: Optional[str] = None, correlation_id: Optional[str] = None):
        """
        Initialize the context manager.

        Args:
            request_id: The request ID to set.
            correlation_id: The correlation ID to set.
        """
        self.request_id = request_id or str(uuid.uuid4())
        self.correlation_id = correlation_id or self.request_id
        self.request_id_token = None
        self.correlation_id_token = None

    def __enter__(self):
        """Enter the context manager."""
        self.request_id_token = request_id_var.set(self.request_id)
        self.correlation_id_token = correlation_id_var.set(self.correlation_id)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager."""
        request_id_var.reset(self.request_id_token)
        correlation_id_var.reset(self.correlation_id_token)


# Set up logging
logger = logging.getLogger(__name__)  # Keep standard logger for backward compatibility


class EnhancedComponentService(ComponentService):
    """
    Enhanced component service with improved caching.

    This class extends the base ComponentService with improved caching,
    background refresh, and circuit breaker integration.
    """

    def __init__(self):
        """Initialize the enhanced component service."""
        super().__init__()
        self.last_refresh_time = datetime.now()
        self.refresh_interval = timedelta(minutes=15)  # Refresh every 15 minutes
        self.refresh_task = None
        self.refreshing = False
        self.refresh_errors = 0
        self.max_refresh_errors = 3
        self.component_access_counts: Dict[str, int] = {}
        self.component_last_access: Dict[str, datetime] = {}

    async def start_background_refresh(self):
        """Start the background refresh task."""
        if self.refresh_task is None or self.refresh_task.done():
            self.refresh_task = asyncio.create_task(self._background_refresh_loop())

    async def stop_background_refresh(self):
        """Stop the background refresh task."""
        if self.refresh_task and not self.refresh_task.done():
            self.refresh_task.cancel()
            try:
                await self.refresh_task
            except asyncio.CancelledError:
                pass

    async def _background_refresh_loop(self):
        """Background refresh loop."""
        try:
            while True:
                # Wait until the refresh interval has elapsed
                now = datetime.now()
                time_since_last_refresh = now - self.last_refresh_time
                if time_since_last_refresh < self.refresh_interval:
                    # Calculate the time to wait
                    wait_time = (self.refresh_interval - time_since_last_refresh).total_seconds()
                    await asyncio.sleep(wait_time)

                # Refresh the components
                with RequestContext():
                    try:
                        await self.refresh_components()
                        self.refresh_errors = 0
                    except Exception as e:
                        self.refresh_errors += 1

                        # If we've had too many errors, stop the refresh task
                        if self.refresh_errors >= self.max_refresh_errors:
                            break

                # Update the last refresh time
                self.last_refresh_time = datetime.now()
        except asyncio.CancelledError:
            raise
        except Exception as e:
            print("Background refresh loop error:", e)

    @circuit_breaker(name="component_discovery", failure_threshold=3, recovery_timeout=60.0)
    async def discover_components(
        self, force_refresh: bool = False
    ) -> Dict[str, Dict[str, ComponentDefinition]]:
        """
        Discover components and register them in the registry.

        Args:
            force_refresh: Whether to force a refresh of the component registry.

        Returns:
            A dictionary of component definitions by category and name.
        """
        request_id = get_request_id()
        correlation_id = get_correlation_id()

        # Log using both the standard logger (for backward compatibility) and the unified logger
        logger.info(
            f"Discovering components (force_refresh={force_refresh})",
            extra={
                "request_id": request_id,
                "correlation_id": correlation_id,
                "force_refresh": force_refresh,
            },
        )

        # If not forcing a refresh and the registry is not empty, return the cached components
        if not force_refresh and self.registry.component_count() > 0:
            # Log using both the standard logger (for backward compatibility) and the unified logger
            logger.info(
                "Using cached component definitions",
                extra={
                    "request_id": request_id,
                    "correlation_id": correlation_id,
                    "component_count": self.registry.component_count(),
                },
            )

            return self.registry.get_all_components()

        # If we're already refreshing, wait for the refresh to complete
        if self.refreshing:
            # Log using both the standard logger (for backward compatibility) and the unified logger
            logger.info(
                "Component refresh already in progress, waiting for completion",
                extra={
                    "request_id": request_id,
                    "correlation_id": correlation_id,
                },
            )

            # Wait for the refresh to complete (with a timeout)
            start_time = time.time()
            while self.refreshing and time.time() - start_time < 30:
                await asyncio.sleep(0.1)

            # If we're still refreshing after the timeout, return the cached components
            if self.refreshing:
                # Log using both the standard logger (for backward compatibility) and the unified logger
                logger.warning(
                    "Component refresh timeout, returning cached components",
                    extra={
                        "request_id": request_id,
                        "correlation_id": correlation_id,
                    },
                )

                return self.registry.get_all_components()

            # Otherwise, return the refreshed components
            return self.registry.get_all_components()

        # Set the refreshing flag
        self.refreshing = True

        try:
            # Clear the registry if forcing a refresh
            if force_refresh:
                # Log using both the standard logger (for backward compatibility) and the unified logger
                logger.info(
                    "Forcing refresh of component registry",
                    extra={
                        "request_id": request_id,
                        "correlation_id": correlation_id,
                    },
                )

                self.registry.clear()

            # Discover Python class components
            self._discover_python_components()

            # Discover MCP components
            self._discover_mcp_components()

            # If no components were found, create a minimal set
            if self.registry.component_count() == 0:
                # Log using both the standard logger (for backward compatibility) and the unified logger
                logger.warning(
                    "No components found. Creating minimal component set.",
                    extra={
                        "request_id": request_id,
                        "correlation_id": correlation_id,
                    },
                )

                self._create_minimal_component_set()

            # Update the last refresh time
            self.last_refresh_time = datetime.now()

            # Log using both the standard logger (for backward compatibility) and the unified logger
            logger.info(
                f"Discovered {self.registry.component_count()} components",
                extra={
                    "request_id": request_id,
                    "correlation_id": correlation_id,
                    "component_count": self.registry.component_count(),
                },
            )

            return self.registry.get_all_components()
        finally:
            # Clear the refreshing flag
            self.refreshing = False

    async def refresh_components(self) -> Dict[str, Dict[str, ComponentDefinition]]:
        """
        Refresh the component registry.

        Returns:
            A dictionary of component definitions by category and name.
        """
        return await self.discover_components(force_refresh=True)

    def get_component(self, category: str, name: str) -> ComponentDefinition:
        """
        Get a component definition.

        Args:
            category: The category of the component.
            name: The name of the component.

        Returns:
            The component definition.

        Raises:
            KeyError: If the component doesn't exist.
        """
        # Update access statistics
        component_key = f"{category}:{name}"
        self.component_access_counts[component_key] = (
            self.component_access_counts.get(component_key, 0) + 1
        )
        self.component_last_access[component_key] = datetime.now()

        return super().get_component(category, name)

    def get_component_access_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Get component access statistics.

        Returns:
            A dictionary of component access statistics.
        """
        stats = {}
        for component_key, count in self.component_access_counts.items():
            category, name = component_key.split(":", 1)
            last_access = self.component_last_access.get(component_key)

            stats[component_key] = {
                "category": category,
                "name": name,
                "access_count": count,
                "last_access": last_access.isoformat() if last_access else None,
            }

        return stats
