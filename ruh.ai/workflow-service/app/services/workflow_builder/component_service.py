"""
Component service.

This module provides a service for managing components.
"""

import importlib
import inspect
import logging
import pkgutil
import traceback
from pathlib import Path
from typing import Dict, Type, Any, List, Tuple, Optional

from app.services.workflow_builder.component_registry import ComponentRegistry
from app.models.workflow_builder.components import (
    ComponentDefinition,
    InputDefinition,
    OutputDefinition,
)
from app.components.core.base_node import BaseNode

# Set up logging
logger = logging.getLogger(__name__)

# Import component package
try:
    import app.components as component_package

    BASE_COMPONENT_DIR = Path(component_package.__file__).parent
except ImportError:
    logger.error("Cannot import components package. Component discovery will fail.")
    component_package = None
    BASE_COMPONENT_DIR = None


class ComponentService:
    """
    Service for managing components.

    This class provides methods for discovering, registering, and retrieving components.
    """

    def __init__(self):
        """Initialize the component service."""
        self.registry = ComponentRegistry()

    async def discover_components(
        self, force_refresh: bool = False
    ) -> Dict[str, Dict[str, ComponentDefinition]]:
        """
        Discover components and register them in the registry.

        Args:
            force_refresh: Whether to force a refresh of the component registry.

        Returns:
            A dictionary of component definitions by category and name.
        """
        # If not forcing a refresh and the registry is not empty, return the cached components
        if not force_refresh and self.registry.component_count() > 0:
            return self.registry.get_all_components()

        # Clear the registry if forcing a refresh
        if force_refresh:
            self.registry.clear()

        # Discover Python class components
        self._discover_python_components()

        # Discover MCP components
        self._discover_mcp_components()

        # If no components were found, create a minimal set
        if self.registry.component_count() == 0:
            self._create_minimal_component_set()

        return self.registry.get_all_components()

    def _discover_python_components(self) -> None:
        """
        Discover Python class components.

        This method scans the components package for BaseNode subclasses and registers them.
        """

        if not BaseNode:
            return

        if not component_package or not BASE_COMPONENT_DIR:
            return

        # Walk the components package
        for _, module_name, _ in pkgutil.walk_packages(
            path=component_package.__path__,
            prefix=component_package.__name__ + ".",
            onerror=lambda x: print(f"Error walking package: {x}"),
        ):
            try:
                # Import the module
                module = importlib.import_module(module_name)

                # Iterate over attributes that are classes defined in this module
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    # Special handling for AI components
                    is_ai_component = "app.components.ai" in module_name and name in [
                        "AgenticAI",
                        "BasicLLMChain",
                        "InformationExtractor",
                        "QuestionAnswerModule",
                        "SentimentAnalyzer",
                        "Summarizer",
                        "Classifier",
                    ]

                    if (
                        obj.__module__ == module_name  # Only classes defined in *this* module
                        and issubclass(obj, BaseNode)  # Must be a subclass of BaseNode
                        and obj is not BaseNode  # Cannot be BaseNode itself
                        and (
                            not getattr(obj, "is_abstract", False) or is_ai_component
                        )  # Skip explicitly abstract classes, but include AI components
                    ):
                        try:
                            # Get the component definition
                            component_def = self._get_component_definition_from_class(obj)

                            # Register the component
                            self.registry.register_component(component_def)

                            # Log successful registration
                            print(f"DEBUG: Component {obj.__name__} registered successfully")
                        except Exception as e:
                            # Log registration error
                            print(f"ERROR: Error registering component {obj.__name__}: {e}")
            except Exception as e:
                print("Error importing module", e)

    def _discover_mcp_components(self) -> None:
        """
        Discover MCP components.

        This method discovers MCP components from the MCP server configs.
        """
        import json
        import os
        from pathlib import Path

        # Path to the MCP server configs file
        config_path = (
            Path(__file__).parent.parent.parent.parent / "services" / "mcp_server_configs.json"
        )

        if not config_path.exists():
            return

        try:
            # Load the MCP server configs
            with open(config_path, "r") as f:
                mcp_configs = json.load(f)

            # Check if the configs contain nodes
            if "nodes" not in mcp_configs or not isinstance(mcp_configs["nodes"], list):
                return

            # Process each node in the configs
            for node in mcp_configs["nodes"]:
                try:
                    # Extract node information
                    node_id = node.get("id")
                    display_name = node.get("display_name")
                    description = node.get("description")
                    server_path = node.get("server_script_path")

                    if not all([node_id, display_name, description, server_path]):
                        continue

                    # Process server tools
                    server_tools = node.get("server_tools", [])
                    if not server_tools:
                        continue

                    # Create a component for each tool
                    for tool in server_tools:
                        tool_id = tool.get("tool_id")
                        tool_name = tool.get("tool_name")
                        tool_description = tool.get("description")
                        endpoint = tool.get("endpoint")
                        input_schema = tool.get("input_schema")
                        output_schema = tool.get("output_schema")

                        if not all([tool_id, tool_name, endpoint, input_schema, output_schema]):
                            continue

                        # Create component name
                        component_name = (
                            f"MCP{tool_name.replace('_', ' ').title().replace(' ', '')}"
                        )

                        # Create inputs from input schema
                        inputs = []
                        if "predefined_fields" in input_schema:
                            for field in input_schema["predefined_fields"]:
                                field_name = field.get("field_name")
                                data_type = field.get("data_type", {})
                                required = field.get("required", False)

                                # Determine input type based on data type
                                input_type = "string"  # Default
                                if data_type.get("type") == "object":
                                    input_type = "dict"
                                elif data_type.get("type") == "array":
                                    input_type = "list"
                                elif data_type.get("type") == "boolean":
                                    input_type = "bool"
                                elif (
                                    data_type.get("type") == "integer"
                                    or data_type.get("type") == "number"
                                ):
                                    input_type = "int"

                                # Create input definition
                                input_def = {
                                    "name": field_name,
                                    "display_name": field_name.replace("_", " ").title(),
                                    "info": data_type.get("description", ""),
                                    "input_type": input_type,
                                    "required": required,
                                    "is_handle": True,  # Allow connections
                                    "value": None,
                                }
                                inputs.append(input_def)

                        # Create outputs from output schema
                        outputs = []
                        if "predefined_fields" in output_schema:
                            for field in output_schema["predefined_fields"]:
                                field_name = field.get("field_name")
                                data_type = field.get("data_type", {})

                                # Determine output type based on data type
                                output_type = "string"  # Default
                                if data_type.get("type") == "object":
                                    output_type = "dict"
                                elif data_type.get("type") == "array":
                                    output_type = "list"
                                elif data_type.get("type") == "boolean":
                                    output_type = "bool"
                                elif (
                                    data_type.get("type") == "integer"
                                    or data_type.get("type") == "number"
                                ):
                                    output_type = "int"

                                # Create output definition
                                output_def = {
                                    "name": field_name,
                                    "display_name": field_name.replace("_", " ").title(),
                                    "output_type": output_type,
                                }
                                outputs.append(output_def)

                        # Create MCP info
                        mcp_info = {
                            "server_id": node_id,
                            "server_path": server_path,
                            "tool_name": tool_name,
                            "tool_id": str(tool_id),
                            "endpoint": endpoint,
                            "input_schema": input_schema,
                            "output_schema": output_schema,
                        }

                        # Create component definition
                        component_def = ComponentDefinition(
                            name=component_name,
                            display_name=f"{display_name} - {tool_name.replace('_', ' ').title()}",
                            description=tool_description or description,
                            category="MCP",
                            icon="Cloud",
                            beta=True,
                            inputs=[InputDefinition(**inp) for inp in inputs],
                            outputs=[OutputDefinition(**out) for out in outputs],
                            is_valid=True,
                            path=f"components.mcp.{component_name.lower()}",
                            type="mcp",
                            mcp_info=mcp_info,
                        )

                        # Register the component
                        self.registry.register_component(component_def)

                except Exception as e:
                    print("Error processing MCP node", e)

        except Exception as e:
            print("Error loading MCP server configs", e)

    def _get_component_definition_from_class(self, cls: Type[BaseNode]) -> ComponentDefinition:
        """
        Get a component definition from a BaseNode subclass.

        Args:
            cls: The BaseNode subclass.

        Returns:
            A ComponentDefinition object.
        """
        # Get the component definition from the class
        definition = cls.get_definition()

        # Check if definition is None (happens for abstract components)
        if definition is None:
            # For AI components that are marked as abstract but should be included
            if cls.category == "AI" and cls.__name__ in [
                "AgenticAI",
                "BasicLLMChain",
                "InformationExtractor",
                "QuestionAnswerModule",
                "SentimentAnalyzer",
                "Summarizer",
                "Classifier",
            ]:
                # Create a definition for the AI component
                definition = {
                    "name": cls.name,
                    "display_name": cls.display_name,
                    "description": cls.description,
                    "category": cls.category,
                    "icon": cls.icon,
                    "beta": getattr(cls, "beta", False),
                    "requires_approval": getattr(cls, "requires_approval", False),
                    "inputs": [inp.model_dump() for inp in cls.inputs],
                    "outputs": [out.model_dump() for out in cls.outputs],
                    "is_valid": True,
                    "is_abstract": False,  # Override abstract flag
                    "path": f"app.components.ai.{cls.name.lower()}",
                }
            else:
                # Skip truly abstract components
                raise ValueError(f"Component {cls.__name__} definition is None")

        # Process inputs to ensure input_types, options, and visibility_rules are properly preserved
        processed_inputs = []
        for inp in definition["inputs"]:
            # Make a copy of the input to avoid modifying the original
            processed_inp = inp.copy()

            # Ensure input_types is a list
            if "input_types" not in processed_inp or processed_inp["input_types"] is None:
                processed_inp["input_types"] = []

            # Ensure options is preserved for dropdown inputs
            if "input_type" in processed_inp and processed_inp["input_type"] == "dropdown":
                if "options" not in processed_inp or processed_inp["options"] is None:
                    processed_inp["options"] = []
                elif not isinstance(processed_inp["options"], list):
                    # Convert to list if it's not already a list
                    processed_inp["options"] = list(processed_inp["options"])

            # Ensure visibility_rules is preserved
            if "visibility_rules" in processed_inp and processed_inp["visibility_rules"]:
                # Make sure it's a list
                if not isinstance(processed_inp["visibility_rules"], list):
                    processed_inp["visibility_rules"] = [processed_inp["visibility_rules"]]

                # Process each rule to ensure it has the required fields
                processed_rules = []
                for rule in processed_inp["visibility_rules"]:
                    if isinstance(rule, dict):
                        # Ensure the rule has the required fields
                        if "field_name" not in rule:
                            logger.warning(f"Visibility rule missing field_name: {rule}")
                            continue
                        if "field_value" not in rule:
                            logger.warning(f"Visibility rule missing field_value: {rule}")
                            continue
                        if "operator" not in rule:
                            rule["operator"] = "equals"  # Default operator
                        processed_rules.append(rule)
                    else:
                        # If it's already a model instance, just use it
                        processed_rules.append(rule)

                processed_inp["visibility_rules"] = processed_rules
            elif "visibility_rules" not in processed_inp:
                processed_inp["visibility_rules"] = []

            # Ensure visibility_logic is set
            if "visibility_logic" not in processed_inp:
                processed_inp["visibility_logic"] = "OR"  # Default logic

            # Ensure requirement_rules is preserved
            if "requirement_rules" in processed_inp and processed_inp["requirement_rules"]:
                # Make sure it's a list
                if not isinstance(processed_inp["requirement_rules"], list):
                    processed_inp["requirement_rules"] = [processed_inp["requirement_rules"]]

                # Process each rule to ensure it has the required fields
                processed_rules = []
                for rule in processed_inp["requirement_rules"]:
                    if isinstance(rule, dict):
                        # Ensure the rule has the required fields
                        if "field_name" not in rule:
                            logger.warning(f"Requirement rule missing field_name: {rule}")
                            continue
                        if "field_value" not in rule:
                            logger.warning(f"Requirement rule missing field_value: {rule}")
                            continue
                        if "operator" not in rule:
                            rule["operator"] = "equals"  # Default operator
                        processed_rules.append(rule)
                    else:
                        # If it's already a model instance, just use it
                        processed_rules.append(rule)

                processed_inp["requirement_rules"] = processed_rules
            elif "requirement_rules" not in processed_inp:
                processed_inp["requirement_rules"] = []

            # Ensure requirement_logic is set
            if "requirement_logic" not in processed_inp:
                processed_inp["requirement_logic"] = "OR"  # Default logic

            processed_inputs.append(processed_inp)

        # Convert to a ComponentDefinition object
        return ComponentDefinition(
            name=definition["name"],
            display_name=definition["display_name"],
            description=definition["description"],
            category=definition["category"],
            icon=definition["icon"],
            beta=definition.get("beta", False),
            inputs=[InputDefinition(**inp) for inp in processed_inputs],
            outputs=[OutputDefinition(**out) for out in definition["outputs"]],
            is_valid=definition.get("is_valid", True),
            path=definition["path"],
            type=definition.get("type"),
            mcp_info=definition.get("mcp_info"),
            interface_issues=definition.get("interface_issues"),
        )

    def _create_minimal_component_set(self) -> None:
        """
        Create a minimal set of components.

        This method creates a minimal set of components when no components are found.
        """

        # Create a minimal StartNode component
        start_node = ComponentDefinition(
            name="StartNode",
            display_name="Start",
            description="The starting point for all workflows.",
            category="Input/Output",
            icon="Play",
            beta=False,
            inputs=[],
            outputs=[OutputDefinition(name="flow", display_name="Flow", output_type="Any")],
            is_valid=True,
            path="app.components.io.start_node",
        )

        # Register the component
        self.registry.register_component(start_node)

    def get_component(self, category: str, name: str) -> ComponentDefinition:
        """
        Get a component definition.

        Args:
            category: The category of the component.
            name: The name of the component.

        Returns:
            The component definition.

        Raises:
            KeyError: If the component doesn't exist.
        """
        return self.registry.get_component(category, name)

    def get_all_components(self) -> Dict[str, Dict[str, ComponentDefinition]]:
        """
        Get all component definitions.

        Returns:
            A dictionary of component definitions by category and name.
        """
        return self.registry.get_all_components()
