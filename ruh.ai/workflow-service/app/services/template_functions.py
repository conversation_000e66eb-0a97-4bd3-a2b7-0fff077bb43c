# app/services/workflow_service.py
import grpc
import json
import structlog
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowTemplate
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
)
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.json_validator import validate_transition_schema
from app.utils.file_upload import GCSUploadService
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.core.config import settings

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowTemplateFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createWorkflowFromTemplate(
        self, request: workflow_pb2.CreateWorkflowFromTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateWorkflowFromTemplateResponse:
        """
        Creates a new workflow by copying from a template.

        Args:
            request: The request containing template_id and owner information
            context: The gRPC context for handling errors

        Returns:
            Response indicating success or failure
        """
        db = self.get_db()
        logger.info("create_workflow_from_template_request", template_id=request.template_id)
        try:
            # Get the template
            template = (
                db.query(WorkflowTemplate)
                .filter(WorkflowTemplate.id == request.template_id)
                .first()
            )
            if not template:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Template with ID {request.template_id} not found")
                return workflow_pb2.CreateWorkflowFromTemplateResponse(
                    success=False, message="Template not found"
                )

            # Create new workflow from template
            new_workflow = Workflow(
                # Primary Fields
                name=template.name,  # Use template name as default
                description=template.description,
                workflow_url=template.workflow_url,
                builder_url=template.builder_url,
                start_nodes=template.start_nodes if template.start_nodes else [],
                # Access Control
                owner_id=request.owner.id,
                user_ids=[request.owner.id],  # Default to owner's ID for new workflows
                owner_type=workflow_pb2.WorkflowOwnerType.Name(request.owner_type).lower(),
                # Template Reference
                workflow_template_id=template.id,  # Reference to the source template
                template_owner_id=template.owner_id,  # Owner of the source template=None,  # New workflow, no parent
                url=None,  # New workflow, no URL
                is_imported=True,  # Mark as imported from template
                # Metadata
                visibility=WorkflowVisibilityEnum.PRIVATE.value,  # Default to private for new workflows
                category=template.category,
                tags=template.tags if template.tags else [],
                status=WorkflowStatusEnum.ACTIVE.value,  # Default to active for new workflows
                version=template.version,  # Use template version
                is_changes_marketplace=False,  # Not a marketplace change by default
            )

            template.use_count += 1

            db.add(new_workflow)
            db.add(template)
            db.commit()
            db.refresh(new_workflow)

            # Update template execution count
            template.execution_count += 1
            db.commit()

            # # Send Kafka notification
            # self.kafka_producer.send_email_event_unified(
            #     email_type=SendEmailTypeEnum.WORKFLOW_CREATED.value,
            #     data={
            #         "emailId": request.owner.email,
            #         "userName": request.owner.full_name,
            #         "userId": request.owner.id,
            #         "fcmToken": request.owner.fcm_token,
            #         "workflowId": new_workflow.id,
            #         "workflowName": new_workflow.name,
            #         "title": "New Workflow Created from Template",
            #         "body": f"Your workflow '{new_workflow.name}' has been created from template successfully.",
            #         "link": f"{settings.FRONTEND_URL}/workflows/{new_workflow.id}",
            #         "logo": f"{settings.FRONTEND_URL}/assets/logo.png",
            #     },
            #     action=["sendNotification", "sendWorkflowEmail"],
            # )

            return workflow_pb2.CreateWorkflowFromTemplateResponse(
                success=True, message="Workflow created from template successfully"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            logger.error("workflow_creation_from_template_failed", error=str(e))
            return workflow_pb2.CreateWorkflowFromTemplateResponse(
                success=False, message="Internal server error"
            )
        finally:
            db.close()

    def getTemplate(
        self, request: workflow_pb2.GetTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetTemplateResponse:
        """
        Retrieve a workflow template by its ID.

        Args:
            request: The request containing the template ID.
            context: The gRPC context for handling the request.

        Returns:
            Response containing the template details if found.
        """
        db = self.get_db()
        logger.info("get_template_request", template_id=request.id)
        try:
            template = db.query(WorkflowTemplate).filter(WorkflowTemplate.id == request.id).first()
            if template is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow template not found")
                return workflow_pb2.GetTemplateResponse(
                    success=False, message="Workflow template not found"
                )

            logger.info("template_retrieved", template_id=template.id)

            # Convert template to protobuf
            template_proto = self._template_to_protobuf(template)

            # Check if user_id is provided and if the user has already added this template
            is_added = False
            if request.HasField("user_id"):
                # Check if there are any workflows created from this template by the user
                user_workflows = (
                    db.query(Workflow)
                    .filter(
                        Workflow.workflow_template_id == template.id,
                        Workflow.owner_id == request.user_id,
                    )
                    .first()
                )
                is_added = user_workflows is not None
                logger.info(
                    "checking_if_user_added_template",
                    user_id=request.user_id,
                    template_id=template.id,
                    is_added=is_added,
                )

            # Set the is_added field
            template_proto.is_added = is_added

            print(f"[DEBUG] Template retrieved: {template_proto}")
            return workflow_pb2.GetTemplateResponse(
                success=True,
                message=f"Workflow template {template.name} retrieved successfully",
                template=template_proto,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.GetTemplateResponse(success=False, message="Internal Server Error")
        finally:
            db.close()


    def _template_to_protobuf(self, template: WorkflowTemplate) -> workflow_pb2.WorkflowTemplate:
        """Convert a WorkflowTemplate model instance to a protobuf WorkflowTemplate message."""
        return workflow_pb2.WorkflowTemplate(
            id=str(template.id),
            name=template.name,
            description=template.description,
            workflow_url=template.workflow_url,
            builder_url=template.builder_url,
            start_nodes=(
                [json.dumps(node) for node in template.start_nodes] if template.start_nodes else []
            ),
            available_nodes=(
                [json.dumps(node) for node in template.available_nodes]
                if template.available_nodes
                else []
            ),
            use_count=template.use_count,
            owner_id=template.owner_id,
            execution_count=template.execution_count,
            category=template.category,
            tags=template.tags if template.tags else [],
            version=template.version,
            status=template.status,
            created_at=template.created_at.isoformat() if template.created_at else "",
            updated_at=template.updated_at.isoformat() if template.updated_at else "",
        )

