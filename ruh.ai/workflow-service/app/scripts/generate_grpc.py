import os
import subprocess
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()


def clone_repository():
    """Clone the repository if it doesn't exist, or update it if it does, using PAT if required."""
    repo_url = os.getenv("REPO_URL")
    git_token = os.getenv("GIT_TOKEN")
    branch = os.getenv("ENV")

    if not repo_url:
        raise ValueError("Environment variable REPO_URL is not set.")

    if git_token:
        repo_url = repo_url.replace("https://", f"https://oauth2:{git_token}@")

    repo_name = repo_url.split("/")[-1].replace(".git", "")
    project_root = Path.cwd() / repo_name

    if not project_root.exists():
        try:
            print(f"Cloning repository: {repo_url} branch: {branch}")
            subprocess.run(["git", "clone", "--branch", branch, repo_url], check=True)
            print(f"Successfully cloned repository: {repo_url}")
        except subprocess.CalledProcessError as e:
            print(f"Error cloning repository: {e}")
            raise
    # else:
    #     print(f"Repository already exists at: {project_root}, updating instead of removing")
    #     try:
    #         # Change to the repository directory
    #         original_dir = os.getcwd()
    #         os.chdir(project_root)

    #         # Fetch the latest changes
    #         subprocess.run(["git", "fetch", "origin", branch], check=True)

    #         # Reset to the latest commit on the branch
    #         subprocess.run(["git", "reset", "--hard", f"origin/{branch}"], check=True)

    #         # Clean any untracked files
    #         subprocess.run(["git", "clean", "-fd"], check=True)

    #         print(f"Successfully updated repository to latest version of branch: {branch}")

    #         # Change back to the original directory
    #         os.chdir(original_dir)
    #     except subprocess.CalledProcessError as e:
    #         print(f"Error updating repository: {e}")
    #         raise
    #     except Exception as e:
    #         print(f"Unexpected error during repository update: {e}")
    #         raise

    return project_root


def generate_grpc_code():
    """Generate gRPC code from proto files."""
    # Get the repository root directory
    project_root = clone_repository()
    proto_dir = project_root
    output_dir = project_root.parent / "app" / "grpc_"

    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create __init__.py in the grpc directory
    (output_dir / "__init__.py").touch()

    # Command to generate gRPC code
    command = [
        "python",
        "-m",
        "grpc_tools.protoc",
        f"--proto_path={proto_dir}",
        f"--python_out={output_dir}",
        f"--grpc_python_out={output_dir}",
        str(proto_dir / "workflow.proto"),  # Required Proto file Name
    ]

    try:
        subprocess.run(command, check=True)
        print("Successfully generated gRPC code")

        # Fix imports in generated files
        for file in output_dir.glob("*pb2*.py"):
            content = file.read_text()
            # Replace relative imports with absolute imports
            content = content.replace("import workflow_pb2", "from app.grpc_ import workflow_pb2")
            file.write_text(content)

        print("Successfully fixed imports in generated files")

    except subprocess.CalledProcessError as e:
        print(f"Error generating gRPC code: {e}")
        raise


if __name__ == "__main__":
    generate_grpc_code()
