"""
<PERSON><PERSON><PERSON> to fix imports after refactoring.

This script updates imports of NodeResult from 'models.context' to 'models.component'.
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple, Set

# Define the root directory
ROOT_DIR = Path(__file__).parent.parent


def find_python_files() -> List[Path]:
    """Find all Python files in the codebase."""
    python_files = []
    for path in ROOT_DIR.glob("**/*.py"):
        # Skip files in the old directory
        if "old" not in path.parts:
            python_files.append(path)
    return python_files


def update_imports_in_file(file_path: Path) -> Tuple[bool, List[str]]:
    """
    Update imports in a file.

    Args:
        file_path: Path to the file to update

    Returns:
        Tuple of (whether changes were made, list of changes)
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return False, []

    original_content = content
    changes = []

    # Update imports of NodeResult from models.context to models.component
    context_import_pattern = r"from\s+models\.context\s+import\s+(.+)"
    for match in re.finditer(context_import_pattern, content):
        imports = match.group(1)

        # Check if NodeResult is in the imports
        if "NodeResult" in imports:
            old_import = match.group(0)

            # Remove NodeResult from the imports
            imports_list = [imp.strip() for imp in imports.split(",")]
            new_imports_list = [imp for imp in imports_list if imp != "NodeResult"]

            # Create new import statement for models.context
            if new_imports_list:
                new_context_import = (
                    f"from app.models.workflow_builder.context import {', '.join(new_imports_list)}"
                )
            else:
                new_context_import = ""

            # Add import for NodeResult from models.component
            new_component_import = ""

            # Replace the old import with the new imports
            if new_context_import:
                new_import = f"{new_context_import}\n{new_component_import}"
            else:
                new_import = new_component_import

            content = content.replace(old_import, new_import)
            changes.append(f"Updated: {old_import} -> {new_import}")

    # Write the updated content back to the file if changes were made
    if content != original_content:
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            return True, changes
        except Exception as e:
            print(f"Error writing file {file_path}: {e}")
            return False, []

    return False, []


def main():
    """Main function to update imports."""
    python_files = find_python_files()
    print(f"Found {len(python_files)} Python files.")

    updated_files = 0
    total_changes = 0

    for file_path in python_files:
        changes_made, changes = update_imports_in_file(file_path)
        if changes_made:
            updated_files += 1
            total_changes += len(changes)
            print(f"Updated {file_path}:")
            for change in changes:
                print(f"  - {change}")

    print(f"\nSummary: Updated {updated_files} files with {total_changes} changes.")


if __name__ == "__main__":
    main()
