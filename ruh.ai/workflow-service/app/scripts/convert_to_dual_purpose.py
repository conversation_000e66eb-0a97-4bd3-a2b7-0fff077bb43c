"""
<PERSON><PERSON><PERSON> to convert components with duplicate inputs to use the dual-purpose input pattern.

This script will:
1. Find components with duplicate inputs (input and input_handle)
2. Update them to use the dual-purpose input pattern
3. Update the build and execute methods to handle the new input structure
"""

import os
import re
import glob
import argparse
from typing import List, Dict, Tuple, Set

# Define the root directory for components
COMPONENTS_DIR = os.path.join(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "components"
)


def find_component_files() -> List[str]:
    """Find all Python files in the components directory."""
    return glob.glob(os.path.join(COMPONENTS_DIR, "**", "*.py"), recursive=True)


def analyze_component_file(file_path: str) -> Dict:
    """
    Analyze a component file to identify duplicate inputs.

    Returns:
        Dict with information about the component and its inputs.
    """
    with open(file_path, "r") as f:
        content = f.read()

    # Extract component name
    component_name_match = re.search(r"class\s+(\w+)\(", content)
    if not component_name_match:
        return None

    component_name = component_name_match.group(1)

    # Skip base components that shouldn't be displayed in the UI
    if component_name in [
        "BaseNode",
        "BaseHITLComponent",
        "BaseDataInteractionComponent",
        "BaseAgentComponent",
    ]:
        return None

    # Find all input definitions
    input_defs = []
    handle_inputs = []

    # Look for HandleInput definitions
    handle_pattern = r"HandleInput\s*\(\s*name\s*=\s*[\"']([^\"']+)[\"']"
    handle_matches = re.finditer(handle_pattern, content)
    for match in handle_matches:
        handle_name = match.group(1)
        handle_inputs.append(handle_name)

    # Look for all input definitions
    input_pattern = r"(?:StringInput|IntInput|FloatInput|BoolInput|DropdownInput|ListInput|DictInput|MultilineInput|CodeInput|FileInput)\s*\(\s*name\s*=\s*[\"']([^\"']+)[\"']"
    input_matches = re.finditer(input_pattern, content)
    for match in input_matches:
        input_name = match.group(1)
        input_defs.append(input_name)

    # Check if the component uses create_dual_purpose_input
    uses_dual_purpose = "create_dual_purpose_input" in content

    # Find potential duplicate inputs
    duplicates = []
    for handle_name in handle_inputs:
        base_name = handle_name.replace("_handle", "")
        if base_name in input_defs:
            duplicates.append((handle_name, base_name))

    # Also check for handle inputs in the build or execute methods
    method_pattern = (
        r"(?:def\s+build|def\s+execute).*?(?:kwargs|inputs)\.get\(\s*[\"']([^\"']+)[\"']"
    )
    method_matches = re.finditer(method_pattern, content, re.DOTALL)

    accessed_inputs = set()
    for match in method_matches:
        accessed_name = match.group(1)
        accessed_inputs.add(accessed_name)

    # Find inputs that are accessed but not defined
    undefined_inputs = accessed_inputs - set(input_defs) - set(handle_inputs)

    # Find handle inputs that are accessed in methods
    accessed_handles = [name for name in accessed_inputs if name.endswith("_handle")]

    return {
        "name": component_name,
        "file_path": file_path,
        "content": content,
        "inputs": input_defs,
        "handle_inputs": handle_inputs,
        "duplicates": duplicates,
        "accessed_inputs": list(accessed_inputs),
        "undefined_inputs": list(undefined_inputs),
        "accessed_handles": accessed_handles,
        "uses_dual_purpose": uses_dual_purpose,
    }


def extract_input_details(content: str, input_name: str) -> Dict:
    """
    Extract details about an input from the component content.

    Args:
        content: The component file content
        input_name: The name of the input to extract details for

    Returns:
        Dict with input details
    """
    # Find the input definition
    input_pattern = rf"(?:HandleInput|StringInput|IntInput|FloatInput|BoolInput|DropdownInput|ListInput|DictInput|MultilineInput|CodeInput|FileInput)\s*\(\s*name\s*=\s*[\"']{input_name}[\"'].*?\)"
    input_match = re.search(input_pattern, content, re.DOTALL)

    if not input_match:
        return None

    input_def = input_match.group(0)

    # Extract input type
    input_type_match = re.match(r"(\w+Input)", input_def)
    if not input_type_match:
        return None

    input_type = input_type_match.group(1)

    # Map input type to dual-purpose type
    input_type_map = {
        "StringInput": "string",
        "IntInput": "int",
        "FloatInput": "float",
        "BoolInput": "bool",
        "DropdownInput": "dropdown",
        "ListInput": "list",
        "DictInput": "dict",
        "MultilineInput": "multiline",
        "CodeInput": "code",
        "FileInput": "file",
        "HandleInput": "string",  # Default for HandleInput
    }

    dual_purpose_type = input_type_map.get(input_type, "string")

    # Extract display name
    display_name_match = re.search(r"display_name\s*=\s*[\"']([^\"']+)[\"']", input_def)
    display_name = display_name_match.group(1) if display_name_match else input_name

    # Extract info
    info_match = re.search(r"info\s*=\s*[\"']([^\"']+)[\"']", input_def)
    info = info_match.group(1) if info_match else None

    # Extract required
    required_match = re.search(r"required\s*=\s*(True|False)", input_def)
    required = required_match.group(1) == "True" if required_match else False

    # Extract value
    value_match = re.search(r"value\s*=\s*([^,\)]+)", input_def)
    value = value_match.group(1) if value_match else None

    # Extract input_types for HandleInput
    input_types_match = re.search(r"input_types\s*=\s*\[(.*?)\]", input_def)
    input_types = input_types_match.group(1) if input_types_match else None

    # Extract options for DropdownInput
    options_match = re.search(r"options\s*=\s*\[(.*?)\]", input_def)
    options = options_match.group(1) if options_match else None

    return {
        "name": input_name,
        "display_name": display_name,
        "input_type": dual_purpose_type,
        "info": info,
        "required": required,
        "value": value,
        "input_types": input_types,
        "options": options,
    }


def convert_to_dual_purpose(component: Dict) -> str:
    """
    Convert a component with duplicate inputs to use the dual-purpose input pattern.

    Args:
        component: The component information

    Returns:
        The updated component content
    """
    content = component["content"]

    # Add import for create_dual_purpose_input if not already present
    if (
        "from app.utils.workflow_builder.input_helpers import create_dual_purpose_input"
        not in content
    ):
        # Find the last import line
        import_pattern = r"(from\s+[\w\.]+\s+import\s+.*?)(?:\n\n|\n\s*class)"
        import_match = re.search(import_pattern, content, re.DOTALL)

        if import_match:
            last_import = import_match.group(1)
            new_imports = f"{last_import}\nfrom app.utils.workflow_builder.input_helpers import create_dual_purpose_input"
            content = content.replace(last_import, new_imports)

    # Process each duplicate input
    for handle_name, direct_name in component["duplicates"]:
        # Extract details from both inputs
        handle_details = extract_input_details(content, handle_name)
        direct_details = extract_input_details(content, direct_name)

        if not handle_details or not direct_details:
            print(f"  Could not extract details for {handle_name} or {direct_name}")
            continue

        # Create the dual-purpose input
        input_types = handle_details.get("input_types", "")
        if not input_types:
            input_types = f'["{direct_details["input_type"]}", "Any"]'

        value = direct_details.get("value", "None")
        if value == "None":
            value = None

        options = direct_details.get("options", None)
        options_param = f", options=[{options}]" if options else ""

        # Combine info from both inputs
        handle_info = handle_details.get("info", "")
        direct_info = direct_details.get("info", "")
        combined_info = direct_info
        if handle_info and direct_info and handle_info != direct_info:
            combined_info = f"{direct_info} Can be connected from another node or entered directly."

        # Create the new input definition
        new_input = f"""create_dual_purpose_input(
            name="{direct_name}",
            display_name="{direct_details['display_name'].replace('(Direct)', '').strip()}",
            input_type="{direct_details['input_type']}",
            required={direct_details['required']},
            info="{combined_info}",
            input_types={input_types}{options_param}"""

        if value is not None:
            new_input += f",\n            value={value}"

        new_input += "\n        )"

        # Find the handle input definition
        handle_pattern = rf"HandleInput\s*\(\s*name\s*=\s*[\"']{handle_name}[\"'].*?\)"
        handle_match = re.search(handle_pattern, content, re.DOTALL)

        if handle_match:
            handle_def = handle_match.group(0)
            # Find the direct input definition
            direct_pattern = rf"(?:StringInput|IntInput|FloatInput|BoolInput|DropdownInput|ListInput|DictInput|MultilineInput|CodeInput|FileInput)\s*\(\s*name\s*=\s*[\"']{direct_name}[\"'].*?\)"
            direct_match = re.search(direct_pattern, content, re.DOTALL)

            if direct_match:
                direct_def = direct_match.group(0)

                # Replace both inputs with the new dual-purpose input
                # First, find the entire input section including comments
                handle_section_pattern = rf"(?:\s*#.*\n)*\s*{re.escape(handle_def)}"
                handle_section_match = re.search(handle_section_pattern, content, re.DOTALL)

                direct_section_pattern = rf"(?:\s*#.*\n)*\s*{re.escape(direct_def)}"
                direct_section_match = re.search(direct_section_pattern, content, re.DOTALL)

                if handle_section_match and direct_section_match:
                    handle_section = handle_section_match.group(0)
                    direct_section = direct_section_match.group(0)

                    # Replace the handle section with the new input
                    new_section = f"        # {direct_details['display_name'].replace('(Direct)', '').strip()} - single input that can be both connected and directly edited\n        {new_input}"
                    content = content.replace(
                        handle_section, new_section, 1
                    )  # Only replace the first occurrence

                    # Remove the direct section
                    content = content.replace(
                        direct_section, "", 1
                    )  # Only replace the first occurrence
                else:
                    print(f"  Could not find input sections for {handle_name} or {direct_name}")
            else:
                print(f"  Could not find direct input definition for {direct_name}")
        else:
            print(f"  Could not find handle input definition for {handle_name}")

    # Update the build and execute methods
    for handle_name, direct_name in component["duplicates"]:
        # Update variable assignments that prioritize handle inputs
        var_pattern = rf"(\w+)\s*=\s*(?:kwargs|inputs)\.get\(\s*[\"']{handle_name}[\"']\s*\)"
        var_replacement = f'\\1 = kwargs.get("{direct_name}")'
        content = re.sub(var_pattern, var_replacement, content)

        # Update variable assignments that use if/else
        var_pattern = rf"(\w+)\s*=\s*{handle_name}\s+if\s+{handle_name}\s+is\s+not\s+None\s+else\s+{direct_name}"
        var_replacement = f"\\1 = {direct_name}"
        content = re.sub(var_pattern, var_replacement, content)

    return content


def main():
    """Main function to convert components to use dual-purpose inputs."""
    parser = argparse.ArgumentParser(description="Convert components to use dual-purpose inputs")
    parser.add_argument("--component", help="Specific component to convert")
    parser.add_argument("--dry-run", action="store_true", help="Don't actually modify files")
    args = parser.parse_args()

    print("Analyzing component files...")
    component_files = find_component_files()
    print(f"Found {len(component_files)} component files.")

    results = []
    for file_path in component_files:
        result = analyze_component_file(file_path)
        if result:
            results.append(result)

    # Filter components with duplicate inputs
    components_with_duplicates = [r for r in results if r and r.get("duplicates")]

    print(f"Found {len(components_with_duplicates)} components with duplicate inputs.")

    # If a specific component was specified, filter to just that one
    if args.component:
        components_with_duplicates = [
            c for c in components_with_duplicates if c["name"] == args.component
        ]
        if not components_with_duplicates:
            print(f"Component '{args.component}' not found or has no duplicate inputs.")
            return

    # Convert each component
    for component in components_with_duplicates:
        print(
            f"Converting {component['name']} ({os.path.relpath(component['file_path'], os.path.dirname(COMPONENTS_DIR))})..."
        )

        # Convert the component
        updated_content = convert_to_dual_purpose(component)

        # Write the updated content back to the file
        if not args.dry_run:
            with open(component["file_path"], "w") as f:
                f.write(updated_content)
            print(f"  Updated {component['name']}")
        else:
            print(f"  Would update {component['name']} (dry run)")

    print("Conversion complete!")


if __name__ == "__main__":
    main()
