"""
Node result models for workflow execution.

This module contains models related to node execution results.
"""

from typing import Dict, Any, Optional
from enum import Enum, auto


class NodeStatus(Enum):
    """
    Status of a node execution.
    """
    SUCCESS = auto()
    ERROR = auto()
    PENDING = auto()
    RUNNING = auto()


class NodeResult:
    """
    Result of a node execution.

    This class represents the result of executing a node in a workflow,
    including the status, outputs, and any error information.
    """

    def __init__(
        self,
        status: NodeStatus,
        outputs: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        execution_time: Optional[float] = None,
    ):
        """
        Initialize a node result.

        Args:
            status: The status of the node execution.
            outputs: The outputs from the node.
            error_message: An error message if the execution failed.
            execution_time: The time taken to execute the node, in seconds.
        """
        self.status = status
        self.outputs = outputs or {}
        self.error_message = error_message
        self.execution_time = execution_time

    @classmethod
    def success(
        cls, outputs: Dict[str, Any], execution_time: Optional[float] = None
    ) -> "NodeResult":
        """
        Create a successful node result.

        Args:
            outputs: The outputs from the node.
            execution_time: The time taken to execute the node, in seconds.

        Returns:
            A NodeResult with SUCCESS status.
        """
        return cls(
            status=NodeStatus.SUCCESS,
            outputs=outputs,
            execution_time=execution_time,
        )

    @classmethod
    def error(
        cls, error_message: str, execution_time: Optional[float] = None
    ) -> "NodeResult":
        """
        Create an error node result.

        Args:
            error_message: The error message.
            execution_time: The time taken to execute the node, in seconds.

        Returns:
            A NodeResult with ERROR status.
        """
        return cls(
            status=NodeStatus.ERROR,
            outputs={},
            error_message=error_message,
            execution_time=execution_time,
        )

    @classmethod
    def pending(cls) -> "NodeResult":
        """
        Create a pending node result.

        Returns:
            A NodeResult with PENDING status.
        """
        return cls(status=NodeStatus.PENDING, outputs={})

    @classmethod
    def running(cls) -> "NodeResult":
        """
        Create a running node result.

        Returns:
            A NodeResult with RUNNING status.
        """
        return cls(status=NodeStatus.RUNNING, outputs={})

    def is_success(self) -> bool:
        """
        Check if the node execution was successful.

        Returns:
            True if the status is SUCCESS, False otherwise.
        """
        return self.status == NodeStatus.SUCCESS

    def is_error(self) -> bool:
        """
        Check if the node execution resulted in an error.

        Returns:
            True if the status is ERROR, False otherwise.
        """
        return self.status == NodeStatus.ERROR

    def is_pending(self) -> bool:
        """
        Check if the node execution is pending.

        Returns:
            True if the status is PENDING, False otherwise.
        """
        return self.status == NodeStatus.PENDING

    def is_running(self) -> bool:
        """
        Check if the node execution is running.

        Returns:
            True if the status is RUNNING, False otherwise.
        """
        return self.status == NodeStatus.RUNNING
