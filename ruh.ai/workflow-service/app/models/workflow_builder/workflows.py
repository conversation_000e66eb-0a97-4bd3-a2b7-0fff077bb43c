"""
Workflow schemas.

This module defines Pydantic models for workflow definitions.
"""

from typing import Dict, List, Any, Optional, Literal
from pydantic import Field, field_validator, model_validator
from enum import Enum

from app.models.workflow_builder.base import (
    BaseSchema,
    IdentifiableSchema,
    NamedSchema,
    PositionedSchema,
)


class NodeType(str, Enum):
    """
    Type of a node in a workflow.

    This enum defines the possible types of a node in a workflow.
    """

    MCP = "mcp"
    COMPONENT = "component"
    AGENT = "agent"


class NodeData(BaseSchema):
    """
    Data for a workflow node.

    This class represents the data field of a node in a workflow.
    It contains the label, type, definition, and configuration of the node.
    """

    label: str = Field(..., description="The label of the node")
    type: NodeType = Field(..., description="The type of the node ('mcp' or 'component')")
    originalType: Optional[str] = Field(None, description="The original type of the node")
    definition: Dict[str, Any] = Field(..., description="The definition of the node")
    config: Dict[str, Any] = Field(
        default_factory=dict, description="The configuration of the node"
    )

    @field_validator("label")
    @classmethod
    def validate_label(cls, v: str) -> str:
        """
        Validate the node label.

        Args:
            v: The label to validate.

        Returns:
            The validated label.

        Raises:
            ValueError: If the label is invalid.
        """
        if not v:
            raise ValueError("Node label cannot be empty")

        return v


class Node(IdentifiableSchema, PositionedSchema):
    """
    A node in a workflow.

    This class represents a node in a workflow, including its ID, type,
    position, and data. Nodes are the building blocks of workflows and
    represent specific components or operations.
    """

    type: Literal["WorkflowNode"] = Field(
        "WorkflowNode", description="The type of the node (always 'WorkflowNode')"
    )
    data: NodeData = Field(..., description="The data of the node")


class Edge(IdentifiableSchema):
    """
    An edge in a workflow.

    This class represents a connection between two nodes in a workflow.
    Edges define the flow of data between nodes and specify which outputs
    are connected to which inputs.
    """

    source: str = Field(..., description="The ID of the source node")
    target: str = Field(..., description="The ID of the target node")
    sourceHandle: Optional[str] = Field(None, description="The handle of the source node")
    targetHandle: Optional[str] = Field(None, description="The handle of the target node")

    @field_validator("source")
    @classmethod
    def validate_source(cls, v: str) -> str:
        """
        Validate the source node ID.

        Args:
            v: The source node ID to validate.

        Returns:
            The validated source node ID.

        Raises:
            ValueError: If the source node ID is invalid.
        """
        if not v:
            raise ValueError("Source node ID cannot be empty")

        return v

    @field_validator("target")
    @classmethod
    def validate_target(cls, v: str) -> str:
        """
        Validate the target node ID.

        Args:
            v: The target node ID to validate.

        Returns:
            The validated target node ID.

        Raises:
            ValueError: If the target node ID is invalid.
        """
        if not v:
            raise ValueError("Target node ID cannot be empty")

        return v


class Workflow(BaseSchema):
    """
    A complete workflow definition.

    This class represents a complete workflow, including its nodes and edges.
    Workflows define a sequence of operations to be performed on data,
    with nodes representing the operations and edges representing the
    flow of data between operations.
    """

    nodes: List[Node] = Field(..., description="The nodes in the workflow")
    edges: List[Edge] = Field(..., description="The edges in the workflow")
    name: str = Field("Untitled Workflow", description="The name of the workflow")
    id: Optional[str] = Field(None, description="The ID of the workflow")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """
        Validate the workflow name.

        Args:
            v: The name to validate.

        Returns:
            The validated name.

        Raises:
            ValueError: If the name is invalid.
        """
        if not v:
            raise ValueError("Workflow name cannot be empty")

        return v

    @model_validator(mode="after")
    def validate_workflow(self) -> "Workflow":
        """
        Validate the workflow.

        This method checks that all nodes have unique IDs, all edges have unique IDs,
        and all edges reference valid nodes.

        Returns:
            The validated workflow.

        Raises:
            ValueError: If the workflow is invalid.
        """
        # Check that all nodes have unique IDs
        node_ids = [node.id for node in self.nodes]
        if len(node_ids) != len(set(node_ids)):
            raise ValueError("All nodes must have unique IDs")

        # Check that all edges have unique IDs
        edge_ids = [edge.id for edge in self.edges]
        if len(edge_ids) != len(set(edge_ids)):
            raise ValueError("All edges must have unique IDs")

        # Check that all edges reference valid nodes
        for edge in self.edges:
            if edge.source not in node_ids:
                raise ValueError(
                    f"Edge {edge.id} references non-existent source node {edge.source}"
                )

            if edge.target not in node_ids:
                raise ValueError(
                    f"Edge {edge.id} references non-existent target node {edge.target}"
                )

        return self
