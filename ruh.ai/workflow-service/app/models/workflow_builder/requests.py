"""
API request and response schemas.

This module defines Pydantic models for API requests and responses.
"""

from typing import Dict, List, Any, Optional
from pydantic import Field, field_validator

from app.models.workflow_builder.base import (
    BaseSchema,
    IdentifiableSchema,
    PositionedSchema,
)
from app.models.workflow_builder.workflows import Workflow, Node, Edge


class RequestNode(IdentifiableSchema, PositionedSchema):
    """
    A node in a workflow request.

    This class represents a node in a workflow request.
    It is used when receiving node data from the frontend.
    """

    type: str = Field(..., description="The type of the node")
    data: Dict[str, Any] = Field(..., description="The data of the node")

    @field_validator("type")
    @classmethod
    def validate_type(cls, v: str) -> str:
        """
        Validate the node type.

        Args:
            v: The type to validate.

        Returns:
            The validated type.

        Raises:
            ValueError: If the type is invalid.
        """
        if not v:
            raise ValueError("Node type cannot be empty")

        return v


class RequestEdge(IdentifiableSchema):
    """
    An edge in a workflow request.

    This class represents an edge in a workflow request.
    It is used when receiving edge data from the frontend.
    """

    source: str = Field(..., description="The ID of the source node")
    target: str = Field(..., description="The ID of the target node")
    sourceHandle: Optional[str] = Field(None, description="The handle of the source node")
    targetHandle: Optional[str] = Field(None, description="The handle of the target node")

    @field_validator("source")
    @classmethod
    def validate_source(cls, v: str) -> str:
        """
        Validate the source node ID.

        Args:
            v: The source node ID to validate.

        Returns:
            The validated source node ID.

        Raises:
            ValueError: If the source node ID is invalid.
        """
        if not v:
            raise ValueError("Source node ID cannot be empty")

        return v

    @field_validator("target")
    @classmethod
    def validate_target(cls, v: str) -> str:
        """
        Validate the target node ID.

        Args:
            v: The target node ID to validate.

        Returns:
            The validated target node ID.

        Raises:
            ValueError: If the target node ID is invalid.
        """
        if not v:
            raise ValueError("Target node ID cannot be empty")

        return v


class WorkflowExecutionRequest(BaseSchema):
    """
    Request model for workflow execution.

    This class represents a request to execute a workflow.
    It contains the nodes and edges of the workflow, as well as
    metadata about the workflow and execution.
    """

    nodes: List[RequestNode] = Field(..., description="The nodes in the workflow")
    edges: List[RequestEdge] = Field(..., description="The edges in the workflow")
    workflow_name: str = Field("Untitled Workflow", description="The name of the workflow")
    workflow_id: Optional[str] = Field(None, description="The ID of the workflow")
    execution_id: Optional[str] = Field(None, description="The ID of the execution")

    @field_validator("workflow_name")
    @classmethod
    def validate_workflow_name(cls, v: str) -> str:
        """
        Validate the workflow name.

        Args:
            v: The name to validate.

        Returns:
            The validated name.

        Raises:
            ValueError: If the name is invalid.
        """
        if not v:
            raise ValueError("Workflow name cannot be empty")

        return v


class WorkflowSaveRequest(BaseSchema):
    """
    Request model for saving a workflow.

    This class represents a request to save a workflow.
    It contains the workflow to be saved.
    """

    workflow: Workflow = Field(..., description="The workflow to save")


class WorkflowValidationRequest(BaseSchema):
    """
    Request model for validating a workflow.

    This class represents a request to validate a workflow.
    It contains the workflow to be validated.
    """

    workflow: Workflow = Field(..., description="The workflow to validate")


class WorkflowSaveAndExecuteRequest(BaseSchema):
    """
    Request model for saving and executing a workflow.

    This class represents a request to save and execute a workflow.
    It contains the workflow to be saved and executed, as well as
    inputs for the workflow execution.
    """

    workflow: Workflow = Field(..., description="The workflow to save and execute")
    inputs: Dict[str, Any] = Field(default_factory=dict, description="The inputs for the workflow")


class ValidationResponse(BaseSchema):
    """
    Response model for workflow validation.

    This class represents the response from validating a workflow.
    It contains information about whether the workflow is valid,
    as well as any validation errors or warnings.
    """

    is_valid: bool = Field(..., description="Whether the workflow is valid")
    missing_fields: List[Dict[str, Any]] = Field(
        default_factory=list, description="The missing required fields"
    )
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="The validation errors")
    warnings: List[Dict[str, Any]] = Field(
        default_factory=list, description="The validation warnings"
    )
    error: Optional[str] = Field(None, description="The error message")


class SaveResponse(BaseSchema):
    """
    Response model for saving a workflow.

    This class represents the response from saving a workflow.
    It contains information about whether the save was successful,
    as well as metadata about the saved workflow.
    """

    success: bool = Field(..., description="Whether the save was successful")
    workflow_id: Optional[str] = Field(None, description="The ID of the saved workflow")
    filepath: Optional[str] = Field(None, description="The path to the saved workflow file")
    message: Optional[str] = Field(None, description="A message about the save operation")
    error: Optional[str] = Field(None, description="The error message")


class ExecutionResponse(BaseSchema):
    """
    Response model for executing a workflow.

    This class represents the response from executing a workflow.
    It contains information about whether the execution was successful,
    as well as the results of the execution.
    """

    success: bool = Field(..., description="Whether the execution was successful")
    execution_id: Optional[str] = Field(None, description="The ID of the execution")
    results: Dict[str, Any] = Field(default_factory=dict, description="The execution results")
    error: Optional[str] = Field(None, description="The error message")


class SaveAndExecuteResponse(BaseSchema):
    """
    Response model for saving and executing a workflow.

    This class represents the response from saving and executing a workflow.
    It contains information about whether the operation was successful,
    as well as metadata about the saved workflow and execution.
    """

    success: bool = Field(..., description="Whether the operation was successful")
    workflow_id: Optional[str] = Field(None, description="The ID of the saved workflow")
    filepath: Optional[str] = Field(None, description="The path to the saved workflow file")
    execution_id: Optional[str] = Field(None, description="The ID of the execution")
    message: Optional[str] = Field(None, description="A message about the operation")
    error: Optional[str] = Field(None, description="The error message")
