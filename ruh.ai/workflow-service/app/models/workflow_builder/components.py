"""
Component schemas.

This module defines Pydantic models for component definitions.
"""

from typing import Dict, List, Any, Optional, Union, Literal
from pydantic import Field, field_validator, BaseModel

from app.models.workflow_builder.base import (
    BaseSchema,
    NamedSchema,
    DescribedSchema,
)


class InputVisibilityRule(BaseModel):
    """
    Defines a rule for conditionally showing an input field in the UI.
    The input field will be shown if the 'field_name' (another input)
    has the specified 'field_value' according to the operator.
    """

    field_name: str  # Name of the input field that controls visibility
    field_value: Any  # The value that field_name must have for this rule to pass
    operator: Optional[str] = (
        "equals"  # Operator to use for comparison (equals, not_equals, contains, greater_than, less_than, exists, not_exists)
    )


class InputRequirementRule(BaseModel):
    """
    Defines a rule for conditionally making an input field required.
    The input field will be required if the 'field_name' (another input)
    has the specified 'field_value' according to the operator.
    """

    field_name: str  # Name of the input field that controls requirement
    field_value: Any  # The value that field_name must have for this rule to pass
    operator: Optional[str] = (
        "equals"  # Operator to use for comparison (equals, not_equals, contains, greater_than, less_than, exists, not_exists)
    )


class InputBase(BaseSchema):
    """
    Base class for all input types.

    This class defines the common properties for all input types.
    """

    name: str
    display_name: str
    info: Optional[str] = None
    value: Optional[Any] = None
    options: Optional[List[str]] = None
    real_time_refresh: bool = False
    advanced: bool = False
    input_types: Optional[List[str]] = None
    required: bool = False
    is_handle: bool = False
    is_list: bool = False
    visibility_rules: Optional[List[InputVisibilityRule]] = None
    visibility_logic: Optional[str] = (
        "OR"  # Logic to use when combining visibility rules (OR, AND, COMPLEX)
    )
    requirement_rules: Optional[List[InputRequirementRule]] = None
    requirement_logic: Optional[str] = (
        "OR"  # Logic to use when combining requirement rules (OR, AND, COMPLEX)
    )
    # Pydantic V2: Use model_config instead of class Config
    model_config = {"validate_assignment": True}


class HandleInput(InputBase):
    """
    Input type for connection handles.

    This input type represents a connection point that can be connected to outputs
    from other components.
    """

    input_type: Literal["handle"] = "handle"
    value: Optional[Any] = None
    is_handle: bool = True
    input_types: Optional[List[str]] = ["Any"]  # Accepts any connection type


class DynamicHandleInput(InputBase):
    """
    A special input type that represents a dynamic set of handle inputs.
    This is used for components that need to accept a variable number of inputs.

    The frontend will render this as a set of handle inputs with a button to add more.
    Each handle will have a corresponding direct input in the inspector panel.
    When a handle is connected, the direct input will be disabled and show a 'Connected via handle' indicator.
    """

    input_type: Literal["dynamic_handle"] = "dynamic_handle"
    is_handle: bool = True
    min_handles: int = 1
    max_handles: int = 10
    default_handles: int = 2
    input_types: Optional[List[str]] = ["Any"]  # Accepts any connection type
    base_name: str = "input"  # Base name for generated handles (e.g., input_1, input_2, etc.)
    base_display_name: str = "Input"  # Base display name for generated handles
    allow_direct_input: bool = True  # Whether to allow direct input in the inspector panel
    show_add_button: bool = True  # Whether to show a button to add more inputs
    show_remove_button: bool = True  # Whether to show a button to remove inputs


class StringInput(InputBase):
    """Input type for string values."""

    input_type: Literal["string"] = "string"
    value: Optional[str] = ""


class IntInput(InputBase):
    """Input type for integer values."""

    input_type: Literal["int"] = "int"
    value: Optional[int] = 0


class FloatInput(InputBase):
    """Input type for float values."""

    input_type: Literal["float"] = "float"
    value: Optional[float] = 0.0


class BoolInput(InputBase):
    """Input type for boolean values."""

    input_type: Literal["bool"] = "bool"
    value: bool = False


class DropdownInput(InputBase):
    """Input type for dropdown selection."""

    input_type: Literal["dropdown"] = "dropdown"
    options: List[str] = Field(default_factory=list)


class MultilineInput(InputBase):
    """Input type for multiline text."""

    input_type: Literal["multiline"] = "multiline"
    value: Optional[str] = ""


class DictInput(InputBase):
    """Input type for dictionary values."""

    input_type: Literal["dict"] = "dict"
    value: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ListInput(InputBase):
    """Input type for list values."""

    input_type: Literal["list"] = "list"
    value: Optional[List[Any]] = Field(default_factory=list)
    is_list: bool = True  # Explicitly mark as list type


class CodeInput(InputBase):
    """Input type for code snippets."""

    input_type: Literal["code"] = "code"
    value: Optional[str] = ""
    language: Optional[str] = "python"


class FileInput(InputBase):
    """Input type for file uploads."""

    input_type: Literal["file"] = "file"
    value: Optional[str] = None
    file_types: List[str] = ["*"]
    allow_multiple: bool = False


class PasswordInput(InputBase):
    """Input type for password fields."""

    input_type: Literal["password"] = "password"
    value: Optional[str] = ""


class CredentialInput(InputBase):
    """
    Input type for credentials that can be stored securely.

    This input type allows users to either enter a credential directly
    or reference a credential stored in the credential store by ID.
    """

    input_type: Literal["credential"] = "credential"
    credential_type: str = "api_key"  # Type of credential (api_key, oauth, etc.)
    use_credential_id: bool = False  # Whether to use a credential ID or direct input
    credential_id: Optional[str] = ""  # ID of the credential in the credential store
    value: Optional[str] = ""  # Direct credential value (used if use_credential_id is False)


class HiddenInput(InputBase):
    """Input type for hidden values."""

    input_type: Literal["hidden"] = "hidden"
    value: Optional[Any] = None


class ButtonInput(InputBase):
    """
    A button input type that triggers an action when clicked.

    This is rendered as a button in the UI and is used for actions like
    fetching data, connecting to services, etc.
    """

    input_type: Literal["button"] = "button"
    value: bool = False  # Will be set to True when clicked, then reset to False
    button_text: str = "Click"  # Text to display on the button


class Output(BaseModel):
    """
    Definition of a component output.

    This class represents an output connection point for a component.
    """

    name: str  # Internal name/key (unique within the node)
    display_name: str  # User-facing label for the output handle
    output_type: str  # Data type expected (e.g., 'string', 'dict', 'Tool', 'Agent', 'Any') - for connection validation
    semantic_type: Optional[str] = None  # Semantic meaning of the output (e.g., 'ai_response', 'api_response', 'error_info')
    method: Optional[str] = (
        None  # Optional: Backend method hint (less relevant for pure definition API)
    )

    model_config = {"validate_assignment": True}


class InputDefinition(NamedSchema):
    """
    Definition of a component input.

    This class represents the definition of an input for a component.
    It defines the properties of an input parameter for a component,
    including its type, validation rules, and default value.
    """

    info: Optional[str] = Field(None, description="Additional information about the input")
    input_type: str = Field(
        ..., description="The type of the input (e.g., 'string', 'int', 'bool')"
    )
    input_types: List[str] = Field(
        default_factory=list,
        description="List of acceptable input types for connections",
    )
    options: Optional[List[str]] = Field(
        default_factory=list,
        description="List of options for dropdown inputs",
    )
    required: bool = Field(False, description="Whether the input is required")
    is_handle: bool = Field(False, description="Whether the input is a connection handle")
    is_list: bool = Field(False, description="Whether the input accepts a list of values")
    real_time_refresh: bool = Field(
        False, description="Whether the input should trigger real-time refresh"
    )
    advanced: bool = Field(False, description="Whether the input is an advanced option")
    value: Any = Field(None, description="The default value of the input")
    visibility_rules: Optional[List["InputVisibilityRule"]] = Field(
        default_factory=list,
        description="Rules for conditionally showing this input field in the UI",
    )
    visibility_logic: Optional[str] = Field(
        "OR", description="Logic to use when combining visibility rules (OR, AND, COMPLEX)"
    )
    requirement_rules: Optional[List["InputRequirementRule"]] = Field(
        default_factory=list,
        description="Rules for conditionally making this input field required",
    )
    requirement_logic: Optional[str] = Field(
        "OR", description="Logic to use when combining requirement rules (OR, AND, COMPLEX)"
    )

    @field_validator("input_type")
    @classmethod
    def validate_input_type(cls, v: str) -> str:
        """
        Validate the input type.

        Args:
            v: The input type to validate.

        Returns:
            The validated input type.

        Raises:
            ValueError: If the input type is invalid.
        """
        if not v:
            raise ValueError("Input type cannot be empty")

        return v


class OutputDefinition(NamedSchema):
    """
    Definition of a component output.

    This class represents the definition of an output for a component.
    It defines the properties of an output parameter for a component,
    including its type and method.
    """

    output_type: str = Field(
        ..., description="The type of the output (e.g., 'string', 'int', 'bool')"
    )
    semantic_type: Optional[str] = Field(
        None, description="The semantic meaning of the output (e.g., 'ai_response', 'api_response', 'error_info')"
    )
    method: Optional[str] = Field(None, description="The method used to generate the output")

    @field_validator("output_type")
    @classmethod
    def validate_output_type(cls, v: str) -> str:
        """
        Validate the output type.

        Args:
            v: The output type to validate.

        Returns:
            The validated output type.

        Raises:
            ValueError: If the output type is invalid.
        """
        if not v:
            raise ValueError("Output type cannot be empty")

        return v


class MCPInfo(BaseSchema):
    """
    MCP-specific information for a component.

    This class represents MCP-specific information for a component,
    including server details and tool information.
    """

    server_id: str = Field(..., description="The ID of the MCP server")
    server_path: str = Field(..., description="The path to the MCP server")
    tool_name: str = Field(..., description="The name of the MCP tool")
    tool_id: Optional[str] = Field(None, description="The ID of the MCP tool")
    endpoint: Optional[str] = Field(None, description="The endpoint for the MCP tool")
    input_schema: Dict[str, Any] = Field(..., description="The input schema for the MCP tool")
    output_schema: Dict[str, Any] = Field(..., description="The output schema for the MCP tool")


class ComponentDefinition(DescribedSchema):
    """
    Definition of a component.

    This class represents the definition of a component, including its inputs,
    outputs, and metadata. Components are the building blocks of workflows
    and provide specific functionality that can be connected together.
    """

    category: str = Field(..., description="The category of the component")
    icon: str = Field(..., description="The icon for the component")
    beta: bool = Field(False, description="Whether the component is in beta")
    inputs: List[InputDefinition] = Field(..., description="The inputs for the component")
    outputs: List[OutputDefinition] = Field(..., description="The outputs for the component")
    is_valid: bool = Field(True, description="Whether the component is valid")
    config: Dict[str, Any] = Field(
        default_factory=dict, description="Additional configuration for the component"
    )
    path: str = Field(..., description="The path to the component implementation")
    type: Optional[str] = Field(
        None, description="The type of the component (e.g., 'mcp', 'component')"
    )
    mcp_info: Optional[MCPInfo] = Field(
        None, description="MCP-specific information for the component"
    )
    interface_issues: Optional[List[str]] = Field(
        None, description="Issues with the component interface"
    )

    @field_validator("category")
    @classmethod
    def validate_category(cls, v: str) -> str:
        """
        Validate the category.

        Args:
            v: The category to validate.

        Returns:
            The validated category.

        Raises:
            ValueError: If the category is invalid.
        """
        if not v:
            raise ValueError("Category cannot be empty")

        return v

    @field_validator("path")
    @classmethod
    def validate_path(cls, v: str) -> str:
        """
        Validate the path.

        Args:
            v: The path to validate.

        Returns:
            The validated path.

        Raises:
            ValueError: If the path is invalid.
        """
        if not v:
            raise ValueError("Path cannot be empty")

        return v
