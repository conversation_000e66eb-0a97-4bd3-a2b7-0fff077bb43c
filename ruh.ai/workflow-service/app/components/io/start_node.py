from typing import Dict, Any, List, ClassVar
from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import Output


class StartNode(BaseNode):
    """
    The starting point for all workflows. Only nodes connected to this node
    (directly or indirectly) will be executed.
    """

    name = "StartNode"
    display_name = "Start"
    description = (
        "The starting point for all workflows. Only nodes connected to this node will be executed."
    )
    category = "IO"
    icon = "Play"
    beta = False

    # Add debug print to see if this class is being loaded
    print("DEBUG: StartNode class loaded")

    # No inputs since this is the starting point
    inputs: ClassVar[List[Any]] = []

    # Output that can connect to any node
    outputs: ClassVar[List[Output]] = [
        Output(
            name="flow",
            display_name="Flow",
            output_type="Any",
            info="Connect this to the first node in your workflow.",
        )
    ]

    # Configuration to store collected parameters
    config_schema = {
        "type": "object",
        "properties": {
            "collected_parameters": {
                "type": "object",
                "title": "Collected Parameters",
                "description": "Parameters collected from the workflow execution",
                "additionalProperties": True,
            }
        },
    }
