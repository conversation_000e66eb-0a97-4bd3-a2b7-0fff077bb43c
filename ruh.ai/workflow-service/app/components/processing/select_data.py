from typing import Dict, Any, List, ClassVar
import json
import asyncio
import logging
import time

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import InputBase, DropdownInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.constants.semantic_types import SELECTED_DATA, ERROR_INFO

logger = logging.getLogger(__name__)


class SelectDataComponent(BaseNode):
    """
    Extracts elements from lists or dictionaries.

    This component takes input data (list or dictionary) and extracts elements
    using the specified selector.
    """

    name: ClassVar[str] = "SelectDataComponent"
    display_name: ClassVar[str] = "Select Data"
    description: ClassVar[str] = "Extracts elements from lists or dictionaries."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Filter"

    inputs: ClassVar[List[InputBase]] = [
        # Input data - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="dict",
            required=True,
            info="The data to select from. Can be connected from another node or entered directly.",
            input_types=["list", "dict", "Any"],
        ),
        DropdownInput(
            name="data_type",
            display_name="Data Type",
            options=["Auto-Detect", "List", "Dictionary"],
            value="Auto-Detect",
            info="The type of data structure to select from.",
        ),
        DropdownInput(
            name="search_mode",
            display_name="Search Mode",
            options=["Exact Path", "Smart Search"],
            value="Exact Path",
            info="Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).",
        ),
        DropdownInput(
            name="field_matching_mode",
            display_name="Field Matching Mode",
            options=["Auto-detect", "Key-based Only", "Property-based Only"],
            value="Auto-detect",
            info="Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').",
        ),
        # Selector - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="selector",
            display_name="Selector",
            input_type="string",
            required=True,
            info="For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script' finds object where property_name='script'). Can be connected from another node or entered directly.",
            input_types=["string"],
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="output_data", display_name="Selected Data", output_type="Any", semantic_type=SELECTED_DATA),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    def _select_from_list(self, data: List, selector: str, search_mode: str = "Exact Path", field_matching_mode: str = "Auto-detect") -> Any:
        """
        Selects elements from a list using index/slice notation or smart search.

        Args:
            data: The list to select from
            selector: Index (e.g., '0') or slice (e.g., '1:5') for exact path, or field name for smart search
            search_mode: "Exact Path" for traditional behavior, "Smart Search" for recursive field search
            field_matching_mode: "Auto-detect", "Key-based Only", or "Property-based Only"

        Returns:
            The selected element(s)
        """
        if search_mode == "Smart Search":
            # Use smart search to find field anywhere in the list structure
            result = self._smart_search_field(data, selector, field_matching_mode)
            return result  # Return None if not found, let caller handle it

        # Original exact path logic for numeric indices
        # Check if selector is a slice
        if ":" in selector:
            parts = selector.split(":")
            start = int(parts[0]) if parts[0] else None
            end = int(parts[1]) if parts[1] else None
            return data[start:end]
        else:
            # Single index
            return data[int(selector)]

    def _select_from_dict(self, data: Dict, selector: str, search_mode: str = "Exact Path", field_matching_mode: str = "Auto-detect") -> Any:
        """
        Selects elements from a dictionary using key or path notation, smart search, or property-based matching.

        Args:
            data: The dictionary to select from
            selector: Key name or path (e.g., 'user.name') for exact path, field name for smart search, or @ notation for property-based
            search_mode: "Exact Path" for traditional behavior, "Smart Search" for recursive field search
            field_matching_mode: "Auto-detect", "Key-based Only", or "Property-based Only"

        Returns:
            The selected element
        """
        if search_mode == "Smart Search":
            # Use smart search to find field anywhere in the structure
            result = self._smart_search_field(data, selector, field_matching_mode)
            return result  # Return None if not found, let caller handle it

        # Handle @ notation in Exact Path mode
        if "@" in selector:
            path_parts, property_name = self._parse_at_notation_path(selector)

            # Navigate to the target location using path_parts
            current = data
            for i, key in enumerate(path_parts):
                if isinstance(current, dict) and key in current:
                    current = current[key]
                elif isinstance(current, list) and key.isdigit():
                    # Handle array index in path
                    index = int(key)
                    if 0 <= index < len(current):
                        current = current[index]
                    else:
                        raise IndexError(f"Index {index} out of range for array at step {i + 1} in path '{selector}'")
                else:
                    raise KeyError(f"Key '{key}' not found in path '{selector}' at step {i + 1}")

            # Now search for property-based field in the current location
            if property_name:
                result = self._search_property_based_in_structure(current, property_name)
                if result is None:
                    raise KeyError(f"Property-based field '@{property_name}' not found in structure")
                return result

        # Original exact path logic
        if "." in selector:
            # Path notation (e.g., 'user.name')
            current = data
            for i, key in enumerate(selector.split(".")):
                if isinstance(current, dict) and key in current:
                    current = current[key]

                    # If we encounter a JSON string, try to parse it
                    if isinstance(current, str) and self._looks_like_json(current):
                        try:
                            import json
                            # Try to fix malformed JSON by adding missing braces
                            json_str = self._fix_malformed_json(current)
                            parsed = json.loads(json_str)
                            current = parsed
                            logger.debug(f"Parsed JSON string at step {i + 1} in path '{selector}', new type: {type(current).__name__}")
                        except (json.JSONDecodeError, ValueError) as e:
                            # If it's not valid JSON, continue with the string
                            logger.debug(f"Value at step {i + 1} looks like JSON but failed to parse: {e}, continuing as string")
                            pass

                else:
                    raise KeyError(f"Key '{key}' not found in path '{selector}'")
            return current
        else:
            # Simple key
            value = data[selector]

            # If the value is a JSON string, try to parse it
            if isinstance(value, str) and self._looks_like_json(value):
                try:
                    import json
                    # Try to fix malformed JSON by adding missing braces
                    json_str = self._fix_malformed_json(value)
                    parsed = json.loads(json_str)
                    logger.debug(f"Parsed JSON string for key '{selector}', new type: {type(parsed).__name__}")
                    return parsed
                except (json.JSONDecodeError, ValueError) as e:
                    # If it's not valid JSON, return the string as-is
                    logger.debug(f"Value for key '{selector}' looks like JSON but failed to parse: {e}, returning as string")
                    pass

            return value

    def _smart_search_field(self, data: Any, field_name: str, field_matching_mode: str = "Auto-detect") -> Any:
        """
        Recursively search for a field name anywhere in the data structure.

        Args:
            data: The data structure to search in
            field_name: The field name to search for
            field_matching_mode: "Auto-detect", "Key-based Only", or "Property-based Only"

        Returns:
            The value of the first occurrence of the field, or None if not found
        """
        logger.debug(f"Smart searching for field '{field_name}' in {type(data).__name__} with mode '{field_matching_mode}'")

        if isinstance(data, dict):
            # Key-based search
            if field_matching_mode in ["Auto-detect", "Key-based Only"]:
                if field_name in data:
                    logger.debug(f"Found field '{field_name}' directly in dictionary (key-based)")
                    return data[field_name]

            # Property-based search in dictionary
            if field_matching_mode in ["Auto-detect", "Property-based Only"]:
                property_result = self._search_property_based_in_structure(data, field_name)
                if property_result is not None:
                    logger.debug(f"Found field '{field_name}' in dictionary (property-based)")
                    return property_result

            # Recursively search in all values (respecting field matching mode)
            for key, value in data.items():
                result = self._smart_search_field(value, field_name, field_matching_mode)
                if result is not None:
                    logger.debug(f"Found field '{field_name}' in nested structure under key '{key}'")
                    return result

        elif isinstance(data, list):
            # Property-based search in list
            if field_matching_mode in ["Auto-detect", "Property-based Only"]:
                mcp_result = self._search_mcp_property_structure(data, field_name)
                if mcp_result is not None:
                    logger.debug(f"Found field '{field_name}' in list (property-based)")
                    return mcp_result

            # Recursively search in each item of the list (respecting field matching mode)
            for i, item in enumerate(data):
                result = self._smart_search_field(item, field_name, field_matching_mode)
                if result is not None:
                    logger.debug(f"Found field '{field_name}' in list item at index {i}")
                    return result

        # Field not found in this branch
        return None

    def _parse_at_notation_path(self, selector: str) -> tuple:
        """
        Parse a selector path that may contain @ notation for property-based lookups.

        Examples:
        - "result.@script" → (["result"], "script")
        - "config.settings.@environment" → (["config", "settings"], "environment")
        - "result.0.@script" → (["result", "0"], "script")
        - "user.name" → (["user", "name"], None)

        Args:
            selector: The selector path to parse

        Returns:
            Tuple of (path_parts, property_name) where property_name is None if no @ notation
        """
        parts = selector.split(".")
        property_name = None
        path_parts = []

        for part in parts:
            if part.startswith("@"):
                # This is a property-based lookup
                property_name = part[1:]  # Remove the @
                break
            else:
                path_parts.append(part)

        return path_parts, property_name

    def _search_property_based_in_structure(self, data: Any, property_name: str) -> Any:
        """
        Search for a property-based field in a data structure.

        This looks for objects with property_name field matching the target
        and returns the corresponding data field value.

        Args:
            data: The data structure to search in
            property_name: The property name to find

        Returns:
            The data value for the matching property_name, or None if not found
        """
        if isinstance(data, list):
            # Search through array for property-based structure
            for item in data:
                if (isinstance(item, dict) and
                    "property_name" in item and
                    "data" in item and
                    item["property_name"] == property_name):
                    logger.debug(f"Found property-based field '{property_name}' with data: {type(item['data']).__name__}")
                    return item["data"]
        elif isinstance(data, dict):
            # Check if this dict itself has the property structure
            if ("property_name" in data and
                "data" in data and
                data["property_name"] == property_name):
                logger.debug(f"Found property-based field '{property_name}' in dict")
                return data["data"]

        return None

    def _search_mcp_property_structure(self, data: list, field_name: str) -> Any:
        """
        Search for a field in MCP-style property structure.

        MCP components often output arrays like:
        [
            {"data": "value1", "property_name": "field1"},
            {"data": "value2", "property_name": "field2"}
        ]

        Args:
            data: List to search in
            field_name: The property name to find

        Returns:
            The data value for the matching property_name, or None if not found
        """
        for item in data:
            if (isinstance(item, dict) and
                "property_name" in item and
                "data" in item and
                item["property_name"] == field_name):
                logger.debug(f"Found MCP property '{field_name}' with data: {type(item['data']).__name__}")
                return item["data"]
        return None

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the SelectDataComponent.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values:
                - input_data: The data to select from
                - data_type: The type of data structure
                - selector: The selector expression

        Returns:
            A dictionary with either:
                - output_data: The selected data
                - error: An error message if the operation failed
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )

        # Get inputs
        input_data = kwargs.get("input_data")
        data_type = kwargs.get("data_type", "Auto-Detect")
        selector = kwargs.get("selector", "")
        search_mode = kwargs.get("search_mode", "Exact Path")
        field_matching_mode = kwargs.get("field_matching_mode", "Auto-detect")

        # Validate inputs
        if input_data is None:
            return {"error": "Input data is missing. Please connect data to select from."}

        if not selector:
            return {"error": "Selector is missing. Please provide a selector expression."}

        # Auto-detect data type if needed
        if data_type == "Auto-Detect":
            if isinstance(input_data, list):
                data_type = "List"
            elif isinstance(input_data, dict):
                data_type = "Dictionary"
            else:
                return {
                    "error": f"Cannot auto-detect data type for {type(input_data).__name__}. Please specify data type."
                }

        try:
            # Select data based on type
            if data_type == "List":
                if not isinstance(input_data, list):
                    return {"error": f"Expected a list, got {type(input_data).__name__}"}
                result = self._select_from_list(input_data, selector, search_mode, field_matching_mode)
            elif data_type == "Dictionary":
                if not isinstance(input_data, dict):
                    return {"error": f"Expected a dictionary, got {type(input_data).__name__}"}
                result = self._select_from_dict(input_data, selector, search_mode, field_matching_mode)
            else:
                return {"error": f"Unsupported data type: {data_type}"}

            print(f"  Data selected successfully: {result}")
            return {"output_data": result, "error": None}

        except (IndexError, KeyError, ValueError) as e:
            error_msg = f"Selection error: {str(e)}"
            print(f"  {error_msg}")
            return {"output_data": None, "error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            print(f"  {error_msg}")
            return {"output_data": None, "error": error_msg}

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the SelectDataComponent.

        This method extracts elements from lists or dictionaries using the specified selector.

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs from context
            input_data = self.get_input_value("input_data", context)
            data_type = self.get_input_value("data_type", context, "Auto-Detect")
            search_mode = self.get_input_value("search_mode", context, "Exact Path")
            field_matching_mode = self.get_input_value("field_matching_mode", context, "Auto-detect")
            selector = self.get_input_value("selector", context)

            # Adjust selector if data is wrapped in a 'value' field
            original_selector = selector
            selector = self._adjust_selector_for_wrapped_data(input_data, selector)

            # Log input values for debugging
            logger.debug(f"Input data type: {type(input_data).__name__}")
            logger.debug(f"Data type setting: {data_type}")
            logger.debug(f"Search mode: {search_mode}")
            logger.debug(f"Original selector: '{original_selector}'")
            logger.debug(f"Adjusted selector: '{selector}'")

            # Validate inputs
            if input_data is None:
                error_msg = "Input data is missing. Please connect data to select from."
                context.log(error_msg)
                return NodeResult.error(error_message=error_msg)

            if not selector:
                error_msg = "Selector is missing. Please provide a selector expression."
                context.log(error_msg)
                return NodeResult.error(error_message=error_msg)

            # Auto-detect data type if needed
            if data_type == "Auto-Detect":
                if isinstance(input_data, list):
                    data_type = "List"
                    logger.debug("Auto-detected data type: List")
                elif isinstance(input_data, dict):
                    data_type = "Dictionary"
                    logger.debug("Auto-detected data type: Dictionary")
                else:
                    error_msg = f"Cannot auto-detect data type for {type(input_data).__name__}. Please specify data type."
                    context.log(error_msg)
                    return NodeResult.error(error_message=error_msg)

            # Select data based on type
            try:
                if data_type == "List":
                    if not isinstance(input_data, list):
                        error_msg = f"Expected a list, got {type(input_data).__name__}"
                        context.log(error_msg)
                        return NodeResult.error(error_message=error_msg)
                    result = self._select_from_list(input_data, selector, search_mode, field_matching_mode)
                elif data_type == "Dictionary":
                    if not isinstance(input_data, dict):
                        error_msg = f"Expected a dictionary, got {type(input_data).__name__}"
                        context.log(error_msg)
                        return NodeResult.error(error_message=error_msg)
                    result = self._select_from_dict(input_data, selector, search_mode, field_matching_mode)
                else:
                    error_msg = f"Unsupported data type: {data_type}"
                    context.log(error_msg)
                    return NodeResult.error(error_message=error_msg)

                # Log success
                execution_time = time.time() - start_time
                context.log(f"Data selected successfully. Result type: {type(result).__name__}. Time: {execution_time:.2f}s")
                logger.info(f"Data selected successfully. Result type: {type(result).__name__}. Time: {execution_time:.2f}s")

                # Return success result
                return NodeResult.success(
                    outputs={"output_data": result, "error": None},
                    execution_time=execution_time
                )

            except (IndexError, KeyError, ValueError) as e:
                error_msg = f"Selection error: {str(e)}"
                context.log(error_msg)
                logger.error(f"{error_msg}")
                return NodeResult.success(
                    outputs={"output_data": None, "error": error_msg},
                    execution_time=time.time() - start_time
                )

        except Exception as e:
            # Log error
            error_msg = f"Unexpected error: {str(e)}"
            context.log(error_msg)
            logger.error(f"{error_msg}", exc_info=True)

            # Return error result with outputs for consistency
            return NodeResult.success(
                outputs={"output_data": None, "error": error_msg},
                execution_time=time.time() - start_time
            )

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the context.

        This method handles both direct inputs and handle inputs, prioritizing handle inputs.

        Args:
            input_name: The name of the input.
            context: The workflow execution context.
            default: The default value to return if the input is not found.

        Returns:
            The input value, or the default value if not found.
        """
        # Get all inputs for the current node
        node_inputs = context.node_outputs.get(context.current_node_id, {})

        # Check for handle input first (prioritize connected values)
        handle_value = node_inputs.get(f"{input_name}")
        if handle_value is not None:
            return handle_value

        # If no handle input, check for direct input
        direct_value = node_inputs.get(input_name)
        if direct_value is not None:
            return direct_value

        # If neither is found, return the default value
        return default

    def _adjust_selector_for_wrapped_data(self, input_data: Any, selector: str) -> str:
        """
        Adjust the selector if the input data is wrapped in a 'value' field.

        This handles cases where dual-purpose inputs wrap data like:
        {"value": {...}} and the user provides selector "data.script"
        We automatically convert it to "value.data.script"

        Args:
            input_data: The input data structure
            selector: The original selector

        Returns:
            The adjusted selector
        """
        # Check if input_data is wrapped in a 'value' field
        if (isinstance(input_data, dict) and
            len(input_data) == 1 and
            "value" in input_data and
            not selector.startswith("value.")):

            adjusted_selector = f"value.{selector}"
            logger.debug(f"Adjusted selector from '{selector}' to '{adjusted_selector}' for wrapped data")
            return adjusted_selector

        return selector

    def _looks_like_json(self, value: Any) -> bool:
        """
        Check if a string looks like it could be JSON.

        Args:
            value: The string to check

        Returns:
            True if the string looks like JSON, False otherwise
        """
        if not isinstance(value, str):
            return False

        # Remove leading/trailing whitespace
        value = value.strip()

        # Check for standard JSON patterns
        if (value.startswith('{') and value.endswith('}')) or \
           (value.startswith('[') and value.endswith(']')):
            return True

        # Check for malformed JSON that starts with a key (missing opening brace)
        if value.startswith('"') and ':' in value and ('}' in value or ']' in value):
            return True

        return False

    def _fix_malformed_json(self, value: str) -> str:
        """
        Try to fix common malformed JSON patterns.

        Args:
            value: The potentially malformed JSON string

        Returns:
            A fixed JSON string
        """
        value = value.strip()

        # If it starts with a quote and contains colons, it might be missing opening brace
        if value.startswith('"') and ':' in value and not value.startswith('{'):
            # Add missing opening brace
            value = '{' + value
            logger.debug(f"Added missing opening brace to JSON string")

        # Count braces to see if we need to add closing braces
        open_braces = value.count('{')
        close_braces = value.count('}')

        if open_braces > close_braces:
            # Add missing closing braces
            missing_braces = open_braces - close_braces
            value = value + ('}' * missing_braces)
            logger.debug(f"Added {missing_braces} missing closing brace(s)")

        return value
