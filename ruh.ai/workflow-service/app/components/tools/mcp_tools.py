"""
MCPToolsComponent for interacting with MCP tools via Stdio or SSE.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, ClassVar
import httpx

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    StringInput,
    DropdownInput,
    ButtonInput,
    InputVisibilityRule,
    IntInput,
)
from app.models.workflow_builder.components import Output

from app.utils.workflow_builder.schema_conversion import (
    create_input_schema_from_json_schema,
    pydantic_to_node_inputs,
)

logger = logging.getLogger(__name__)


class MCPToolsComponent(BaseNode):
    """
    Component for interacting with MCP tools via Stdio or SSE.

    This component allows connecting to MCP tools using either Stdio or SSE,
    discovering available tools, and calling them with arguments.
    """

    name: ClassVar[str] = "MCPToolsComponent"
    display_name: ClassVar[str] = "MCP Tools"
    description: ClassVar[str] = "Interact with MCP tools via Stdio or SSE."
    category: ClassVar[str] = "Tools"
    icon: ClassVar[str] = "Tool"

    # Static inputs
    inputs: ClassVar[List[InputBase]] = [
        # Connection mode
        DropdownInput(
            name="mode",
            display_name="Connection Mode",
            options=["Stdio", "SSE"],
            value="Stdio",
            info="The mode to use for connecting to MCP tools.",
        ),
        # Stdio command with fetch button
        StringInput(
            name="command",
            display_name="Stdio Command",
            value="",
            info="The command to run for Stdio mode. Enter command and click 'Fetch Tools'.",
            visibility_rules=[InputVisibilityRule(field_name="mode", field_value="Stdio")],
        ),
        # Fetch tools button for Stdio
        ButtonInput(
            name="fetch_stdio_tools",
            display_name="Fetch Tools",
            button_text="Fetch Tools",
            info="Click to connect and fetch tools using the Stdio command.",
            visibility_rules=[InputVisibilityRule(field_name="mode", field_value="Stdio")],
        ),
        # SSE URL with fetch button
        StringInput(
            name="sse_url",
            display_name="SSE URL",
            value="",
            info="The URL of the SSE server. Enter URL and click 'Fetch Tools'.",
            visibility_rules=[InputVisibilityRule(field_name="mode", field_value="SSE")],
        ),
        # Fetch tools button for SSE
        ButtonInput(
            name="fetch_sse_tools",
            display_name="Fetch Tools",
            button_text="Fetch Tools",
            info="Click to connect and fetch tools using the SSE URL.",
            visibility_rules=[InputVisibilityRule(field_name="mode", field_value="SSE")],
        ),
        # Connection status display (hidden from UI but used for internal state)
        StringInput(
            name="connection_status",
            display_name="Connection Status",
            value="Not Connected",
            info="Current connection status.",
            advanced=True,  # Hide in basic view
        ),
        # Tool selection - only show when connected
        DropdownInput(
            name="selected_tool_name",
            display_name="Tool",
            options=[],  # Will be populated dynamically
            value="",
            info="Select the MCP tool to use.",
            visibility_rules=[
                InputVisibilityRule(field_name="connection_status", field_value="Connected")
            ],
        ),
        # Dynamic inputs will be added based on the selected tool
    ]

    # Outputs
    outputs: ClassVar[List[Output]] = [
        Output(name="result", display_name="Result", output_type="dict"),
        Output(name="combined_text", display_name="Combined Text", output_type="string"),
        Output(name="error", display_name="Error", output_type="string"),
    ]

    def __init__(self):
        """Initialize the MCPToolsComponent."""
        # Internal state
        self._client = None
        self._connection_lock = asyncio.Lock()
        self._mcp_tools_list = []
        self._tool_name_options = []
        self._dynamic_inputs = []
        self._last_connection_params = {}
        self._connection_error = None
        self._tool_schemas = {}  # Store tool schemas by name

        # Initialize component attributes
        self.mode = "Stdio"
        self.command = ""
        self.sse_url = ""
        self.selected_tool_name = ""
        self.connection_status = "Not Connected"

    def _restore_internal_state(self, state: Dict[str, Any]) -> None:
        """Restore internal state from a saved state dictionary.

        Args:
            state: Dictionary containing the internal state to restore
        """
        # Restore mode
        if "mode" in state:
            self.mode = state["mode"]

            # Update mode input
            try:
                mode_input = next(
                    (i for i in self.inputs if hasattr(i, "name") and i.name == "mode"),
                    None,
                )
                if mode_input:
                    mode_input.value = self.mode
            except Exception:
                pass

        # Restore connection status
        if "connection_status" in state:
            self.connection_status = state["connection_status"]

            # Update connection status input
            try:
                connection_status_input = next(
                    (
                        i
                        for i in self.inputs
                        if hasattr(i, "name") and i.name == "connection_status"
                    ),
                    None,
                )
                if connection_status_input:
                    connection_status_input.value = self.connection_status
            except Exception:
                pass

        # Restore tool lists and schemas
        if "mcp_tools_list" in state:
            self._mcp_tools_list = state["mcp_tools_list"]

        if "tool_schemas" in state:
            self._tool_schemas = state["tool_schemas"]

        if "tool_name_options" in state:
            self._tool_name_options = state["tool_name_options"]

            # Update tool options in the dropdown
            try:
                tool_input = next(
                    (
                        i
                        for i in self.inputs
                        if hasattr(i, "name") and i.name == "selected_tool_name"
                    ),
                    None,
                )
                if tool_input:
                    tool_input.options = self._tool_name_options
            except Exception:
                pass

    async def _reset_state(self) -> None:
        """Reset the component state when mode changes or when reconnecting."""
        # Reset internal state
        self._mcp_tools_list = []
        self._tool_name_options = []
        self._dynamic_inputs = []
        self._connection_error = None

        # Reset connection status
        self.connection_status = "Not Connected"

        # Update connection status input
        try:
            connection_status_input = next(
                (i for i in self.inputs if hasattr(i, "name") and i.name == "connection_status"),
                None,
            )
            if connection_status_input:
                connection_status_input.value = "Not Connected"
        except Exception:
            pass

        # Reset selected tool
        self.selected_tool_name = ""

        # Update selected tool input
        try:
            tool_input = next(
                (i for i in self.inputs if hasattr(i, "name") and i.name == "selected_tool_name"),
                None,
            )
            if tool_input:
                tool_input.options = []
                tool_input.value = ""
        except Exception:
            pass

        # Disconnect client if connected
        if (
            self._client
            and hasattr(self._client, "disconnect")
            and hasattr(self._client, "is_connected")
        ):
            try:
                if self._client.is_connected:
                    await self._client.disconnect()
            except Exception:
                pass

        self._client = None

    async def update_config(self, **kwargs) -> Dict[str, Any]:
        """
        Update the component configuration based on user input.

        This method is called when the user changes inputs in the UI.
        It handles connection to the MCP server, tool discovery, and dynamic input generation.

        Args:
            **kwargs: The updated input values

        Returns:
            Updated node configuration
        """
        # Special handling for button inputs
        for key in ["fetch_stdio_tools", "fetch_sse_tools"]:
            if key in kwargs and kwargs[key] is True:
                changed_input = key
                setattr(self, key, True)  # Set it to True temporarily
                break
        else:
            # Normal input handling if no button was clicked
            # Determine which input changed
            changed_input = None
            for key, value in kwargs.items():
                if hasattr(self, key) and getattr(self, key) != value:
                    changed_input = key
                    setattr(self, key, value)

            # If no input changed, return current config
            if not changed_input:
                return self._get_current_config()

        # Handle different input changes
        if changed_input == "mode":
            # Mode changed, reset state
            await self._reset_state()

        elif changed_input == "fetch_stdio_tools" and kwargs.get("fetch_stdio_tools") is True:
            # Fetch Stdio Tools button clicked
            await self._reset_state()

            # Set mode to Stdio if not already
            if self.mode != "Stdio":
                self.mode = "Stdio"

                # Update mode input
                try:
                    mode_input = next(
                        (i for i in self.inputs if hasattr(i, "name") and i.name == "mode"),
                        None,
                    )
                    if mode_input:
                        mode_input.value = "Stdio"
                except Exception:
                    pass

            # Connect and fetch tools
            await self._connect_and_fetch_tools()

            # Reset the button state (it's a momentary action)
            setattr(self, "fetch_stdio_tools", False)

            # Also reset the input value
            try:
                button_input = next(
                    (
                        i
                        for i in self.inputs
                        if hasattr(i, "name") and i.name == "fetch_stdio_tools"
                    ),
                    None,
                )
                if button_input:
                    button_input.value = False
            except Exception:
                pass

            # Update tool options in the dropdown
            try:
                tool_input = next(
                    (
                        i
                        for i in self.inputs
                        if hasattr(i, "name") and i.name == "selected_tool_name"
                    ),
                    None,
                )
                if tool_input:
                    tool_input.options = self._tool_name_options
                    tool_input.value = self._tool_name_options[0] if self._tool_name_options else ""
            except Exception:
                pass

        elif changed_input == "fetch_sse_tools" and kwargs.get("fetch_sse_tools") is True:
            # Fetch SSE Tools button clicked
            await self._reset_state()

            # Set mode to SSE if not already
            if self.mode != "SSE":
                self.mode = "SSE"

                # Update mode input
                try:
                    mode_input = next(
                        (i for i in self.inputs if hasattr(i, "name") and i.name == "mode"),
                        None,
                    )
                    if mode_input:
                        mode_input.value = "SSE"
                except Exception:
                    pass

            # Connect and fetch tools
            await self._connect_and_fetch_tools()

            # Disconnect the client after fetching tools to prevent connection issues
            if self._client and hasattr(self._client, "is_connected") and self._client.is_connected:
                try:
                    # Save the current mode before disconnecting
                    current_mode = self.mode

                    await self._client.disconnect()

                    # Restore the mode after disconnecting
                    if self.mode != current_mode:
                        self.mode = current_mode

                        # Update mode input
                        try:
                            mode_input = next(
                                (i for i in self.inputs if hasattr(i, "name") and i.name == "mode"),
                                None,
                            )
                            if mode_input:
                                mode_input.value = current_mode
                        except Exception:
                            pass
                except Exception:
                    pass

            # Reset the button state (it's a momentary action)
            setattr(self, "fetch_sse_tools", False)

            # Also reset the input value
            try:
                button_input = next(
                    (i for i in self.inputs if hasattr(i, "name") and i.name == "fetch_sse_tools"),
                    None,
                )
                if button_input:
                    button_input.value = False
            except Exception:
                pass

            # Update tool options in the dropdown
            try:
                tool_input = next(
                    (
                        i
                        for i in self.inputs
                        if hasattr(i, "name") and i.name == "selected_tool_name"
                    ),
                    None,
                )
                if tool_input:
                    tool_input.options = self._tool_name_options
                    tool_input.value = self._tool_name_options[0] if self._tool_name_options else ""
            except Exception:
                pass

        # Note: Removed handling for refresh_tools, disconnect, clear_stdio_state, and clear_sse_state buttons
        # as they have been removed from the UI

        elif changed_input == "selected_tool_name":
            # Tool selection changed, generate dynamic inputs
            # Save the current mode before processing
            current_mode = self.mode

            # Update the selected_tool_name in the dropdown input
            try:
                tool_input = next(
                    (
                        i
                        for i in self.inputs
                        if hasattr(i, "name") and i.name == "selected_tool_name"
                    ),
                    None,
                )
                if tool_input:
                    tool_input.value = self.selected_tool_name
            except Exception:
                pass

            # Clear existing dynamic inputs before generating new ones
            self._dynamic_inputs = []

            # Generate dynamic inputs for the selected tool
            await self._generate_dynamic_inputs_for_tool()

            # Ensure mode is preserved after tool selection
            if self.mode != current_mode:
                self.mode = current_mode

                # Update mode input
                try:
                    mode_input = next(
                        (i for i in self.inputs if hasattr(i, "name") and i.name == "mode"),
                        None,
                    )
                    if mode_input:
                        mode_input.value = current_mode
                except Exception:
                    pass

        # Final check to ensure mode is preserved
        if changed_input in ["fetch_sse_tools", "fetch_stdio_tools"]:
            # Get the expected mode based on the button clicked
            expected_mode = "SSE" if changed_input == "fetch_sse_tools" else "Stdio"

            # If the mode doesn't match the expected value, fix it
            if self.mode != expected_mode:
                self.mode = expected_mode

                # Update mode input
                try:
                    mode_input = next(
                        (i for i in self.inputs if hasattr(i, "name") and i.name == "mode"),
                        None,
                    )
                    if mode_input:
                        mode_input.value = expected_mode
                except Exception:
                    pass

        # Return updated configuration
        return self._get_current_config()

    async def _reset_state(self, clear_schemas: bool = False) -> None:
        """Reset the component state.

        Args:
            clear_schemas: Whether to clear the stored tool schemas
        """
        # Disconnect existing client
        if self._client:
            try:
                await self._client.disconnect()
            except Exception:
                pass
            self._client = None

        # Save the tool schemas before resetting state (unless we're clearing them)
        saved_schemas = {}
        if not clear_schemas:
            saved_schemas = self._tool_schemas.copy() if hasattr(self, "_tool_schemas") else {}

        # Reset state
        self._mcp_tools_list = []
        self._tool_name_options = []
        self._dynamic_inputs = []
        self._last_connection_params = {}
        self._connection_error = None

        # Restore the saved schemas (if not clearing)
        self._tool_schemas = saved_schemas

        # Reset connection status
        try:
            # Handle both InputBase objects and dictionaries
            connection_status_input = next(
                (i for i in self.inputs if hasattr(i, "name") and i.name == "connection_status"),
                None,
            )
            if connection_status_input:
                connection_status_input.value = "Not Connected"
        except Exception:
            pass

        # Set the attribute directly as well (for visibility rules)
        self.connection_status = "Not Connected"

    async def _connect_and_fetch_tools(self) -> bool:
        """
        Connect to the MCP server and fetch available tools.

        Returns:
            True if successful, False otherwise
        """
        # Only log essential information
        logger.info(f"Connecting to MCP server: mode={self.mode}")

        async with self._connection_lock:
            try:
                # Validate parameters
                if self.mode == "Stdio" and not self.command:
                    logger.error("Stdio command is required but not provided")
                    self._connection_error = "Stdio command is required."
                    self.connection_status = "Connection Failed"
                    return False

                if self.mode == "SSE" and not self.sse_url:
                    logger.error("SSE URL is required but not provided")
                    self._connection_error = "SSE URL is required."
                    self.connection_status = "Connection Failed"
                    return False

                # Create client based on mode
                if self.mode == "Stdio":
                    self._client = MCPStdioClientWrapper()
                elif self.mode == "SSE":
                    self._client = MCPSseClientWrapper()
                else:
                    logger.error(f"Invalid mode: {self.mode}. Expected 'Stdio' or 'SSE'.")
                    self._connection_error = (
                        f"Invalid mode: {self.mode}. Expected 'Stdio' or 'SSE'."
                    )
                    self.connection_status = "Connection Failed"
                    return False

                # Update status to connecting
                self.connection_status = "Connecting..."

                # Update connection status input
                try:
                    connection_status_input = next(
                        (
                            i
                            for i in self.inputs
                            if hasattr(i, "name") and i.name == "connection_status"
                        ),
                        None,
                    )
                    if connection_status_input:
                        connection_status_input.value = "Connecting..."
                except Exception as e:
                    logger.warning(f"Error updating connection status input: {e}")

                # Connect to server
                try:
                    if self.mode == "Stdio":
                        self._mcp_tools_list = await self._client.connect_to_server(self.command)
                    else:  # mode == "SSE"
                        self._mcp_tools_list = await self._client.connect_to_server(
                            self.sse_url, headers=None
                        )

                    logger.info(f"Connected successfully, found {len(self._mcp_tools_list)} tools")
                except Exception as e:
                    logger.error(f"Error connecting to MCP server: {e}")
                    self._connection_error = f"Error connecting to MCP server: {str(e)}"
                    self.connection_status = "Connection Failed"

                    # Update connection status input
                    try:
                        connection_status_input = next(
                            (
                                i
                                for i in self.inputs
                                if hasattr(i, "name") and i.name == "connection_status"
                            ),
                            None,
                        )
                        if connection_status_input:
                            connection_status_input.value = "Connection Failed"
                    except Exception:
                        pass

                    return False

                # Check if we got any tools
                if not self._mcp_tools_list:
                    logger.warning("No tools found on the MCP server")
                    self._connection_error = "No tools found on the MCP server."
                    self.connection_status = "Connected, but no tools found"

                    # Update connection status input
                    try:
                        connection_status_input = next(
                            (
                                i
                                for i in self.inputs
                                if hasattr(i, "name") and i.name == "connection_status"
                            ),
                            None,
                        )
                        if connection_status_input:
                            connection_status_input.value = "Connected, but no tools found"
                    except Exception:
                        pass

                    return True  # Still consider this a successful connection

                # Update status to connected
                self.connection_status = "Connected"

                # Update connection status input
                try:
                    connection_status_input = next(
                        (
                            i
                            for i in self.inputs
                            if hasattr(i, "name") and i.name == "connection_status"
                        ),
                        None,
                    )
                    if connection_status_input:
                        connection_status_input.value = "Connected"
                except Exception:
                    pass

                # Extract tool names and store schemas
                self._tool_name_options = []
                self._tool_schemas = {}  # Clear existing schemas

                for tool in self._mcp_tools_list:
                    try:
                        # Try dictionary access first
                        if isinstance(tool, dict):
                            tool_name = tool.get("name", "")
                            if "input_schema" in tool:
                                self._tool_schemas[tool_name] = tool["input_schema"]
                        else:
                            # Try attribute access
                            tool_name = getattr(tool, "name", str(tool))
                            if hasattr(tool, "input_schema"):
                                self._tool_schemas[tool_name] = tool.input_schema

                        if tool_name:
                            self._tool_name_options.append(tool_name)
                    except Exception:
                        pass

                # Update tool options in the dropdown
                try:
                    tool_input = next(
                        (
                            i
                            for i in self.inputs
                            if hasattr(i, "name") and i.name == "selected_tool_name"
                        ),
                        None,
                    )
                    if tool_input:
                        tool_input.options = self._tool_name_options
                        if self._tool_name_options:
                            tool_input.value = self._tool_name_options[0]
                            self.selected_tool_name = self._tool_name_options[0]
                except Exception:
                    pass

                # Save connection parameters
                self._last_connection_params = {
                    "mode": self.mode,
                    "command": self.command,
                    "sse_url": self.sse_url,
                }

                # Generate dynamic inputs for the selected tool if one was selected
                if self.selected_tool_name:
                    await self._generate_dynamic_inputs_for_tool()

                return True
            except Exception as e:
                logger.error(f"Error connecting to MCP server: {e}")
                self._connection_error = str(e)
                self.connection_status = "Connection Error"

                # Update connection status input
                try:
                    connection_status_input = next(
                        (
                            i
                            for i in self.inputs
                            if hasattr(i, "name") and i.name == "connection_status"
                        ),
                        None,
                    )
                    if connection_status_input:
                        connection_status_input.value = "Connection Error"
                except Exception:
                    pass

                return False

    async def _generate_dynamic_inputs_for_tool(self) -> None:
        """Generate dynamic inputs based on the selected tool."""
        # Clear existing dynamic inputs
        self._dynamic_inputs = []

        # Import required modules
        from app.models.workflow_builder.components import StringInput, IntInput

        # Get selected tool
        if not self.selected_tool_name:
            return

        # Update the selected_tool_name in the dropdown input
        try:
            tool_input = next(
                (i for i in self.inputs if hasattr(i, "name") and i.name == "selected_tool_name"),
                None,
            )
            if tool_input:
                tool_input.value = self.selected_tool_name
        except Exception:
            pass

        # Make sure connection_status is set to Connected
        if self.connection_status != "Connected":
            self.connection_status = "Connected"
            try:
                connection_status_input = next(
                    (
                        i
                        for i in self.inputs
                        if hasattr(i, "name") and i.name == "connection_status"
                    ),
                    None,
                )
                if connection_status_input:
                    connection_status_input.value = "Connected"
            except Exception:
                pass

        # Check if we have a stored schema for this tool
        if self.selected_tool_name in self._tool_schemas:
            input_schema = self._tool_schemas[self.selected_tool_name]

            # Process the stored schema
            try:
                # Ensure the schema has the required structure
                if "type" not in input_schema:
                    input_schema["type"] = "object"

                # Get properties from the schema
                properties = input_schema.get("properties", {})

                # If there are no properties, create a default input
                if not properties:
                    self._dynamic_inputs = [
                        StringInput(
                            name="tool_arg_input",
                            display_name="Input",
                            info="Input for the tool",
                            is_handle=True,
                            input_types=["string", "Any"],
                            value="",
                        )
                    ]
                    return

                # Create Pydantic model from the schema
                schema_model = create_input_schema_from_json_schema(
                    input_schema, f"{self.selected_tool_name}Input"
                )

                # Convert the model to input definitions with is_handle=True
                self._dynamic_inputs = pydantic_to_node_inputs(
                    schema_model, "tool_arg_", make_handles=True
                )

                # If no dynamic inputs were generated, create inputs manually from properties
                if not self._dynamic_inputs and properties:
                    for prop_name, prop_def in properties.items():
                        prop_type = prop_def.get("type", "string")
                        prop_desc = prop_def.get("description", f"Input for {prop_name}")
                        required = prop_name in input_schema.get("required", [])

                        if prop_type == "number" or prop_type == "integer":
                            self._dynamic_inputs.append(
                                IntInput(
                                    name=f"tool_arg_{prop_name}",
                                    display_name=prop_name.capitalize(),
                                    info=prop_desc,
                                    value=prop_def.get("default", 0),
                                    required=required,
                                    is_handle=True,
                                    input_types=["int", "float", "number", "Any"],
                                )
                            )
                        else:  # Default to string for all other types
                            self._dynamic_inputs.append(
                                StringInput(
                                    name=f"tool_arg_{prop_name}",
                                    display_name=prop_name.capitalize(),
                                    info=prop_desc,
                                    value=prop_def.get("default", ""),
                                    required=required,
                                    is_handle=True,
                                    input_types=["string", "text", "Any"],
                                )
                            )

                return
            except Exception as e:
                logger.error(f"Error generating inputs from schema: {str(e)}")
                # Fall through to create default input

        # If we don't have a stored schema or processing it failed, create a default input
        self._dynamic_inputs = [
            StringInput(
                name="tool_arg_input",
                display_name="Input",
                info="Input for the tool",
                is_handle=True,
                input_types=["string", "Any"],
                value="",
            )
        ]

    def _get_current_config(self) -> Dict[str, Any]:
        """
        Get the current component configuration.

        Returns:
            A dictionary with the current configuration
        """
        # Get the current mode
        current_mode = self.mode

        # Make sure the mode input value matches the current mode
        try:
            mode_input = next(
                (i for i in self.inputs if hasattr(i, "name") and i.name == "mode"),
                None,
            )
            if mode_input and mode_input.value != current_mode:
                mode_input.value = current_mode
        except Exception:
            pass

        # Make sure connection_status input value matches the current status
        try:
            connection_status_input = next(
                (i for i in self.inputs if hasattr(i, "name") and i.name == "connection_status"),
                None,
            )
            if connection_status_input and connection_status_input.value != self.connection_status:
                connection_status_input.value = self.connection_status
        except Exception:
            pass

        # Create a clean config without duplicating inputs
        config = {
            # Store essential configuration values
            "mode": current_mode,
            "command": self.command,
            "sse_url": self.sse_url,
            "selected_tool_name": self.selected_tool_name,
            "connection_status": self.connection_status,
            # Include static and dynamic inputs separately
            "static_inputs": self.inputs,
            "dynamic_inputs": self._dynamic_inputs,
            # Include outputs
            "outputs": self.outputs,
            # Store internal state for restoration
            "_internal_state": {
                "mode": current_mode,
                "mcp_tools_list": self._mcp_tools_list,
                "tool_schemas": self._tool_schemas,
                "tool_name_options": self._tool_name_options,
                "connection_status": self.connection_status,
            },
        }

        # Generate combined inputs list for the frontend
        all_inputs = list(self.inputs)

        # Add dynamic inputs if available
        if self._dynamic_inputs:
            all_inputs.extend(self._dynamic_inputs)
        else:
            # Force regeneration of dynamic inputs if we have a selected tool
            if self.selected_tool_name:
                self._dynamic_inputs = [
                    StringInput(
                        name="tool_arg_input",
                        display_name="Input",
                        info="Input for the tool",
                        is_handle=True,
                        input_types=["string", "Any"],
                        value="",
                    )
                ]
                all_inputs.extend(self._dynamic_inputs)
                config["dynamic_inputs"] = self._dynamic_inputs

        # Set the combined inputs list
        config["inputs"] = all_inputs

        # Add connection status
        if self._client and hasattr(self._client, "is_connected") and self._client.is_connected:
            config["status"] = "Connected"
            config["status_color"] = "green"
        elif self._connection_error:
            config["status"] = "Error"
            config["status_color"] = "red"
            config["error"] = self._connection_error
        else:
            config["status"] = "Not Connected"
            config["status_color"] = "yellow"

        return config

    # Legacy build method for backward compatibility
    def build(self, **_) -> Dict[str, Any]:
        """
        Legacy executor for the MCPToolsComponent.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Legacy method for executing the component.

        This method is not implemented for MCPToolsComponent.
        Use the async execute method instead.

        Returns:
            A dictionary with the execution result
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        # This will be called by the execute method if needed
        raise NotImplementedError("MCPToolsComponent only supports the async execute method.")
