from typing import Dict, Any, List, ClassVar
import importlib
import uuid
import warnings

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput, ListInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeR<PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.constants.semantic_types import AI_ANALYSIS, AI_METADATA, ERROR_INFO


class Classifier(BaseAgentComponent):
    """
    Categorizes input text into one of several predefined classes or labels.

    This component takes input text and assigns it to one of the specified categories.
    """

    name: ClassVar[str] = "Classifier"
    display_name: ClassVar[str] = "Text Classifier"
    description: ClassVar[str] = (
        "Categorizes text into one of several predefined classes or labels."
    )

    icon: ClassVar[str] = "Tag"

    # Define component-specific inputs using dual-purpose pattern
    component_inputs: ClassVar[List[InputBase]] = [
        # Text to classify - unified dual-purpose input
        create_dual_purpose_input(
            name="text",
            display_name="Text to Classify",
            input_type="string",
            required=True,
            info="The text that needs to be assigned a category. Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
        # Categories - unified dual-purpose input
        create_dual_purpose_input(
            name="categories",
            display_name="Categories",
            input_type="list",
            required=True,
            info="List of possible categories for classification. Can be connected from another node or entered directly.",
            input_types=["list", "string", "Any"],
            default_value=[],
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="category", display_name="Assigned Category", output_type="string", semantic_type=AI_ANALYSIS),
        Output(name="confidence", display_name="Confidence", output_type="float", semantic_type=AI_METADATA),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute text classification via node-executor-service.

        This is the modern execution method that uses the unified AI executor
        for consistent behavior and improved performance.

        Args:
            context: The workflow execution context

        Returns:
            NodeResult with classification results or error information
        """
        context.log(f"Executing {self.name}...")

        try:
            # Extract inputs from context
            inputs = self._extract_component_inputs(context)

            # Validate required inputs
            validation_result = self._validate_inputs(inputs)
            if not validation_result.is_valid:
                return NodeResult.error(validation_result.error_message)

            # Build request payload for node-executor-service
            payload = {
                "component_type": "classifier",
                "model_provider": inputs.get("model_provider", "OpenAI"),
                "api_key": inputs.get("api_key"),
                "model_name": inputs.get("model_name", "gpt-4o"),
                "temperature": 0.3,  # Lower temperature for more deterministic classification
                "base_url": inputs.get("base_url"),
                "component_inputs": self._build_component_inputs(inputs),
                "request_id": context.request_id or str(uuid.uuid4())
            }

            # Execute via node-executor-service
            result = await self._execute_via_node_executor(payload, context)

            # Process and return results
            if result.get("status") == "success":
                outputs = self._map_outputs(result.get("result", {}))
                return NodeResult.success(outputs)
            else:
                error_msg = result.get("error", "Text classification failed")
                context.log(f"Error: {error_msg}")
                return NodeResult.error(error_msg)

        except Exception as e:
            error_msg = f"Error executing {self.name}: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg)

    def _extract_component_inputs(self, context: WorkflowContext) -> Dict[str, Any]:
        """Extract component inputs from workflow context."""
        return {
            # Model configuration
            "model_provider": self.get_input_value("model_provider", context, "OpenAI"),
            "api_key": self._extract_credential_value(self.get_input_value("api_key", context)),
            "model_name": self.get_input_value("model_name", context, "gpt-4o"),
            "base_url": self.get_input_value("base_url", context),

            # Input data - unified dual-purpose inputs
            "text": self.get_input_value("text", context, ""),
            "categories": self.get_input_value("categories", context, []),
        }

    def _validate_inputs(self, inputs: Dict[str, Any]) -> "ValidationResult":
        """Validate component inputs."""
        from app.core_.base_component import ValidationResult

        # Check API key
        if not inputs.get("api_key"):
            return ValidationResult(
                is_valid=False,
                error_message="API key is required for text classification"
            )

        # Check text input
        text = inputs.get("text", "")
        if not text:
            return ValidationResult(
                is_valid=False,
                error_message="Text to classify is required. Please connect text or provide it directly."
            )

        # Check categories input
        categories = inputs.get("categories", [])
        if not categories:
            return ValidationResult(
                is_valid=False,
                error_message="Categories are required. Please provide at least one category."
            )

        return ValidationResult(is_valid=True)

    def _build_component_inputs(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Build component-specific inputs for executor."""
        text = inputs.get("text", "")
        categories = inputs.get("categories", [])

        # Process categories if it's a string (comma-separated)
        if isinstance(categories, str):
            categories = [cat.strip() for cat in categories.split(",") if cat.strip()]

        return {
            "text": text,
            "categories": categories
        }

    def _map_outputs(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Map executor results to component outputs."""
        return {
            "category": result.get("category", "Unknown"),
            "confidence": result.get("confidence", 0.0),
            "error": result.get("error")
        }

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the Classifier.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Classifies the provided text into one of the specified categories.

        Args:
            **kwargs: Contains the input values:
                - text: The text to classify
                - categories: The set of valid categories
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - category: The assigned category
                - confidence: A confidence score between 0 and 1
                - error: An error message if the operation failed
        """
        warnings.warn(
            f"The build method for {self.name} is deprecated and will be removed in a future version. "
            f"Please use the execute method instead for improved performance and consistency.",
            DeprecationWarning,
            stacklevel=2
        )
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get(
            "temperature", 0.3
        )  # Lower temperature for more deterministic classification

        text = kwargs.get("text", "")
        categories = kwargs.get("categories", [])

        # Process categories if it's a string (comma-separated)
        if isinstance(categories, str):
            categories = [cat.strip() for cat in categories.split(",") if cat.strip()]

        # Validate inputs
        if not text:
            return {
                "error": "Text to classify is missing. Please connect text or provide it directly."
            }
        if not categories or len(categories) == 0:
            return {"error": "Categories are missing. Please provide at least one category."}
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for classification
            categories_str = ", ".join([f'"{cat}"' for cat in categories])
            system_prompt = f"""You are a text classification assistant. Classify the provided text into exactly one of the following categories:
{categories_str}

Also provide a confidence score between 0 and 1, where 1 is the highest confidence.
Format your response as a JSON object with two fields: "category" and "confidence".
Example: {{"category": "Technology", "confidence": 0.85}}"""

            # Create user prompt
            user_prompt = f"Text to classify: {text}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            result_text = response.choices[0].message.content.strip()

            # Parse the JSON response
            import json

            try:
                result = json.loads(result_text)
                category = result.get("category", "Unknown")
                confidence = float(result.get("confidence", 0.0))

                print(
                    f"  Classification completed successfully: {category} (confidence: {confidence})"
                )
                return {"category": category, "confidence": confidence}
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract category directly from text
                for cat in categories:
                    if cat.lower() in result_text.lower():
                        print(f"  Classification completed with fallback parsing: {cat}")
                        return {
                            "category": cat,
                            "confidence": 0.5,  # Default confidence when parsing fails
                        }

                # If no category found, return the first one with low confidence
                print(f"  Classification failed to parse result, using default category")
                return {
                    "category": categories[0] if categories else "Unknown",
                    "confidence": 0.1,  # Very low confidence
                }

        except Exception as e:
            error_msg = f"Error classifying text: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
