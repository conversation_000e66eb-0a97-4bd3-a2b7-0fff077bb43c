"""add_user_ids

Revision ID: 004
Revises: 8df2016f8a76
Create Date: 2025-05-15 10:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "004"
down_revision: Union[str, None] = "8df2016f8a76"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Define table references for use in migrations
workflows = sa.table(
    "workflows-test",
    sa.column("id", sa.String),
    sa.column("user_id", sa.String),
    sa.column("user_ids", postgresql.ARRAY(sa.String)),
)


def upgrade() -> None:
    """
    Implement your upgrade migrations here.

    Changes to implement:
    1. Add user_ids column to workflows-test table
    2. Migrate data from user_id to user_ids
    3. Drop user_id column from workflows-test table
    """
    # Add user_ids column to workflows-test table
    op.add_column(
        "workflows-test", sa.Column("user_ids", postgresql.ARRAY(sa.String()), nullable=True)
    )

    # Migrate data from user_id to user_ids
    op.execute(
        """
        UPDATE "workflows-test"
        SET user_ids = ARRAY[user_id]
        WHERE user_id IS NOT NULL
        """
    )

    # Drop user_id column from workflows-test table
    op.drop_column("workflows-test", "user_id")


def downgrade() -> None:
    """
    Implement your downgrade migrations here.

    Changes to implement:
    1. Add user_id column to workflows-test table
    2. Migrate data from user_ids to user_id
    3. Drop user_ids column from workflows-test table
    """
    # Add user_id column to workflows-test table
    op.add_column("workflows-test", sa.Column("user_id", sa.String(), nullable=True))

    # Migrate data from user_ids to user_id
    op.execute(
        """
        UPDATE "workflows-test"
        SET user_id = (user_ids)[1]
        WHERE array_length(user_ids, 1) > 0
        """
    )

    # Drop user_ids column from workflows-test table
    op.drop_column("workflows-test", "user_ids")
