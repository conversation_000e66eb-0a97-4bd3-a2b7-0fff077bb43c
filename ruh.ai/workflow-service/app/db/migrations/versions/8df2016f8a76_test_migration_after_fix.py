"""test_migration_after_fix

Revision ID: 8df2016f8a76
Revises: 003
Create Date: 2025-05-13 18:32:55.428757

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql import table, column

# revision identifiers, used by Alembic.
revision: str = "8df2016f8a76"
down_revision: Union[str, None] = "003"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Define table references for use in migrations
workflows = table(
    "workflows",
    column("id", sa.String),
    column("name", sa.String),
    column("description", sa.String),
    column("owner_id", sa.String),
    column("user_id", sa.String),
    column("tags", postgresql.JSON),
)

workflow_templates = table(
    "workflow-templates",
    column("id", sa.String),
    column("name", sa.String),
    column("description", sa.String),
    column("owner_id", sa.String),
    column("tags", postgresql.JSON),
)


def upgrade() -> None:
    """
    Implement your upgrade migrations here.

    Changes to implement:
    1. Remove owner_name column from workflow_templates table
    2. Add average_rating column to workflow_templates table
    3. Add visibility column to workflow_templates table
    4. Remove owner_name column from workflows table
    5. Change user_ids (ARRAY) to user_id (String) in workflows table
    """
    # Add average_rating column to workflow_templates table
    op.add_column(
        "workflow-templates",
        sa.Column("average_rating", sa.Integer(), nullable=True, server_default="0"),
    )

    # Add visibility column to workflow_templates table if it doesn't exist
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col["name"] for col in inspector.get_columns("workflow-templates")]

    if "visibility" not in columns:
        # First check if the enum type already exists and get its values
        has_enum = False
        enum_values = []

        # Print all enums for debugging
        all_enums = inspector.get_enums()
        print(f"All enums: {all_enums}")

        for enum in all_enums:
            print(f"Enum: {enum}")
            if enum.get("name") == "workflowvisibilityenum":
                has_enum = True
                # Different databases might have different key names
                if "enums" in enum:
                    enum_values = enum["enums"]
                elif "values" in enum:
                    enum_values = enum["values"]
                elif "labels" in enum:
                    enum_values = enum["labels"]
                break

        # Print enum values for debugging
        print(f"Existing enum values: {enum_values}")

        # If enum exists but doesn't have 'private', handle differently
        if has_enum and "private" not in enum_values:
            # Use a different default value that exists in the enum
            default_value = enum_values[0] if enum_values else None
            print(f"Using default value: {default_value}")

            # Add the column without a default value first
            op.add_column(
                "workflow-templates",
                sa.Column(
                    "visibility",
                    postgresql.ENUM(name="workflowvisibilityenum", create_type=False),
                    nullable=True,
                ),
            )

            # Then update with a valid value if we have one
            if default_value:
                op.execute(
                    f"""
                    UPDATE "workflow-templates"
                    SET visibility = '{default_value}'
                    """
                )
        else:
            # If enum doesn't exist, create it
            if not has_enum:
                op.execute(
                    """
                    CREATE TYPE workflowvisibilityenum
                    AS ENUM ('private', 'public')
                    """
                )

            # Add the column using the existing enum type
            op.add_column(
                "workflow-templates",
                sa.Column(
                    "visibility",
                    postgresql.ENUM(
                        "private", "public", name="workflowvisibilityenum", create_type=False
                    ),
                    nullable=True,
                    server_default="private",
                ),
            )

    # Add user_id column to workflows table
    op.add_column("workflows", sa.Column("user_id", sa.String(), nullable=True))

    # Migrate data from user_ids to user_id
    op.execute(
        """
        UPDATE workflows
        SET user_id = (user_ids)[1]
        WHERE array_length(user_ids, 1) > 0
        """
    )

    # Drop user_ids column from workflows table
    op.drop_column("workflows", "user_ids")

    # Drop owner_name column from workflows table
    op.drop_column("workflows", "owner_name")

    # Drop owner_name column from workflow_templates table
    op.drop_column("workflow-templates", "owner_name")


def downgrade() -> None:
    """
    Implement your downgrade migrations here.

    Revert changes:
    1. Add owner_name column to workflow_templates table
    2. Add owner_name column to workflows table
    3. Add user_ids column to workflows table
    4. Migrate data from user_id to user_ids
    5. Drop user_id column from workflows table
    6. Drop average_rating column from workflow_templates table
    7. Drop visibility column from workflow_templates table
    """
    # Add owner_name column to workflow_templates table
    op.add_column(
        "workflow-templates",
        sa.Column("owner_name", sa.String(), nullable=False, server_default=""),
    )

    # Add owner_name column to workflows table
    op.add_column(
        "workflows", sa.Column("owner_name", sa.String(), nullable=False, server_default="")
    )

    # Add user_ids column to workflows table
    op.add_column("workflows", sa.Column("user_ids", postgresql.ARRAY(sa.String()), nullable=True))

    # Migrate data from user_id to user_ids
    op.execute(
        """
        UPDATE workflows
        SET user_ids = ARRAY[user_id]
        WHERE user_id IS NOT NULL
        """
    )

    # Drop user_id column from workflows table
    op.drop_column("workflows", "user_id")

    # Drop average_rating column from workflow_templates table
    op.drop_column("workflow-templates", "average_rating")

    # Drop visibility column from workflow_templates table if it exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col["name"] for col in inspector.get_columns("workflow-templates")]

    if "visibility" in columns:
        op.drop_column("workflow-templates", "visibility")
