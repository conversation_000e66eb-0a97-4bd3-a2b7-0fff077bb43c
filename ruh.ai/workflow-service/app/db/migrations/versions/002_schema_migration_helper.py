"""schema migration helper

Revision ID: 002
Revises: 001
Create Date: 2024-06-01 10:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql import table, column

# revision identifiers, used by Alembic.
revision = "002"
down_revision = "001"
branch_labels = None
depends_on = None

# Define table references for use in migrations
workflows = table(
    "workflows",
    column("id", sa.String),
    column("name", sa.String),
    column("description", sa.String),
    column("tags", postgresql.JSON),
    column("status", sa.String),
    column("visibility", sa.String),
    column("category", sa.String),
    # Add other columns as needed
)

workflow_templates = table(
    "workflow-templates",
    column("id", sa.String),
    column("name", sa.String),
    column("description", sa.String),
    column("tags", postgresql.JSON),
    column("status", sa.String),
    column("visibility", sa.String),
    column("category", sa.String),
    # Add other columns as needed
)


def upgrade() -> None:
    """
    Example migration operations:

    1. Adding a new column
    2. Modifying column type
    3. Renaming a column
    4. Dropping a column
    5. Adding/updating data in existing columns
    """
    # Example 1: Add a new column
    # op.add_column('workflows', sa.Column('new_field', sa.String(), nullable=True))

    # Example 2: Change column type (with data conversion)
    # op.alter_column('workflows', 'some_field',
    #                 type_=sa.String(255),
    #                 existing_type=sa.String(100),
    #                 nullable=False)

    # Example 3: Rename a column
    # op.alter_column('workflows', 'old_name', new_column_name='new_name')

    # Example 4: Drop a column
    # op.drop_column('workflows', 'obsolete_field')

    # Example 5: Update data in existing rows
    # op.execute(
    #     workflows.update()
    #     .where(workflows.c.status == 'old_status')
    #     .values(status='new_status')
    # )

    # Example 6: Add a JSON field or update existing JSON structure
    # op.execute(
    #     workflows.update()
    #     .values(
    #         tags=sa.func.jsonb_set(
    #             workflows.c.tags,
    #             '{new_key}',
    #             '"new_value"'
    #         )
    #     )
    # )

    # Example 7: Create a new index
    # op.create_index(op.f('ix_workflows_new_field'), 'workflows', ['new_field'], unique=False)

    # Example 8: Add a foreign key
    # op.add_column('workflows', sa.Column('related_id', sa.String(), nullable=True))
    # op.create_foreign_key('fk_workflows_related', 'workflows', 'related_table', ['related_id'], ['id'])

    pass


def downgrade() -> None:
    """
    Reverse all operations from upgrade() in reverse order
    """
    # Example 8 (reverse): Drop foreign key and column
    # op.drop_constraint('fk_workflows_related', 'workflows', type_='foreignkey')
    # op.drop_column('workflows', 'related_id')

    # Example 7 (reverse): Drop index
    # op.drop_index(op.f('ix_workflows_new_field'), table_name='workflows')

    # Example 6 (reverse): Revert JSON changes
    # op.execute(
    #     workflows.update()
    #     .values(
    #         tags=sa.func.jsonb_remove(workflows.c.tags, '{new_key}')
    #     )
    # )

    # Example 5 (reverse): Revert data changes
    # op.execute(
    #     workflows.update()
    #     .where(workflows.c.status == 'new_status')
    #     .values(status='old_status')
    # )

    # Example 4 (reverse): Add back the dropped column
    # op.add_column('workflows', sa.Column('obsolete_field', sa.String(), nullable=True))

    # Example 3 (reverse): Rename column back
    # op.alter_column('workflows', 'new_name', new_column_name='old_name')

    # Example 2 (reverse): Change column type back
    # op.alter_column('workflows', 'some_field',
    #                 type_=sa.String(100),
    #                 existing_type=sa.String(255),
    #                 nullable=True)

    # Example 1 (reverse): Drop the added column
    # op.drop_column('workflows', 'new_field')

    pass
