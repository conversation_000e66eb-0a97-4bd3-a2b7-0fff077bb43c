# app/services/workflow_service.py
import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.grpc_ import workflow_pb2

def get_db() -> Session:
    """Create and return a new database session"""
    return SessionLocal()


# Initialize structured logger
logger = structlog.get_logger()


def _workflow_to_protobuf(workflow: Workflow, db=None) -> workflow_pb2.Workflow:
    """Convert a Workflow model instance to a protobuf Workflow message."""
    # Get current version information if available
    current_version_number = None
    if workflow.current_version_id:
        try:
            # Use provided db session or create a new one
            db_session = db if db else get_db()
            current_version = (
                db_session.query(WorkflowVersion)
                .filter(WorkflowVersion.id == workflow.current_version_id)
                .first()
            )
            if current_version:
                current_version_number = current_version.version_number
            # Only close if we created the session
            if not db and db_session:
                db_session.close()
        except Exception as e:
            logger.warning(f"Failed to get current version for workflow {workflow.id}: {str(e)}")

    return workflow_pb2.Workflow(
        id=str(workflow.id),  # Convert UUID to string
        name=workflow.name,
        description=workflow.description,
        workflow_url=workflow.workflow_url,
        builder_url=workflow.builder_url,
        owner_id=workflow.owner_id,
        user_ids=workflow.user_ids if workflow.user_ids else [],
        owner_type=workflow.owner_type,
        start_nodes=(
            [json.dumps(node) for node in workflow.start_nodes] if workflow.start_nodes else []
        ),
        available_nodes=(
            [json.dumps(node) for node in workflow.available_nodes]
            if workflow.available_nodes
            else []
        ),
        # Template Reference fields - Always include these fields
        workflow_template_id=(
            str(workflow.workflow_template_id) if workflow.workflow_template_id else None
        ),
        template_owner_id=(str(workflow.template_owner_id) if workflow.template_owner_id else None),
        is_imported=workflow.is_imported if workflow.is_imported else False,
        # Metadata
        is_updated=(
            workflow.is_updated
            if hasattr(workflow, "is_updated") and workflow.is_updated
            else False
        ),
        is_customizable=workflow.is_customizable if workflow.is_customizable else False,
        version=(current_version_number if current_version_number else "1.0.0"),
        visibility=workflow.visibility,
        category=workflow.category,
        tags=workflow.tags if workflow.tags else [],
        status=workflow.status,
        # Timestamps
        created_at=workflow.created_at.isoformat() if workflow.created_at else "",
        updated_at=workflow.updated_at.isoformat() if workflow.updated_at else "",
    )


def _workflow_version_to_protobuf(
    version: WorkflowVersion, is_current: bool
) -> workflow_pb2.WorkflowVersion:
    """Convert a WorkflowVersion model instance to a protobuf WorkflowVersion message."""
    return workflow_pb2.WorkflowVersion(
        id=str(version.id),
        workflow_id=str(version.workflow_id),
        version_number=version.version_number,
        name=version.name,
        description=version.description or "",
        workflow_url=version.workflow_url,
        builder_url=version.builder_url,
        start_nodes=(
            [json.dumps(node) for node in version.start_nodes] if version.start_nodes else []
        ),
        available_nodes=(
            [json.dumps(node) for node in version.available_nodes]
            if version.available_nodes
            else []
        ),
        category=version.category or "",
        tags=version.tags if version.tags else [],
        changelog=version.changelog or "",
        status=version.status or "active",
        is_customizable=(version.is_customizable if hasattr(version, "is_customizable") else True),
        created_at=version.created_at.isoformat() if version.created_at else "",
        is_current=is_current,
    )


def _marketplace_listing_to_protobuf(
    listing: WorkflowMarketplaceListing,
) -> workflow_pb2.WorkflowTemplate:
    """Convert a WorkflowMarketplaceListing model instance to a protobuf WorkflowTemplate message for backward compatibility."""
    # Get the workflow version details
    workflow_version = None
    if listing.workflow_version_id:
        db = None
        try:
            db = get_db()
            workflow_version = (
                db.query(WorkflowVersion)
                .filter(WorkflowVersion.id == listing.workflow_version_id)
                .first()
            )
        except Exception as e:
            logger.warning(f"Failed to get workflow version for listing {listing.id}: {str(e)}")
        finally:
            if db:
                db.close()

    return workflow_pb2.WorkflowTemplate(
        id=str(listing.id),
        name=listing.title,
        description=listing.description,
        workflow_url=workflow_version.workflow_url if workflow_version else "",
        builder_url=workflow_version.builder_url if workflow_version else "",
        start_nodes=(
            [json.dumps(node) for node in workflow_version.start_nodes]
            if workflow_version and workflow_version.start_nodes
            else []
        ),
        available_nodes=(
            [json.dumps(node) for node in workflow_version.available_nodes]
            if workflow_version and workflow_version.available_nodes
            else []
        ),
        use_count=listing.use_count,
        owner_id=listing.listed_by_user_id,
        execution_count=listing.execution_count,
        category=listing.category,
        tags=listing.tags if listing.tags else [],
        version=workflow_version.version_number if workflow_version else "1.0.0",
        status=listing.status.value if hasattr(listing.status, "value") else listing.status,
        created_at=listing.created_at.isoformat() if listing.created_at else "",
        updated_at=listing.updated_at.isoformat() if listing.updated_at else "",
        source_workflow_id=str(listing.workflow_id) if listing.workflow_id else "",
    )


def _listing_to_marketplace_workflow(
    listing: WorkflowMarketplaceListing,
) -> workflow_pb2.MarketplaceWorkflow:
    """Convert a WorkflowMarketplaceListing model instance to a protobuf MarketplaceWorkflow message."""
    # Get the workflow version details
    workflow_version = None
    if listing.workflow_version_id:
        db = None
        try:
            db = get_db()
            workflow_version = (
                db.query(WorkflowVersion)
                .filter(WorkflowVersion.id == listing.workflow_version_id)
                .first()
            )
        except Exception as e:
            logger.warning(f"Failed to get workflow version for listing {listing.id}: {str(e)}")
        finally:
            if db:
                db.close()

    return workflow_pb2.MarketplaceWorkflow(
        id=str(listing.id),
        name=listing.title,
        description=listing.description if listing.description else "",
        image_url=listing.image_url if listing.image_url else "",
        workflow_url=workflow_version.workflow_url if workflow_version else "",
        builder_url=workflow_version.builder_url if workflow_version else "",
        start_nodes=(
            [json.dumps(node) for node in workflow_version.start_nodes]
            if workflow_version and workflow_version.start_nodes
            else []
        ),
        category=listing.category if listing.category else "",
        tags=listing.tags if listing.tags else [],
        created_at=listing.created_at.isoformat() if listing.created_at else "",
        updated_at=listing.updated_at.isoformat() if listing.updated_at else "",
        owner_id=listing.listed_by_user_id,
        average_rating=listing.average_rating if listing.average_rating else 0,
        use_count=listing.use_count if listing.use_count else 0,
        execution_count=listing.execution_count if listing.execution_count else 0,
        visibility="PUBLIC",
        version=workflow_version.version_number if workflow_version else "1.0.0",
        status=listing.status if listing.status else "active",
    )
