"""
Semantic type constants for component outputs.

This module defines the semantic types that can be assigned to component outputs
to provide better context about the meaning and purpose of the output data.
"""

# AI-related semantic types
AI_RESPONSE = "ai_response"  # Generated text/content from AI models
AI_ANALYSIS = "ai_analysis"  # Analysis results (sentiment, classification, etc.)
AI_EXTRACTION = "ai_extraction"  # Extracted information from text
AI_MEMORY = "ai_memory"  # Memory objects for AI agents
AI_METADATA = "ai_metadata"  # Token usage, costs, model info, etc.

# Data processing semantic types
PROCESSED_DATA = "processed_data"  # Transformed/processed data
SELECTED_DATA = "selected_data"  # Filtered/selected subset of data
COMBINED_DATA = "combined_data"  # Merged/combined datasets
STRUCTURED_DATA = "structured_data"  # Formatted/structured output (DataFrames, etc.)
FILE_REFERENCE = "file_reference"  # File paths/references

# Control flow semantic types
ROUTING_DATA = "routing_data"  # Data passed through conditional routing
LOOP_DATA = "loop_data"  # Data from loop iterations
CONTROL_SIGNAL = "control_signal"  # Control flow signals

# Communication semantic types
MESSAGE_DATA = "message_data"  # Communication content (emails, chat, etc.)
API_RESPONSE = "api_response"  # External API responses
WEBHOOK_DATA = "webhook_data"  # Webhook payloads

# System semantic types
ERROR_INFO = "error_info"  # Error messages and details
STATUS_INFO = "status_info"  # Status/success indicators
METADATA = "metadata"  # System metadata, headers, etc.
IDENTIFIER = "identifier"  # Generated IDs/references

# Collection of all valid semantic types
VALID_SEMANTIC_TYPES = {
    # AI types
    AI_RESPONSE,
    AI_ANALYSIS,
    AI_EXTRACTION,
    AI_MEMORY,
    AI_METADATA,
    
    # Data processing types
    PROCESSED_DATA,
    SELECTED_DATA,
    COMBINED_DATA,
    STRUCTURED_DATA,
    FILE_REFERENCE,
    
    # Control flow types
    ROUTING_DATA,
    LOOP_DATA,
    CONTROL_SIGNAL,
    
    # Communication types
    MESSAGE_DATA,
    API_RESPONSE,
    WEBHOOK_DATA,
    
    # System types
    ERROR_INFO,
    STATUS_INFO,
    METADATA,
    IDENTIFIER,
}

# Semantic type descriptions for documentation
SEMANTIC_TYPE_DESCRIPTIONS = {
    AI_RESPONSE: "Generated text or content from AI models",
    AI_ANALYSIS: "Analysis results like sentiment, classification, etc.",
    AI_EXTRACTION: "Information extracted from text by AI",
    AI_MEMORY: "Memory objects for AI agents",
    AI_METADATA: "AI-related metadata like token usage, costs, model info",
    
    PROCESSED_DATA: "Data that has been transformed or processed",
    SELECTED_DATA: "Filtered or selected subset of data",
    COMBINED_DATA: "Merged or combined datasets",
    STRUCTURED_DATA: "Formatted or structured output like DataFrames",
    FILE_REFERENCE: "File paths or file references",
    
    ROUTING_DATA: "Data passed through conditional routing",
    LOOP_DATA: "Data from loop iterations",
    CONTROL_SIGNAL: "Control flow signals",
    
    MESSAGE_DATA: "Communication content like emails, chat messages",
    API_RESPONSE: "Responses from external APIs",
    WEBHOOK_DATA: "Webhook payloads",
    
    ERROR_INFO: "Error messages and error details",
    STATUS_INFO: "Status indicators and success flags",
    METADATA: "System metadata, headers, configuration info",
    IDENTIFIER: "Generated IDs, references, or unique identifiers",
}
