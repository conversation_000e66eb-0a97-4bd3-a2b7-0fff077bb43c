# User Service

A gRPC-based microservice for user authentication and management.

## Features

- User registration and authentication
- JWT-based authentication
- Google OAuth integration
- User profile management
- Password hashing and verification

## Prerequisites

- Python 3.11 or higher
- Poetry (Python package manager)
- PostgreSQL database

## Setup

1. Install dependencies:

```bash
poetry install
```

2. Set up environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Application Settings
APP_NAME=user-service
DEBUG=true
PORT=50052

# Database Settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=user_service
DB_PASSWORD=userpass
DB_NAME=user_db

# JWT Settings
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Google OAuth Settings
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## Running the Service

Use the provided script:

```bash
chmod +x run_local.sh
./run_local.sh
```

Or run manually:

```bash
# Install dependencies
poetry install

# Initialize database
poetry run python -m app.db.init_db

# Start the service
poetry run python -m app.main
```

The service will start on port 50052.

## API Documentation

### gRPC Methods

- `register`: Register a new user
- `login`: Authenticate user and get tokens
- `googleOAuthLogin`: Login with Google OAuth
- `refreshToken`: Get new access token using refresh token
- `getUser`: Get user profile
- `updateUser`: Update user information
- `deleteUser`: Delete user account
- `listUsers`: Get paginated list of users
- `searchUsers`: Search users by email or name

## Development

### Project Structure

```
user-service/
├── app/
│   ├── api/
│   ├── core/          # Core functionality (config, security)
│   ├── db/            # Database models and session
│   ├── grpc/          # Generated gRPC code
│   ├── models/        # SQLAlchemy models
│   ├── schemas/       # Pydantic models
│   └── services/      # Business logic
├── proto-definitions/ # Proto files
├── tests/            # Test files
├── .env.example      # Example environment variables
├── poetry.lock       # Lock file for dependencies
├── pyproject.toml    # Project configuration
└── README.md         # This file
```

### Testing

Run tests with:

```bash
poetry run pytest
```

### Generating gRPC Code

After modifying proto files:

```bash
poetry run python -m app.scripts.generate_grpc
```

## Database Migrations

This service uses Alembic for database migrations to manage schema changes. Follow these steps to work with migrations:

### Checking Migration Status

To check the current state of migrations:

```bash
poetry run python -m app.db.check_migration_status
```

This will show:
- Current migration revision
- Migration history
- Whether there are pending migrations

### Creating Migrations

#### Automatic Migrations

To automatically detect changes in your models and create a migration:

```bash
poetry run python -m app.db.create_auto_migration "description of changes"
```

This will:
1. Ensure the database is up-to-date with existing migrations
2. Detect changes between your models and the database schema
3. Generate a migration file with the necessary changes

#### Manual Migrations

For more complex changes, create a manual migration:

```bash
poetry run python -m app.db.create_migration "description of changes"
```

Then edit the generated file in `app/db/migrations/versions/` to implement your changes.

### Applying Migrations

To apply all pending migrations:

```bash
poetry run python -m app.db.apply_migrations
```

### Initializing a Fresh Database

For a new database or to reset an existing one:

```bash
poetry run python -m app.db.init_db
```

This will:
1. Create all tables based on your models
2. Mark the database as up-to-date with the latest migration

### Common Migration Tasks

- **Adding a new field**: Update your model, then run auto-migration
- **Changing field type**: Update your model, then run auto-migration
- **Data migrations**: Create a manual migration and implement the data transformations
- **Complex schema changes**: Create a manual migration for precise control

### Handling Missing Column Errors

If you encounter an error like:
```
ProgrammingError: column <table>.<column> does not exist
```

This means your model has been updated but the database schema hasn't. To fix this:

1. Apply pending migrations:
   ```bash
   poetry run python -m app.db.apply_migrations
   ```

2. If the error persists, create a specific migration for the missing column:
   ```bash
   poetry run python -m app.db.create_migration "add missing column"
   ```

3. Edit the generated migration file to add the specific column:
   ```python
   def upgrade() -> None:
       op.add_column('table_name', sa.Column('column_name', sa.ColumnType(), nullable=True))
   
   def downgrade() -> None:
       op.drop_column('table_name', 'column_name')
   ```

4. Apply the specific migration:
   ```bash
   poetry run python -m app.db.apply_specific_migration <revision_number>
   ```

### Troubleshooting

- **"Target database is not up to date"**: Run `apply_migrations` first
- **Conflicts in migration files**: Manually resolve conflicts, or reset with `init_db`
- **Migration not detecting changes**: Ensure your model is imported in `app/db/migrations/env.py`
