import unittest
import json
import os
import sys
from pathlib import Path
from jsonschema import Draft7Validator
from jsonschema.exceptions import ValidationError

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from app.services.workflow_schema_converter import convert_workflow_to_transition_schema


class TestWorkflowSchemaConverter(unittest.TestCase):
    """Test suite for the workflow schema converter."""

    def setUp(self):
        """Set up test fixtures."""
        # Define paths
        self.schema_def_path = Path(
            "E:/RapidInnovation/Automation Projects/Ruh.ai/orchestration-engine/app/shared/json_schemas/transition_schema.json"
        )
        self.sample_workflow_path = (
            Path(__file__).parent.parent / "testing" / "sample_workflow.json"
        )
        self.converted_schema_path = (
            Path(__file__).parent.parent / "testing" / "converted_schema.json"
        )

        # Load schema definition
        try:
            with open(self.schema_def_path, "r") as f:
                self.schema_def = json.load(f)
        except FileNotFoundError:
            self.fail(f"Schema definition file not found at: {self.schema_def_path}")
        except json.JSONDecodeError:
            self.fail(f"Error decoding JSON in schema definition file: {self.schema_def_path}")

        # Load sample workflow
        try:
            with open(self.sample_workflow_path, "r") as f:
                self.sample_workflow = json.load(f)
        except FileNotFoundError:
            self.fail(f"Sample workflow file not found at: {self.sample_workflow_path}")
        except json.JSONDecodeError:
            self.fail(f"Error decoding JSON in sample workflow file: {self.sample_workflow_path}")

        # Convert workflow to transition schema
        self.transition_schema = convert_workflow_to_transition_schema(self.sample_workflow)

        # Save the converted schema for validation
        with open(self.converted_schema_path, "w") as f:
            json.dump(self.transition_schema, f, indent=2)

    def test_schema_validation(self):
        """Test if the converted schema is valid against the schema definition."""
        validator = Draft7Validator(self.schema_def)
        try:
            validator.validate(self.transition_schema)
            self.assertTrue(True, "Converted schema is valid against the schema definition.")
        except ValidationError as e:
            self.fail(
                f"Converted schema is NOT valid against the schema definition. Validation error: {e}"
            )

    def test_node_ids_and_tool_names(self):
        """Test that node IDs and tool names are correctly mapped."""
        # Test node id in nodes array
        for node in self.transition_schema["nodes"]:
            # Find the corresponding node in the original workflow
            original_node = None
            for workflow_node in self.sample_workflow["nodes"]:
                if workflow_node["id"] in node["id"]:
                    original_node = workflow_node
                    break

            if original_node and "mcp" in original_node["data"]["type"]:
                # For MCP nodes, id should be the server_id from mcp_info
                if (
                    "mcp_info" in original_node["data"]["definition"]
                    and "server_id" in original_node["data"]["definition"]["mcp_info"]
                ):
                    expected_id = original_node["data"]["definition"]["mcp_info"]["server_id"]
                else:
                    expected_id = original_node["data"]["definition"]["name"]

                self.assertEqual(
                    expected_id,
                    node["id"],
                    f"Node id should be from server_id. Expected: {expected_id}, Got: {node['id']}",
                )

                # Check tool_name in nodes array should be from mcp_info.tool_name
                if (
                    "mcp_info" in original_node["data"]["definition"]
                    and "tool_name" in original_node["data"]["definition"]["mcp_info"]
                ):
                    expected_tool_name = original_node["data"]["definition"]["mcp_info"][
                        "tool_name"
                    ]
                    actual_tool_name = node["server_tools"][0]["tool_name"]
                    self.assertEqual(
                        expected_tool_name,
                        actual_tool_name,
                        f"Tool name in nodes array should be from mcp_info.tool_name. Expected: {expected_tool_name}, Got: {actual_tool_name}",
                    )

    def test_transition_tool_names(self):
        """Test that tool names in transitions are correctly mapped."""
        # Test transitions array
        for transition in self.transition_schema["transitions"]:
            # Find the corresponding node in the original workflow
            node_id = transition["node_info"]["node_id"]
            original_node = None
            for workflow_node in self.sample_workflow["nodes"]:
                if node_id in workflow_node["id"] or workflow_node["id"] in node_id:
                    original_node = workflow_node
                    break

            if original_node and "mcp" in original_node["data"].get("type", ""):
                # Check tool_name in transitions array
                execution_type = transition["execution_type"]
                actual_tool_name = transition["node_info"]["tools_to_use"][0]["tool_name"]

                # Get the expected tool_name from the original node
                if (
                    execution_type == "MCP"
                    and "mcp_info" in original_node["data"]["definition"]
                    and "tool_name" in original_node["data"]["definition"]["mcp_info"]
                ):
                    # For MCP nodes, tool_name should be from mcp_info.tool_name
                    expected_tool_name = original_node["data"]["definition"]["mcp_info"][
                        "tool_name"
                    ]
                else:
                    # For other nodes, tool_name should be from node definition name
                    expected_tool_name = original_node["data"]["definition"]["name"]

                # Special case for fetch_audio which is a different tool for the same node
                if (
                    actual_tool_name == "fetch_audio"
                    and "generate-audio" in transition["node_info"]["node_id"]
                ):
                    # This is valid - the node has multiple tools
                    expected_tool_name = actual_tool_name

                self.assertEqual(
                    expected_tool_name,
                    actual_tool_name,
                    f"Tool name in transition should be correct based on execution type. Expected: {expected_tool_name}, Got: {actual_tool_name}",
                )

    def test_field_values(self):
        """Test that field values are correctly set."""
        # Check field_value is null if not passed anything
        for transition in self.transition_schema["transitions"]:
            for tool in transition["node_info"]["tools_to_use"]:
                for item in tool["tool_params"]["items"]:
                    if item["field_value"] == "":
                        self.fail(
                            f"Field value should be null if not passed anything, got empty string for {item['field_name']}"
                        )

    def test_input_data_structure(self):
        """Test that input_data does not have target_node_id."""
        for transition in self.transition_schema["transitions"]:
            for input_data in transition["node_info"]["input_data"]:
                self.assertNotIn(
                    "target_node_id", input_data, "input_data should not have target_node_id"
                )

    def test_field_mapping(self):
        """Test that field mapping from sourceHandle to targetHandle is correctly implemented."""
        # For each edge in the workflow, check that the corresponding field value exists in the target transition's tool_params
        for edge in self.sample_workflow["edges"]:
            source_node_id = edge["source"]
            target_node_id = edge["target"]
            source_handle = edge.get("sourceHandle")
            target_handle = edge.get("targetHandle")

            # Skip edges without handles
            if not source_handle or not target_handle:
                continue

            # Find the corresponding transition in the transition schema
            found_transition = False
            for transition in self.transition_schema["transitions"]:
                # Find the original target node to get the correct node_id
                original_target_node = next(
                    (n for n in self.sample_workflow["nodes"] if n["id"] == target_node_id), None
                )
                if original_target_node:
                    target_node_type = original_target_node["data"]["type"]
                    target_node_def = original_target_node["data"]["definition"]
                    expected_target_node_id = ""

                    if target_node_type == "mcp":
                        # For MCP nodes, use the server_id from mcp_info if available
                        if (
                            "mcp_info" in target_node_def
                            and "server_id" in target_node_def["mcp_info"]
                        ):
                            expected_target_node_id = target_node_def["mcp_info"]["server_id"]
                        else:
                            # Fallback to the name from the node definition
                            expected_target_node_id = target_node_def["name"]
                    else:
                        # For Components nodes, use the definition name
                        expected_target_node_id = target_node_def["name"]

                    # Check if this transition has the target node as its node_id
                    if transition["node_info"]["node_id"] == expected_target_node_id:
                        # Find the original source node to get the correct node_id
                        original_source_node = next(
                            (n for n in self.sample_workflow["nodes"] if n["id"] == source_node_id),
                            None,
                        )
                        if original_source_node:
                            source_node_type = original_source_node["data"]["type"]
                            source_node_def = original_source_node["data"]["definition"]
                            expected_source_node_id = ""

                            if source_node_type == "mcp":
                                # For MCP nodes, use the server_id from mcp_info if available
                                if (
                                    "mcp_info" in source_node_def
                                    and "server_id" in source_node_def["mcp_info"]
                                ):
                                    expected_source_node_id = source_node_def["mcp_info"][
                                        "server_id"
                                    ]
                                else:
                                    # Fallback to the name from the node definition
                                    expected_source_node_id = source_node_def["name"]
                            else:
                                # For Components nodes, use the definition name
                                expected_source_node_id = source_node_def["name"]

                            # Check if this transition has an input_data entry from the source node
                            for input_data in transition["node_info"]["input_data"]:
                                if input_data["source_node_id"] == expected_source_node_id:
                                    # This is the correct transition, now check if the field value is correctly set in tool_params
                                    found_transition = True

                                    # Check if the tool_params has the target field with the correct field_value
                                    found_field = False
                                    for tool in transition["node_info"]["tools_to_use"]:
                                        for item in tool["tool_params"]["items"]:
                                            if item["field_name"] == target_handle:
                                                found_field = True
                                                # Check if the field_value is correctly set to ${source_handle}
                                                expected_field_value = f"${{{source_handle}}}"
                                                self.assertEqual(
                                                    item["field_value"],
                                                    expected_field_value,
                                                    f"Field value mismatch for edge from {source_node_id} to {target_node_id}. Expected: {expected_field_value}, Got: {item['field_value']}",
                                                )
                                                break
                                        if found_field:
                                            break

                                    self.assertTrue(
                                        found_field,
                                        f"Field {target_handle} not found in tool_params for edge from {source_node_id} to {target_node_id}",
                                    )
                                    break

                    if found_transition:
                        break

            self.assertTrue(
                found_transition,
                f"Transition not found for edge from {source_node_id} to {target_node_id}",
            )

    def test_no_temporary_fields(self):
        """Test that there are no temporary fields (_source_handle, _target_handle) in the output."""
        for transition in self.transition_schema["transitions"]:
            for input_data in transition["node_info"]["input_data"]:
                self.assertNotIn(
                    "_source_handle", input_data, "input_data should not have _source_handle"
                )
                self.assertNotIn(
                    "_target_handle", input_data, "input_data should not have _target_handle"
                )

            for output_data in transition["node_info"]["output_data"]:
                self.assertNotIn(
                    "_source_handle", output_data, "output_data should not have _source_handle"
                )
                self.assertNotIn(
                    "_target_handle", output_data, "output_data should not have _target_handle"
                )


if __name__ == "__main__":
    unittest.main()
