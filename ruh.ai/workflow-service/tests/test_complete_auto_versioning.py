#!/usr/bin/env python3
"""
Complete test to demonstrate the auto-versioning fix is working correctly.
This test simulates the exact user scenario described in the issue.
"""

import sys
import os
import json
from datetime import datetime
import traceback
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

try:
    from app.services.workflow_functions import WorkflowFunctions
    from app.grpc_ import workflow_pb2
    from app.models.workflow import Workflow, WorkflowVersion
    from app.db.session import SessionLocal
    from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum, WorkflowOwnerTypeEnum, WorkflowCategoryEnum
    from unittest.mock import Mock, patch
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import failed: {str(e)}")
    traceback.print_exc()
    sys.exit(1)


def test_complete_auto_versioning_scenario():
    """
    Complete test that simulates the exact user scenario:
    1. Create workflow with auto_version_on_update=True
    2. Update workflow with changes
    3. Verify new version is created
    4. Call listVersions and verify latest changes are present
    """
    
    print("=== Complete Auto-Versioning Scenario Test ===")
    print("Simulating: User updates workflow with auto_version=True and checks listVersions")
    
    workflow_functions = WorkflowFunctions()
    db = SessionLocal()
    
    try:
        # Step 1: Create a workflow with auto_version_on_update=True
        print("\n📝 Step 1: Creating workflow with auto_version_on_update=True")
        
        test_workflow = Workflow(
            id="complete-test-workflow-123",
            name="Complete Test Workflow",
            description="Original description for complete test",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start"}],
            owner_id="complete-test-user-123",
            owner_type=WorkflowOwnerTypeEnum.USER,
            auto_version_on_update=True,  # 🔑 This is the key setting
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            category=WorkflowCategoryEnum.AUTOMATION,
            tags=["complete-test"]
        )
        
        db.add(test_workflow)
        db.flush()
        
        # Create initial version (v1.0.0)
        initial_version = WorkflowVersion(
            workflow_id=test_workflow.id,
            version_number="1.0.0",
            name=test_workflow.name,
            description=test_workflow.description,
            workflow_url=test_workflow.workflow_url,
            builder_url=test_workflow.builder_url,
            start_nodes=test_workflow.start_nodes,
            category=test_workflow.category,
            tags=test_workflow.tags,
            changelog="Initial version"
        )
        
        db.add(initial_version)
        db.flush()
        
        test_workflow.current_version_id = initial_version.id
        db.commit()
        
        print(f"✅ Created workflow: {test_workflow.id}")
        print(f"✅ Created initial version: {initial_version.version_number} (ID: {initial_version.id})")
        
        # Step 2: Update workflow with changes (this should trigger auto-versioning)
        print("\n🔄 Step 2: Updating workflow with changes (should trigger auto-versioning)")
        
        update_request = workflow_pb2.UpdateWorkflowRequest()
        update_request.id = test_workflow.id
        update_request.name = "Updated Complete Test Workflow"  # Changed name
        update_request.description = "Updated description for complete test"  # Changed description
        update_request.owner.id = "complete-test-user-123"
        
        # Add fields to update mask
        update_request.update_mask.paths.append("name")
        update_request.update_mask.paths.append("description")
        
        context = Mock()
        
        # Mock the get_db method to return our test database session
        with patch.object(workflow_functions, 'get_db', return_value=db):
            response = workflow_functions.updateWorkflow(update_request, context)
            
            print(f"Update response: {response.success} - {response.message}")
            
            if not response.success:
                print(f"❌ Update failed: {response.message}")
                return False
        
        # Step 3: Verify new version was created
        print("\n🔍 Step 3: Verifying new version was created")
        
        # Re-query the workflow to get updated state
        updated_workflow = db.query(Workflow).filter(Workflow.id == test_workflow.id).first()
        
        # Check all versions
        all_versions = db.query(WorkflowVersion).filter(
            WorkflowVersion.workflow_id == test_workflow.id
        ).order_by(WorkflowVersion.created_at.desc()).all()
        
        print(f"Total versions found: {len(all_versions)}")
        
        if len(all_versions) != 2:
            print(f"❌ Expected 2 versions, but found {len(all_versions)}")
            return False
        
        latest_version = all_versions[0]
        print(f"✅ Latest version: {latest_version.version_number} (ID: {latest_version.id})")
        print(f"✅ Latest version name: {latest_version.name}")
        print(f"✅ Latest version description: {latest_version.description}")
        print(f"✅ Current version ID: {updated_workflow.current_version_id}")
        
        # Verify the latest version has the updated content
        if latest_version.name != "Updated Complete Test Workflow":
            print(f"❌ Latest version name mismatch. Expected: 'Updated Complete Test Workflow', Got: '{latest_version.name}'")
            return False
        
        if latest_version.description != "Updated description for complete test":
            print(f"❌ Latest version description mismatch. Expected: 'Updated description for complete test', Got: '{latest_version.description}'")
            return False
        
        # Step 4: Test listVersions functionality
        print("\n📋 Step 4: Testing listVersions functionality")
        
        list_request = workflow_pb2.ListWorkflowVersionsRequest()
        list_request.workflow_id = test_workflow.id
        list_request.user_id = "complete-test-user-123"
        list_request.page = 1
        list_request.page_size = 10
        
        with patch.object(workflow_functions, 'get_db', return_value=db):
            list_response = workflow_functions.listWorkflowVersions(list_request, context)
        
        print(f"List response: {list_response.success} - {list_response.message}")
        print(f"Total versions in response: {list_response.total}")
        print(f"Current version ID in response: {list_response.current_version_id}")
        
        if not list_response.success:
            print(f"❌ listVersions failed: {list_response.message}")
            return False
        
        if len(list_response.versions) != 2:
            print(f"❌ listVersions returned {len(list_response.versions)} versions, expected 2")
            return False
        
        # Step 5: Verify latest changes are present in listVersions
        print("\n✅ Step 5: Verifying latest changes are present in listVersions")
        
        print("Versions from listVersions:")
        for i, version_proto in enumerate(list_response.versions):
            print(f"  {i+1}. Version {version_proto.version_number} (ID: {version_proto.id})")
            print(f"     Name: {version_proto.name}")
            print(f"     Description: {version_proto.description}")
            print(f"     Is Current: {version_proto.is_current}")
        
        # The first version should be the latest (newest)
        latest_from_list = list_response.versions[0]
        
        if latest_from_list.name != "Updated Complete Test Workflow":
            print(f"❌ Latest changes NOT present in listVersions!")
            print(f"   Expected name: 'Updated Complete Test Workflow'")
            print(f"   Actual name: '{latest_from_list.name}'")
            return False
        
        if latest_from_list.description != "Updated description for complete test":
            print(f"❌ Latest changes NOT present in listVersions!")
            print(f"   Expected description: 'Updated description for complete test'")
            print(f"   Actual description: '{latest_from_list.description}'")
            return False
        
        if not latest_from_list.is_current:
            print(f"❌ Latest version is not marked as current in listVersions!")
            return False
        
        print("🎉 SUCCESS: Latest changes are present in listVersions!")
        print("🎉 Auto-versioning is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        try:
            # Set current_version_id to NULL first
            workflow = db.query(Workflow).filter(Workflow.id == "complete-test-workflow-123").first()
            if workflow:
                workflow.current_version_id = None
                db.commit()
            
            # Delete versions
            db.query(WorkflowVersion).filter(WorkflowVersion.workflow_id == "complete-test-workflow-123").delete()
            db.commit()
            
            # Delete workflow
            db.query(Workflow).filter(Workflow.id == "complete-test-workflow-123").delete()
            db.commit()
            print("\n✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


if __name__ == "__main__":
    success = test_complete_auto_versioning_scenario()
    if success:
        print("\n🎉 ALL TESTS PASSED - Auto-versioning fix is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ TEST FAILED - Auto-versioning needs more work")
        sys.exit(1)
