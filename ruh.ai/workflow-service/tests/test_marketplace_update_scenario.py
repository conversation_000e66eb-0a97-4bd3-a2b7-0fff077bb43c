"""
Test to reproduce the marketplace listing duplication issue.
This test simulates the scenario where a marketplace listing already exists
and a new version is created - it should update the existing listing, not create a new one.
"""

import pytest
from datetime import datetime, timezone
from sqlalchemy.orm import Session

from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.db.session import SessionLocal


def test_marketplace_listing_update_not_duplicate():
    """Test that existing marketplace listings are updated, not duplicated."""
    db = SessionLocal()
    workflow_service = WorkflowFunctions()
    mock_context = type('MockContext', (), {
        'set_code': lambda self, code: None,
        'set_details': lambda self, details: None
    })()
    
    try:
        # Step 1: Create a workflow
        workflow = Workflow(
            name="Test Workflow",
            description="Test Description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[{"id": "node1", "type": "action"}],
            owner_id="user123",
            user_ids=["user123"],
            owner_type="user",
            is_updated=False,
            visibility=WorkflowVisibilityEnum.PUBLIC,
            status=WorkflowStatusEnum.ACTIVE
        )
        db.add(workflow)
        db.flush()
        
        # Step 2: Create initial version (v1.0.0)
        initial_version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.0.0",
            name=workflow.name,
            description=workflow.description,
            workflow_url=workflow.workflow_url,
            builder_url=workflow.builder_url,
            start_nodes=workflow.start_nodes,
            available_nodes=workflow.available_nodes,
            changelog="Initial version"
        )
        db.add(initial_version)
        db.flush()
        
        workflow.current_version_id = initial_version.id
        db.commit()
        
        # Step 3: Create an existing marketplace listing for v1.0.0
        existing_listing = WorkflowMarketplaceListing(
            workflow_id=workflow.id,
            workflow_version_id=initial_version.id,
            listed_by_user_id=workflow.owner_id,
            title=workflow.name,
            description=workflow.description or "No description provided.",
            category=workflow.category,
            tags=workflow.tags if workflow.tags else [],
            use_count=5,  # Some existing usage
            execution_count=10,
            average_rating=4.5,
            visibility=WorkflowVisibilityEnum.PUBLIC,
            status=WorkflowStatusEnum.ACTIVE,
        )
        db.add(existing_listing)
        db.commit()
        
        # Store IDs for verification
        workflow_id = workflow.id
        existing_listing_id = existing_listing.id
        
        print(f"Created workflow {workflow_id} with existing marketplace listing {existing_listing_id}")
        
        # Step 4: Simulate workflow changes and set is_updated=True
        workflow.name = "Updated Workflow Name"
        workflow.description = "Updated Description"
        workflow.is_updated = True
        db.commit()
        
        # Step 5: Create new version and publish (this should UPDATE existing listing, not create new one)
        request = workflow_pb2.CreateVersionAndPublishRequest(
            workflow_id=str(workflow_id),
            user_id="user123",
            publish_to_marketplace=True
        )
        
        # Count marketplace listings before the operation
        listings_before = db.query(WorkflowMarketplaceListing).filter(
            WorkflowMarketplaceListing.workflow_id == workflow_id
        ).count()
        print(f"Marketplace listings before createVersionAndPublish: {listings_before}")
        
        # Call the method
        response = workflow_service.createVersionAndPublish(request, mock_context)
        
        # Step 6: Verify the response
        assert response.success is True
        assert response.version_created is True
        assert response.marketplace_updated is True
        print(f"Response: {response.message}")
        
        # Step 7: Check that NO NEW marketplace listing was created (should still be 1)
        db.close()
        db = SessionLocal()
        
        listings_after = db.query(WorkflowMarketplaceListing).filter(
            WorkflowMarketplaceListing.workflow_id == workflow_id
        ).count()
        print(f"Marketplace listings after createVersionAndPublish: {listings_after}")
        
        # This should be 1 (updated existing), not 2 (created new)
        assert listings_after == 1, f"Expected 1 marketplace listing, but found {listings_after}. This indicates a duplicate was created!"
        
        # Step 8: Verify the existing listing was updated with new version info
        updated_listing = db.query(WorkflowMarketplaceListing).filter(
            WorkflowMarketplaceListing.workflow_id == workflow_id
        ).first()
        
        assert updated_listing.id == existing_listing_id, "The listing ID should remain the same (updated, not replaced)"
        
        # Verify the listing was updated with new version
        new_version = db.query(WorkflowVersion).filter(
            WorkflowVersion.workflow_id == workflow_id,
            WorkflowVersion.version_number == "1.1.0"
        ).first()
        assert new_version is not None, "New version 1.1.0 should have been created"
        
        assert updated_listing.workflow_version_id == new_version.id, "Listing should reference the new version"
        assert updated_listing.title == "Updated Workflow Name", "Listing title should be updated"
        assert updated_listing.description == "Updated Description", "Listing description should be updated"
        
        # Verify existing metadata was preserved
        assert updated_listing.use_count == 5, "Existing use count should be preserved"
        assert updated_listing.execution_count == 10, "Existing execution count should be preserved"
        assert updated_listing.average_rating == 4.5, "Existing rating should be preserved"
        
        print("✅ Test passed: Existing marketplace listing was updated, no duplicate created!")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Test failed: {str(e)}")
        raise e
    finally:
        db.close()


if __name__ == "__main__":
    test_marketplace_listing_update_not_duplicate()