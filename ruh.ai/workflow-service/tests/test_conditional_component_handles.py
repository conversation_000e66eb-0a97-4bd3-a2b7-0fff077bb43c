"""
Test suite for conditional component handle management in workflow service.

This module tests the handle management functionality for conditional components:
- Data flow through conditional components
- Input/output handle mapping for conditional workflows
- Integration with existing handle management system
- Proper data source resolution for conditional component workflows

Following TDD methodology - Phase 3 Cycle 2: Handle Management for Conditional Components
"""

import pytest
import os
from unittest.mock import patch
from typing import Dict, Any

from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema
)


class TestConditionalComponentHandles:
    """Test suite for conditional component handle management."""
    
    @pytest.fixture
    def conditional_workflow_with_handles(self):
        """Fixture providing a workflow with conditional component and handle connections."""
        return {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {
                        "originalType": "StartNode",
                        "definition": {"name": "StartNode"}
                    }
                },
                {
                    "id": "input-processor",
                    "data": {
                        "type": "component",
                        "definition": {"name": "input_processor"}
                    }
                },
                {
                    "id": "conditional-router", 
                    "data": {
                        "originalType": "ConditionalNode",
                        "definition": {"name": "ConditionalNode"},
                        "config": {
                            "num_conditions": 2,
                            "condition_1_operator": "equals",
                            "condition_1_expected_value": "approved",
                            "condition_1_source": "node_output",
                            "condition_2_operator": "contains",
                            "condition_2_expected_value": "error",
                            "condition_2_source": "node_output"
                        }
                    }
                },
                {
                    "id": "approval-handler",
                    "data": {
                        "type": "component",
                        "definition": {"name": "approval_handler"}
                    }
                },
                {
                    "id": "error-handler",
                    "data": {
                        "type": "component",
                        "definition": {"name": "error_handler"}
                    }
                },
                {
                    "id": "default-handler",
                    "data": {
                        "type": "component",
                        "definition": {"name": "default_handler"}
                    }
                }
            ],
            "edges": [
                {"source": "start-1", "target": "input-processor"},
                {
                    "source": "input-processor", 
                    "target": "conditional-router",
                    "sourceHandle": "output",
                    "targetHandle": "input"
                },
                {
                    "source": "conditional-router",
                    "target": "approval-handler", 
                    "sourceHandle": "condition_1_output",
                    "targetHandle": "input"
                },
                {
                    "source": "conditional-router",
                    "target": "error-handler",
                    "sourceHandle": "condition_2_output", 
                    "targetHandle": "input"
                },
                {
                    "source": "conditional-router",
                    "target": "default-handler",
                    "sourceHandle": "default_output",
                    "targetHandle": "input"
                }
            ],
            "mcp_configs": []
        }
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_conditional_component_handles_data_flow(self, conditional_workflow_with_handles):
        """
        Test that conditional components properly handle data flow.
        
        Expected to FAIL initially until handle management is implemented.
        """
        result = convert_workflow_to_transition_schema(conditional_workflow_with_handles)
        
        # Verify separate conditional transition exists
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-router" in t["id"]),
            None
        )

        assert conditional_transition is not None, "Conditional transition should exist in separate transition mode"

        # Conditional transition should have conditional component
        conditional_tools = conditional_transition["node_info"]["tools_to_use"]
        conditional_tool = next((tool for tool in conditional_tools if tool["tool_name"] == "conditional"), None)
        assert conditional_tool is not None, "Conditional transition should have conditional component"

        # Input processor should NOT have conditional component in separate transition mode
        input_transition = next(
            (t for t in result["transitions"] if "input-processor" in t["id"]),
            None
        )
        assert input_transition is not None, "Input processor transition should exist"
        input_tools = input_transition["node_info"]["tools_to_use"]
        input_conditional_tools = [tool for tool in input_tools if tool["tool_name"] == "conditional"]
        assert len(input_conditional_tools) == 0, "Input processor should not have conditional component in separate transition mode"

        # Verify approval handler receives data from conditional transition (not input processor)
        approval_transition = next(
            (t for t in result["transitions"] if "approval-handler" in t["id"]),
            None
        )

        assert approval_transition is not None, "Approval handler transition should exist"

        input_data = approval_transition["node_info"]["input_data"]
        assert len(input_data) > 0, "Approval handler should have input data"

        # Should reference the conditional transition in separate transition mode
        source_transition = input_data[0]["from_transition_id"]
        assert "conditional-router" in source_transition, \
            f"Should reference conditional transition in separate mode, got {source_transition}"
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_all_conditional_targets_receive_correct_data_source(self, conditional_workflow_with_handles):
        """
        Test that all targets of conditional routing receive data from the correct source.
        
        Expected to FAIL initially until handle management is implemented.
        """
        result = convert_workflow_to_transition_schema(conditional_workflow_with_handles)
        
        # Get the conditional transition ID
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-router" in t["id"]),
            None
        )
        conditional_transition_id = conditional_transition["id"]

        # Check all conditional targets
        target_handlers = ["approval-handler", "error-handler", "default-handler"]

        for handler_name in target_handlers:
            handler_transition = next(
                (t for t in result["transitions"] if handler_name in t["id"]),
                None
            )

            assert handler_transition is not None, f"{handler_name} transition should exist"

            input_data = handler_transition["node_info"]["input_data"]
            assert len(input_data) > 0, f"{handler_name} should have input data"

            # Should reference the conditional transition in separate transition mode
            source_transition = input_data[0]["from_transition_id"]
            assert source_transition == conditional_transition_id, \
                f"{handler_name} should reference conditional transition in separate mode"
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_conditional_component_creates_separate_transition(self, conditional_workflow_with_handles):
        """
        Test that conditional component creates a separate transition in the new architecture.

        This test has been updated to reflect the new separate transition architecture.
        """
        result = convert_workflow_to_transition_schema(conditional_workflow_with_handles)

        # Should have a separate transition for the conditional node
        conditional_transitions = [
            t for t in result["transitions"]
            if "conditional-router" in t["id"]
        ]

        assert len(conditional_transitions) == 1, \
            "Conditional component should create separate transition in new architecture"

        # Verify the conditional transition has the conditional component
        conditional_transition = conditional_transitions[0]
        tools = conditional_transition["node_info"]["tools_to_use"]
        conditional_tool = next((tool for tool in tools if tool["tool_name"] == "conditional"), None)
        assert conditional_tool is not None, "Conditional transition should have conditional component"
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_handle_mapping_preserves_data_types(self, conditional_workflow_with_handles):
        """
        Test that handle mapping preserves data types through conditional routing.
        
        Expected to FAIL initially until data type preservation is implemented.
        """
        result = convert_workflow_to_transition_schema(conditional_workflow_with_handles)
        
        # Check that data types are preserved in input_data
        approval_transition = next(
            (t for t in result["transitions"] if "approval-handler" in t["id"]),
            None
        )
        
        input_data = approval_transition["node_info"]["input_data"]
        assert len(input_data) > 0
        
        # Should have proper data type information
        data_entry = input_data[0]
        assert "data_type" in data_entry
        assert data_entry["data_type"] in ["string", "object", "array", "number", "boolean"]
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "embedded"})
    def test_embedded_mode_handle_management_unchanged(self, conditional_workflow_with_handles):
        """
        Test that embedded mode handle management remains unchanged.
        
        Expected to FAIL initially until backward compatibility is ensured.
        """
        result = convert_workflow_to_transition_schema(conditional_workflow_with_handles)
        
        # In embedded mode, should have conditional_routing in the input processor transition
        input_transition = next(
            (t for t in result["transitions"] if "input-processor" in t["id"]),
            None
        )
        
        assert input_transition is not None
        assert "conditional_routing" in input_transition, \
            "Embedded mode should have conditional_routing field"
        
        # Should not have conditional component in tools
        tools = input_transition["node_info"]["tools_to_use"]
        conditional_tools = [tool for tool in tools if tool["tool_name"] == "conditional"]
        assert len(conditional_tools) == 0, \
            "Embedded mode should not have conditional component"
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_complex_handle_mapping_with_multiple_sources(self):
        """
        Test handle mapping with multiple data sources feeding into conditional component.

        Expected to FAIL initially until complex handle mapping is implemented.
        """
        # Simplified workflow to test the core functionality
        complex_workflow = {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {"originalType": "StartNode", "definition": {"name": "StartNode"}}
                },
                {
                    "id": "data-combiner",
                    "data": {"type": "component", "definition": {"name": "data_combiner"}}
                },
                {
                    "id": "conditional-complex",
                    "data": {
                        "originalType": "ConditionalNode",
                        "definition": {"name": "ConditionalNode"},
                        "config": {
                            "num_conditions": 1,
                            "condition_1_operator": "equals",
                            "condition_1_expected_value": "combined_success",
                            "condition_1_source": "node_output"
                        }
                    }
                },
                {
                    "id": "final-processor",
                    "data": {"type": "component", "definition": {"name": "final_processor"}}
                }
            ],
            "edges": [
                {"source": "start-1", "target": "data-combiner"},
                {"source": "data-combiner", "target": "conditional-complex"},
                {"source": "conditional-complex", "target": "final-processor", "sourceHandle": "condition_1_output"}
            ],
            "mcp_configs": []
        }

        result = convert_workflow_to_transition_schema(complex_workflow)

        # Should have separate conditional transition
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-complex" in t["id"]),
            None
        )

        assert conditional_transition is not None, "Should have separate conditional transition"

        # Conditional transition should have conditional component
        tools = conditional_transition["node_info"]["tools_to_use"]
        conditional_tool = next((tool for tool in tools if tool["tool_name"] == "conditional"), None)
        assert conditional_tool is not None, "Conditional transition should have conditional component"

        # Verify conditional component has correct configuration
        assert conditional_tool["server_id"] == "node-executor-service"
        assert "tool_params" in conditional_tool

        # Extract conditions from tool_params format
        tool_params = conditional_tool["tool_params"]
        conditions = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
                break

        assert conditions is not None
        assert len(conditions) == 1

        condition = conditions[0]
        assert condition["operator"] == "equals"
        assert condition["expected_value"] == "combined_success"
        assert condition["source"] == "node_output"
        assert condition["next_transition"] == "transition-final-processor"

        # Data combiner should NOT have conditional component in separate transition mode
        combiner_transition = next(
            (t for t in result["transitions"] if "data-combiner" in t["id"]),
            None
        )
        assert combiner_transition is not None
        combiner_tools = combiner_transition["node_info"]["tools_to_use"]
        combiner_conditional_tools = [tool for tool in combiner_tools if tool["tool_name"] == "conditional"]
        assert len(combiner_conditional_tools) == 0, "Combiner should not have conditional component in separate transition mode"
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_handle_mapping_with_global_context_conditions(self):
        """
        Test handle mapping when conditions use global context variables.
        
        Expected to FAIL initially until global context handle mapping is implemented.
        """
        global_context_workflow = {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {"originalType": "StartNode", "definition": {"name": "StartNode"}}
                },
                {
                    "id": "context-processor",
                    "data": {"type": "component", "definition": {"name": "context_processor"}}
                },
                {
                    "id": "conditional-global",
                    "data": {
                        "originalType": "ConditionalNode",
                        "definition": {"name": "ConditionalNode"},
                        "config": {
                            "num_conditions": 1,
                            "condition_1_operator": "equals",
                            "condition_1_expected_value": "premium",
                            "condition_1_source": "global_context",
                            "condition_1_variable": "user_tier"
                        }
                    }
                },
                {
                    "id": "premium-handler",
                    "data": {"type": "component", "definition": {"name": "premium_handler"}}
                }
            ],
            "edges": [
                {"source": "start-1", "target": "context-processor"},
                {"source": "context-processor", "target": "conditional-global"},
                {"source": "conditional-global", "target": "premium-handler", "sourceHandle": "condition_1_output"}
            ],
            "mcp_configs": []
        }
        
        result = convert_workflow_to_transition_schema(global_context_workflow)
        
        # Should have separate conditional transition
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-global" in t["id"]),
            None
        )

        assert conditional_transition is not None, "Should have separate conditional transition"

        # Conditional transition should have conditional component
        tools = conditional_transition["node_info"]["tools_to_use"]
        conditional_tool = next((tool for tool in tools if tool["tool_name"] == "conditional"), None)

        assert conditional_tool is not None, "Conditional transition should have conditional component"

        # Verify global context condition is properly configured
        # Extract conditions from tool_params format
        tool_params = conditional_tool["tool_params"]
        conditions = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
                break

        assert conditions is not None
        condition = conditions[0]
        assert condition["source"] == "global_context"
        assert condition["variable_name"] == "user_tier"
        assert condition["expected_value"] == "premium"

        # Context processor should NOT have conditional component in separate transition mode
        context_transition = next(
            (t for t in result["transitions"] if "context-processor" in t["id"]),
            None
        )
        assert context_transition is not None
        context_tools = context_transition["node_info"]["tools_to_use"]
        context_conditional_tools = [tool for tool in context_tools if tool["tool_name"] == "conditional"]
        assert len(context_conditional_tools) == 0, "Context processor should not have conditional component in separate transition mode"


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
