import pytest
from unittest.mock import MagicMock, patch
from app.services.marketplace_functions import WorkflowMarketplaceFunctions
from app.grpc_.workflow_pb2 import GetTemplateRequest, GetTemplateResponse, WorkflowTemplate


class TestSourceWorkflowIdIntegration:
    """Integration tests for source_workflow_id functionality"""

    def setup_method(self):
        """Set up test fixtures"""
        self.marketplace_functions = WorkflowMarketplaceFunctions()

    def test_get_template_with_source_workflow_id_integration(self):
        """Integration test: get_template returns source_workflow_id from marketplace listing"""
        
        # Mock database session
        mock_db = MagicMock()
        
        # Mock marketplace listing with workflow_id (which becomes source_workflow_id)
        mock_listing = MagicMock()
        mock_listing.id = "listing-123"
        mock_listing.name = "Test Workflow Template"
        mock_listing.description = "A test workflow template"
        mock_listing.workflow_id = "original-workflow-456"  # This becomes source_workflow_id
        mock_listing.owner_id = "user-123"
        mock_listing.category = "automation"
        mock_listing.tags = '["automation", "test"]'
        mock_listing.version = "1.0.0"
        mock_listing.status = "ACTIVE"
        mock_listing.visibility = "PUBLIC"
        mock_listing.workflow_url = "https://example.com/workflow.json"
        mock_listing.builder_url = "https://example.com/builder.json"
        mock_listing.use_count = 5
        mock_listing.execution_count = 10
        mock_listing.created_at = "2024-01-01T00:00:00Z"
        mock_listing.updated_at = "2024-01-01T00:00:00Z"
        mock_listing.start_nodes = "[]"
        mock_listing.available_nodes = "[]"
        
        # Mock database query
        mock_db.query().filter().first.return_value = mock_listing
        
        # Create request
        request = GetTemplateRequest(template_id="listing-123", user_id="user-123")
        context = MagicMock()
        
        # Patch the get_db method
        with patch.object(self.marketplace_functions, 'get_db', return_value=mock_db):
            # Call the method
            response = self.marketplace_functions.getTemplate(request, context)
            
            # Verify response
            assert isinstance(response, GetTemplateResponse)
            assert response.success is True
            assert response.message == "Template retrieved successfully"
            
            # Verify template includes source_workflow_id
            template = response.template
            assert isinstance(template, WorkflowTemplate)
            assert template.id == "listing-123"
            assert template.name == "Test Workflow Template"
            assert template.source_workflow_id == "original-workflow-456"
            
            # Verify other fields
            assert template.owner_id == "user-123"
            assert template.category == "automation"
            assert template.version == "1.0.0"
            assert template.status == "ACTIVE"
            assert template.use_count == 5
            assert template.execution_count == 10

    def test_get_template_with_null_source_workflow_id_integration(self):
        """Integration test: get_template handles null workflow_id gracefully"""
        
        # Mock database session
        mock_db = MagicMock()
        
        # Mock marketplace listing with null workflow_id
        mock_listing = MagicMock()
        mock_listing.id = "listing-123"
        mock_listing.name = "Test Workflow Template"
        mock_listing.description = "A test workflow template"
        mock_listing.workflow_id = None  # This should result in empty source_workflow_id
        mock_listing.owner_id = "user-123"
        mock_listing.category = "automation"
        mock_listing.tags = '["automation", "test"]'
        mock_listing.version = "1.0.0"
        mock_listing.status = "ACTIVE"
        mock_listing.visibility = "PUBLIC"
        mock_listing.workflow_url = "https://example.com/workflow.json"
        mock_listing.builder_url = "https://example.com/builder.json"
        mock_listing.use_count = 5
        mock_listing.execution_count = 10
        mock_listing.created_at = "2024-01-01T00:00:00Z"
        mock_listing.updated_at = "2024-01-01T00:00:00Z"
        mock_listing.start_nodes = "[]"
        mock_listing.available_nodes = "[]"
        
        # Mock database query
        mock_db.query().filter().first.return_value = mock_listing
        
        # Create request
        request = GetTemplateRequest(template_id="listing-123", user_id="user-123")
        context = MagicMock()
        
        # Patch the get_db method
        with patch.object(self.marketplace_functions, 'get_db', return_value=mock_db):
            # Call the method
            response = self.marketplace_functions.getTemplate(request, context)
            
            # Verify response
            assert isinstance(response, GetTemplateResponse)
            assert response.success is True
            
            # Verify template handles null source_workflow_id
            template = response.template
            assert isinstance(template, WorkflowTemplate)
            assert template.id == "listing-123"
            assert template.name == "Test Workflow Template"
            assert template.source_workflow_id == ""  # Should be empty string when None
            
            # Verify other fields are still correct
            assert template.owner_id == "user-123"
            assert template.category == "automation"

    def test_marketplace_listing_to_protobuf_conversion(self):
        """Test the _marketplace_listing_to_protobuf method directly"""
        
        # Mock marketplace listing
        mock_listing = MagicMock()
        mock_listing.id = "listing-123"
        mock_listing.name = "Test Workflow Template"
        mock_listing.description = "A test workflow template"
        mock_listing.workflow_id = "original-workflow-456"
        mock_listing.owner_id = "user-123"
        mock_listing.category = "automation"
        mock_listing.tags = '["automation", "test"]'
        mock_listing.version = "1.0.0"
        mock_listing.status = "ACTIVE"
        mock_listing.visibility = "PUBLIC"
        mock_listing.workflow_url = "https://example.com/workflow.json"
        mock_listing.builder_url = "https://example.com/builder.json"
        mock_listing.use_count = 5
        mock_listing.execution_count = 10
        mock_listing.created_at = "2024-01-01T00:00:00Z"
        mock_listing.updated_at = "2024-01-01T00:00:00Z"
        mock_listing.start_nodes = "[]"
        mock_listing.available_nodes = "[]"
        
        # Call the conversion method
        template = self.marketplace_functions._marketplace_listing_to_protobuf(mock_listing)
        
        # Verify the conversion
        assert isinstance(template, WorkflowTemplate)
        assert template.id == "listing-123"
        assert template.name == "Test Workflow Template"
        assert template.description == "A test workflow template"
        assert template.source_workflow_id == "original-workflow-456"
        assert template.owner_id == "user-123"
        assert template.category == "automation"
        assert template.version == "1.0.0"
        assert template.status == "ACTIVE"
        assert template.visibility == "PUBLIC"
        assert template.workflow_url == "https://example.com/workflow.json"
        assert template.builder_url == "https://example.com/builder.json"
        assert template.use_count == 5
        assert template.execution_count == 10

    def test_marketplace_listing_to_protobuf_with_none_workflow_id(self):
        """Test the _marketplace_listing_to_protobuf method with None workflow_id"""
        
        # Mock marketplace listing with None workflow_id
        mock_listing = MagicMock()
        mock_listing.id = "listing-123"
        mock_listing.name = "Test Workflow Template"
        mock_listing.description = "A test workflow template"
        mock_listing.workflow_id = None  # This should be handled gracefully
        mock_listing.owner_id = "user-123"
        mock_listing.category = "automation"
        mock_listing.tags = '["automation", "test"]'
        mock_listing.version = "1.0.0"
        mock_listing.status = "ACTIVE"
        mock_listing.visibility = "PUBLIC"
        mock_listing.workflow_url = "https://example.com/workflow.json"
        mock_listing.builder_url = "https://example.com/builder.json"
        mock_listing.use_count = 5
        mock_listing.execution_count = 10
        mock_listing.created_at = "2024-01-01T00:00:00Z"
        mock_listing.updated_at = "2024-01-01T00:00:00Z"
        mock_listing.start_nodes = "[]"
        mock_listing.available_nodes = "[]"
        
        # Call the conversion method
        template = self.marketplace_functions._marketplace_listing_to_protobuf(mock_listing)
        
        # Verify the conversion handles None gracefully
        assert isinstance(template, WorkflowTemplate)
        assert template.id == "listing-123"
        assert template.name == "Test Workflow Template"
        assert template.source_workflow_id == ""  # Should be empty string when None
        assert template.owner_id == "user-123"