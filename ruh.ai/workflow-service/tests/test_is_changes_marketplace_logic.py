"""
Test the is_changes_marketplace field behavior implementation.

This test verifies:
1. New cloned workflows have is_changes_marketplace=False
2. When source workflow is updated, cloned workflows get is_changes_marketplace=True
3. Pull updates endpoint resets is_changes_marketplace=False
4. Check updates endpoint returns correct status
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import Test<PERSON>lient
from app.main import app
from app.api.routers.workflow_routes import pull_updates_from_source, check_for_updates


class TestIsChangesMarketplaceLogic:
    """Test cases for is_changes_marketplace field behavior"""

    def setup_method(self):
        """Set up test fixtures"""
        self.client = TestClient(app)

    @patch('app.api.routers.workflow_routes.workflow_service')
    @patch('app.api.routers.workflow_routes.role_required')
    def test_check_updates_endpoint_with_updates_available(self, mock_role_required, mock_workflow_service):
        """Test check updates endpoint when updates are available"""
        
        # Mock authentication
        mock_role_required.return_value = lambda: {"user_id": "user-123"}
        
        # Mock workflow service response for cloned workflow with updates
        mock_workflow_response = MagicMock()
        mock_workflow_response.success = True
        mock_workflow_response.workflow = MagicMock()
        
        # Mock workflow data indicating updates are available
        mock_workflow_dict = {
            "id": "cloned-workflow-id",
            "name": "Cloned Workflow",
            "owner_id": "user-123",
            "is_imported": True,
            "workflow_template_id": "source-workflow-id",
            "is_changes_marketplace": True,  # Updates available
            "updated_at": "2024-01-01T00:00:00Z"
        }
        
        with patch('app.api.routers.workflow_routes.prepare_workflow_dict', return_value=mock_workflow_dict):
            with patch('app.api.routers.workflow_routes.MessageToDict', return_value={}):
                mock_workflow_service.get_workflow = AsyncMock(return_value=mock_workflow_response)
                
                # Test the endpoint
                response = self.client.get(
                    "/workflows/cloned-workflow-id/check-updates",
                    headers={"Authorization": "Bearer test-token"}
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] == True
                assert data["has_updates"] == True
                assert data["source_workflow_id"] == "source-workflow-id"
                assert "Updates available" in data["message"]

    @patch('app.api.routers.workflow_routes.workflow_service')
    @patch('app.api.routers.workflow_routes.role_required')
    def test_check_updates_endpoint_no_updates(self, mock_role_required, mock_workflow_service):
        """Test check updates endpoint when no updates are available"""
        
        # Mock authentication
        mock_role_required.return_value = lambda: {"user_id": "user-123"}
        
        # Mock workflow service response for cloned workflow without updates
        mock_workflow_response = MagicMock()
        mock_workflow_response.success = True
        mock_workflow_response.workflow = MagicMock()
        
        # Mock workflow data indicating no updates
        mock_workflow_dict = {
            "id": "cloned-workflow-id",
            "name": "Cloned Workflow",
            "owner_id": "user-123",
            "is_imported": True,
            "workflow_template_id": "source-workflow-id",
            "is_changes_marketplace": False,  # No updates
            "updated_at": "2024-01-01T00:00:00Z"
        }
        
        with patch('app.api.routers.workflow_routes.prepare_workflow_dict', return_value=mock_workflow_dict):
            with patch('app.api.routers.workflow_routes.MessageToDict', return_value={}):
                mock_workflow_service.get_workflow = AsyncMock(return_value=mock_workflow_response)
                
                # Test the endpoint
                response = self.client.get(
                    "/workflows/cloned-workflow-id/check-updates",
                    headers={"Authorization": "Bearer test-token"}
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] == True
                assert data["has_updates"] == False
                assert data["source_workflow_id"] == "source-workflow-id"
                assert "up to date" in data["message"]

    @patch('app.api.routers.workflow_routes.workflow_service')
    @patch('app.api.routers.workflow_routes.role_required')
    def test_check_updates_non_cloned_workflow(self, mock_role_required, mock_workflow_service):
        """Test check updates endpoint with non-cloned workflow"""
        
        # Mock authentication
        mock_role_required.return_value = lambda: {"user_id": "user-123"}
        
        # Mock workflow service response for non-cloned workflow
        mock_workflow_response = MagicMock()
        mock_workflow_response.success = True
        mock_workflow_response.workflow = MagicMock()
        
        # Mock workflow data for non-cloned workflow
        mock_workflow_dict = {
            "id": "original-workflow-id",
            "name": "Original Workflow",
            "owner_id": "user-123",
            "is_imported": False,  # Not cloned
            "workflow_template_id": None,
            "is_changes_marketplace": False
        }
        
        with patch('app.api.routers.workflow_routes.prepare_workflow_dict', return_value=mock_workflow_dict):
            with patch('app.api.routers.workflow_routes.MessageToDict', return_value={}):
                mock_workflow_service.get_workflow = AsyncMock(return_value=mock_workflow_response)
                
                # Test the endpoint
                response = self.client.get(
                    "/workflows/original-workflow-id/check-updates",
                    headers={"Authorization": "Bearer test-token"}
                )
                
                assert response.status_code == 400
                assert "not cloned from a source workflow" in response.json()["detail"]

    @patch('app.api.routers.workflow_routes.workflow_service')
    @patch('app.api.routers.workflow_routes.role_required')
    def test_pull_updates_endpoint_success(self, mock_role_required, mock_workflow_service):
        """Test pull updates endpoint successfully updates cloned workflow"""
        
        # Mock authentication
        mock_role_required.return_value = lambda: {"user_id": "user-123"}
        
        # Mock cloned workflow response
        mock_cloned_response = MagicMock()
        mock_cloned_response.success = True
        mock_cloned_response.workflow = MagicMock()
        
        mock_cloned_dict = {
            "id": "cloned-workflow-id",
            "name": "Cloned Workflow",
            "owner_id": "user-123",
            "is_imported": True,
            "workflow_template_id": "source-workflow-id",
            "is_changes_marketplace": True
        }
        
        # Mock source workflow response
        mock_source_response = MagicMock()
        mock_source_response.success = True
        mock_source_response.workflow = MagicMock()
        
        mock_source_dict = {
            "id": "source-workflow-id",
            "name": "Source Workflow",
            "description": "Updated description",
            "start_nodes": ["node1", "node2"],
            "category": "automation",
            "tags": {"type": "template"}
        }
        
        # Mock update response
        mock_update_response = MagicMock()
        mock_update_response.success = True
        mock_update_response.message = "Workflow updated successfully"
        
        with patch('app.api.routers.workflow_routes.prepare_workflow_dict') as mock_prepare:
            with patch('app.api.routers.workflow_routes.MessageToDict', return_value={}):
                with patch('app.api.routers.workflow_routes.handle_grpc_response_status'):
                    # Set up the mock to return different values for different calls
                    mock_prepare.side_effect = [mock_cloned_dict, mock_source_dict]
                    
                    # Mock workflow service methods
                    mock_workflow_service.get_workflow = AsyncMock()
                    mock_workflow_service.get_workflow.side_effect = [mock_cloned_response, mock_source_response]
                    mock_workflow_service.patch_workflow = AsyncMock(return_value=mock_update_response)
                    
                    # Test the endpoint
                    response = self.client.post(
                        "/workflows/cloned-workflow-id/pull-updates",
                        headers={"Authorization": "Bearer test-token"}
                    )
                    
                    assert response.status_code == 200
                    data = response.json()
                    assert data["success"] == True
                    assert "Successfully pulled updates" in data["message"]
                    
                    # Verify that patch_workflow was called with correct parameters
                    mock_workflow_service.patch_workflow.assert_called_once()
                    call_args = mock_workflow_service.patch_workflow.call_args
                    update_fields = call_args[1]["update_fields"]
                    assert update_fields["is_changes_marketplace"] == False
                    assert update_fields["description"] == "Updated description"

    def test_workflow_schema_includes_is_changes_marketplace(self):
        """Test that workflow schema includes is_changes_marketplace field"""
        from app.schemas.workflow import WorkflowInDB, WorkflowPatchPayload
        
        # Test that the field is included in the response schema
        workflow_data = {
            "id": "test-id",
            "name": "Test Workflow",
            "workflow_url": "http://example.com",
            "builder_url": "http://example.com",
            "start_nodes": [],
            "owner_id": "user-123",
            "user_ids": ["user-123"],
            "owner_type": "user",
            "version": "1.0.0",
            "is_changes_marketplace": True
        }
        
        workflow = WorkflowInDB.model_validate(workflow_data)
        assert workflow.is_changes_marketplace == True
        
        # Test that the field can be updated via PATCH
        patch_data = {"is_changes_marketplace": False}
        patch_payload = WorkflowPatchPayload.model_validate(patch_data)
        assert patch_payload.is_changes_marketplace == False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
