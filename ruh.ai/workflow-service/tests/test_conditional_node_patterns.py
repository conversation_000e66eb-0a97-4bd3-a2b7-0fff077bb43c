"""
Test cases for ConditionalNode following established patterns.

This module verifies that ConditionalNode follows the same patterns as
combine_text.py and merge_data.py for dynamic input/output generation.
"""

import pytest
from app.components.control_flow.conditionalNode import ConditionalNode


class TestConditionalNodePatterns:
    """Test that ConditionalNode follows established component patterns."""

    def setup_method(self):
        """Set up test fixtures."""
        self.node = ConditionalNode()

    def test_follows_dynamic_input_pattern(self):
        """Test that ConditionalNode follows the same dynamic input pattern as combine_text.py."""
        # Should have a configuration input for controlling dynamic inputs
        config_input = next(
            (inp for inp in self.node.inputs if inp.name == "num_additional_conditions"),
            None
        )
        assert config_input is not None, "Should have num_additional_conditions input like combine_text.py has num_additional_inputs"
        assert config_input.value == 0, "Should default to 0 additional conditions"

    def test_has_dynamic_condition_inputs(self):
        """Test that dynamic condition inputs are generated with proper visibility rules."""
        # Should have dynamic inputs for conditions 3-10
        for i in range(3, 11):  # 3 to 10
            condition_source = next(
                (inp for inp in self.node.inputs if inp.name == f"condition_{i}_source"),
                None
            )
            assert condition_source is not None, f"Should have condition_{i}_source input"
            assert condition_source.visibility_rules is not None, f"condition_{i}_source should have visibility rules"
            assert len(condition_source.visibility_rules) > 0, f"condition_{i}_source should have visibility rules"

    def test_has_dynamic_input_handles(self):
        """Test that dual-purpose input handles are generated for each condition."""
        # Should have primary input for condition 1
        primary_input = next(
            (inp for inp in self.node.inputs if inp.name == "primary"),
            None
        )
        assert primary_input is not None, "Should have primary dual-purpose input"
        assert hasattr(primary_input, 'is_handle'), "primary should be a handle input"
        assert primary_input.is_handle, "primary should be marked as handle"

        # Should have dual-purpose inputs for conditions 2-10
        for i in range(2, 11):  # 2 to 10
            input_handle = next(
                (inp for inp in self.node.inputs if inp.name == f"condition_{i}"),
                None
            )
            assert input_handle is not None, f"Should have condition_{i} dual-purpose input"
            assert hasattr(input_handle, 'is_handle'), f"condition_{i} should be a handle input"
            assert input_handle.is_handle, f"condition_{i} should be marked as handle"

    def test_input_handle_visibility_rules(self):
        """Test that input handles have proper visibility rules."""
        # Base conditions (1-2) should have simple visibility rules
        for i in [1, 2]:
            input_handle = next(
                (inp for inp in self.node.inputs if inp.name == f"condition_{i}_input_handle"),
                None
            )
            assert input_handle.visibility_rules is not None, f"condition_{i}_input_handle should have visibility rules"
            # Should depend on source being "node_output"
            has_source_rule = any(
                rule.field_name == f"condition_{i}_source" and rule.field_value == "node_output"
                for rule in input_handle.visibility_rules
            )
            assert has_source_rule, f"condition_{i}_input_handle should have source visibility rule"

        # Additional conditions (3-10) should have compound visibility rules
        for i in range(3, 11):
            input_handle = next(
                (inp for inp in self.node.inputs if inp.name == f"condition_{i}_input_handle"),
                None
            )
            assert input_handle.visibility_rules is not None, f"condition_{i}_input_handle should have visibility rules"
            assert len(input_handle.visibility_rules) > 1, f"condition_{i}_input_handle should have multiple visibility rules"
            
            # Should have rules for both num_additional_conditions AND source
            has_num_rule = any(
                rule.field_name == "num_additional_conditions"
                for rule in input_handle.visibility_rules
            )
            has_source_rule = any(
                rule.field_name == f"condition_{i}_source" and rule.field_value == "node_output"
                for rule in input_handle.visibility_rules
            )
            assert has_num_rule, f"condition_{i}_input_handle should have num_additional_conditions rule"
            assert has_source_rule, f"condition_{i}_input_handle should have source rule"

    def test_has_get_dynamic_outputs_method(self):
        """Test that ConditionalNode has get_dynamic_outputs method for frontend."""
        assert hasattr(self.node, 'get_dynamic_outputs'), "Should have get_dynamic_outputs method"
        
        # Test the method works
        config = {"num_additional_conditions": 2}
        dynamic_outputs = self.node.get_dynamic_outputs(config)
        
        assert len(dynamic_outputs) == 5, "Should have 4 condition outputs + 1 default = 5 total"
        
        # Check output names
        output_names = [output.name for output in dynamic_outputs]
        expected_names = [
            "condition_1_output",
            "condition_2_output", 
            "condition_3_output",
            "condition_4_output",
            "default_output"
        ]
        assert output_names == expected_names, f"Expected {expected_names}, got {output_names}"

    def test_visibility_rules_follow_combine_text_pattern(self):
        """Test that visibility rules follow the same pattern as combine_text.py."""
        # Check condition 3 source input (first additional condition)
        condition_3_source = next(
            (inp for inp in self.node.inputs if inp.name == "condition_3_source"),
            None
        )
        assert condition_3_source is not None
        assert condition_3_source.visibility_rules is not None
        
        # Should have visibility rules for num_additional_conditions >= 1
        # Following combine_text pattern: rules for values 1, 2, 3, ..., MAX
        expected_values = list(range(1, 9))  # 1 to 8 (since max additional is 8)
        actual_values = [
            rule.field_value for rule in condition_3_source.visibility_rules
            if rule.field_name == "num_additional_conditions"
        ]
        assert set(actual_values) == set(expected_values), f"Expected {expected_values}, got {actual_values}"

    def test_constants_match_pattern(self):
        """Test that constants follow the established pattern."""
        assert hasattr(self.node, 'BASE_CONDITIONS'), "Should have BASE_CONDITIONS constant"
        assert hasattr(self.node, 'MAX_ADDITIONAL_CONDITIONS'), "Should have MAX_ADDITIONAL_CONDITIONS constant"
        assert self.node.BASE_CONDITIONS == 2, "Should have 2 base conditions"
        assert self.node.MAX_ADDITIONAL_CONDITIONS == 8, "Should have max 8 additional conditions"

    def test_primary_input_is_dual_purpose(self):
        """Test that primary input follows dual-purpose pattern like combine_text.py."""
        primary_input = next(
            (inp for inp in self.node.inputs if inp.name == "primary_input_data"),
            None
        )
        assert primary_input is not None, "Should have primary_input_data"
        assert hasattr(primary_input, 'is_handle'), "Should be a dual-purpose input"
        assert primary_input.is_handle, "Should be marked as handle (dual-purpose)"

    def test_no_hardcoded_frontend_logic(self):
        """Test that component relies on backend visibility rules, not hardcoded frontend logic."""
        # This test ensures we're not hardcoding frontend logic in the component
        # All visibility should be handled by the visibility_rules on inputs
        
        # Check that all dynamic inputs have proper visibility rules
        dynamic_inputs = [
            inp for inp in self.node.inputs 
            if inp.name.startswith("condition_") and not inp.name.endswith("_input_handle")
        ]
        
        for inp in dynamic_inputs:
            if inp.name.startswith("condition_3") or inp.name.startswith("condition_4"):  # Additional conditions
                assert inp.visibility_rules is not None, f"{inp.name} should have visibility rules"
                assert len(inp.visibility_rules) > 0, f"{inp.name} should have visibility rules"


if __name__ == "__main__":
    pytest.main([__file__])
