"""
Test for node combination in workflow schema converter.
"""

import json
import os
import sys
import unittest
from pathlib import Path

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from app.services.workflow_schema_converter import convert_workflow_to_transition_schema


class TestNodeCombination(unittest.TestCase):
    """Test case for node combination in workflow schema converter."""

    def setUp(self):
        """Set up test fixtures."""
        # Path to sample workflow
        sample_workflow_path = Path(__file__).parent.parent / "testing" / "sample_workflow.json"

        # Load the sample workflow
        with open(sample_workflow_path, "r") as f:
            self.sample_workflow = json.load(f)

    def test_no_duplicate_nodes(self):
        """Test that there are no duplicate nodes in the output."""
        # Convert the sample workflow to transition schema format
        converted_schema = convert_workflow_to_transition_schema(self.sample_workflow)

        # Check if there are any duplicate node IDs
        node_ids = [node["id"] for node in converted_schema["nodes"]]
        duplicate_ids = set([node_id for node_id in node_ids if node_ids.count(node_id) > 1])

        # Assert that there are no duplicate node IDs
        self.assertEqual(len(duplicate_ids), 0, f"Found duplicate node IDs: {duplicate_ids}")

    def test_generate_audio_node_has_both_tools(self):
        """Test that the generate-audio node has both tools."""
        # Convert the sample workflow to transition schema format
        converted_schema = convert_workflow_to_transition_schema(self.sample_workflow)

        # Find the generate-audio node
        generate_audio_nodes = [
            node for node in converted_schema["nodes"] if node["id"] == "generate-audio"
        ]

        # Assert that there is exactly one generate-audio node
        self.assertEqual(
            len(generate_audio_nodes), 1, "There should be exactly one generate-audio node"
        )

        # Get the tools for the generate-audio node
        generate_audio_node = generate_audio_nodes[0]
        tool_names = [tool["tool_name"] for tool in generate_audio_node["server_tools"]]

        # Assert that the generate-audio node has both tools
        self.assertIn(
            "generate_audio", tool_names, "generate-audio node should have generate_audio tool"
        )
        self.assertIn("fetch_audio", tool_names, "generate-audio node should have fetch_audio tool")

        # Assert that the tool IDs are unique
        tool_ids = [tool["tool_id"] for tool in generate_audio_node["server_tools"]]
        self.assertEqual(len(tool_ids), len(set(tool_ids)), "Tool IDs should be unique")


if __name__ == "__main__":
    unittest.main()
