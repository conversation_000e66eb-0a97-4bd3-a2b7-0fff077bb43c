"""
Test script for the DataToDataFrameComponent in the Workflow Service.

This test verifies that the component follows modern patterns and works correctly
with both the new execute method and legacy build method.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch

from app.components.processing.data_to_dataframe import DataToDataFrameComponent
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult


class TestDataToDataFrameComponent:
    """Test cases for the DataToDataFrameComponent."""

    def setup_method(self):
        """Set up test fixtures."""
        self.component = DataToDataFrameComponent()

        # Create a mock workflow context
        self.context = Mock(spec=WorkflowContext)
        self.context.current_node_id = "test_node_1"
        self.context.node_outputs = {}
        self.context.log = Mock()

    def test_component_metadata(self):
        """Test that the component has correct metadata."""
        assert self.component.name == "DataToDataFrameComponent"
        assert self.component.display_name == "Data to DataFrame"
        assert self.component.description == "Converts data to a Pandas DataFrame."
        assert self.component.category == "Processing"
        assert self.component.icon == "Table"
        assert self.component.beta is False

    def test_component_inputs(self):
        """Test that the component has correct input definitions."""
        inputs = self.component.inputs
        assert len(inputs) == 2

        # Check input_data input
        input_data_input = inputs[0]
        assert input_data_input.name == "input_data"
        assert input_data_input.display_name == "Input Data"
        assert input_data_input.required is True
        assert "list" in input_data_input.input_types
        assert "dict" in input_data_input.input_types
        assert "Any" in input_data_input.input_types

        # Check orientation input
        orientation_input = inputs[1]
        assert orientation_input.name == "orientation"
        assert orientation_input.display_name == "Data Orientation"
        assert orientation_input.value == "auto-detect"
        assert "records" in orientation_input.options
        assert "columns" in orientation_input.options
        assert "auto-detect" in orientation_input.options

    def test_component_outputs(self):
        """Test that the component has correct output definitions."""
        outputs = self.component.outputs
        assert len(outputs) == 2

        # Check output_dataframe output
        dataframe_output = outputs[0]
        assert dataframe_output.name == "output_dataframe"
        assert dataframe_output.display_name == "DataFrame"
        assert dataframe_output.output_type == "DataFrame"

        # Check error output
        error_output = outputs[1]
        assert error_output.name == "error"
        assert error_output.display_name == "Error"
        assert error_output.output_type == "str"

    def test_get_input_value(self):
        """Test the get_input_value helper method."""
        # Test with value in node outputs
        self.context.node_outputs = {
            "test_node_1": {
                "input_data": [{"name": "Alice", "age": 30}],
                "orientation": "records"
            }
        }

        input_data = self.component.get_input_value("input_data", self.context)
        assert input_data == [{"name": "Alice", "age": 30}]

        orientation = self.component.get_input_value("orientation", self.context)
        assert orientation == "records"

        # Test with default value
        missing_value = self.component.get_input_value("missing_input", self.context, "default")
        assert missing_value == "default"

    @pytest.mark.asyncio
    async def test_execute_records_orientation(self):
        """Test execute method with records orientation."""
        # Mock input data
        test_data = [
            {"name": "Alice", "age": 30, "city": "New York"},
            {"name": "Bob", "age": 25, "city": "Los Angeles"}
        ]

        self.context.node_outputs = {
            "test_node_1": {
                "input_data": test_data,
                "orientation": "records"
            }
        }

        # Mock pandas DataFrame.from_records
        with patch('pandas.DataFrame.from_records') as mock_from_records:
            # Mock DataFrame
            mock_df = Mock()
            mock_df.shape = (2, 3)
            mock_from_records.return_value = mock_df

            result = await self.component.execute(self.context)

            assert isinstance(result, NodeResult)
            assert result.is_success() is True
            assert "output_dataframe" in result.outputs
            assert result.outputs["output_dataframe"] == mock_df
            mock_from_records.assert_called_once_with(test_data)

    @pytest.mark.asyncio
    async def test_execute_columns_orientation(self):
        """Test execute method with columns orientation."""
        # Mock input data
        test_data = {
            "name": ["Alice", "Bob"],
            "age": [30, 25],
            "city": ["New York", "Los Angeles"]
        }

        self.context.node_outputs = {
            "test_node_1": {
                "input_data": test_data,
                "orientation": "columns"
            }
        }

        # Mock pandas DataFrame
        with patch('pandas.DataFrame') as mock_dataframe:
            # Mock DataFrame
            mock_df = Mock()
            mock_df.shape = (2, 3)
            mock_dataframe.return_value = mock_df

            result = await self.component.execute(self.context)

            assert isinstance(result, NodeResult)
            assert result.is_success() is True
            assert "output_dataframe" in result.outputs
            assert result.outputs["output_dataframe"] == mock_df
            mock_dataframe.assert_called_once_with(test_data)

    @pytest.mark.asyncio
    async def test_execute_auto_detect_records(self):
        """Test execute method with auto-detect orientation (records)."""
        # Mock input data that should be detected as records
        test_data = [
            {"name": "Alice", "age": 30},
            {"name": "Bob", "age": 25}
        ]

        self.context.node_outputs = {
            "test_node_1": {
                "input_data": test_data,
                "orientation": "auto-detect"
            }
        }

        # Mock pandas DataFrame.from_records
        with patch('pandas.DataFrame.from_records') as mock_from_records:
            # Mock DataFrame
            mock_df = Mock()
            mock_df.shape = (2, 2)
            mock_from_records.return_value = mock_df

            result = await self.component.execute(self.context)

            assert isinstance(result, NodeResult)
            assert result.is_success() is True
            mock_from_records.assert_called_once_with(test_data)
            # Verify auto-detection log
            self.context.log.assert_any_call("Auto-detected orientation: records (list of dicts)")

    @pytest.mark.asyncio
    async def test_execute_auto_detect_columns(self):
        """Test execute method with auto-detect orientation (columns)."""
        # Mock input data that should be detected as columns
        test_data = {
            "name": ["Alice", "Bob"],
            "age": [30, 25]
        }

        self.context.node_outputs = {
            "test_node_1": {
                "input_data": test_data,
                "orientation": "auto-detect"
            }
        }

        # Mock pandas DataFrame
        with patch('pandas.DataFrame') as mock_dataframe:
            # Mock DataFrame
            mock_df = Mock()
            mock_df.shape = (2, 2)
            mock_dataframe.return_value = mock_df

            result = await self.component.execute(self.context)

            assert isinstance(result, NodeResult)
            assert result.is_success() is True
            mock_dataframe.assert_called_once_with(test_data)
            # Verify auto-detection log
            self.context.log.assert_any_call("Auto-detected orientation: columns (dict of lists)")

    @pytest.mark.asyncio
    async def test_execute_missing_input_data(self):
        """Test execute method with missing input data."""
        self.context.node_outputs = {
            "test_node_1": {
                "orientation": "records"
            }
        }

        result = await self.component.execute(self.context)

        assert isinstance(result, NodeResult)
        assert result.is_error() is True
        assert "Input data is missing" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_pandas_import_error(self):
        """Test execute method when pandas is not available."""
        self.context.node_outputs = {
            "test_node_1": {
                "input_data": [{"name": "Alice"}],
                "orientation": "records"
            }
        }

        # Mock the import to raise ImportError
        with patch('builtins.__import__', side_effect=ImportError("No module named 'pandas'")):
            result = await self.component.execute(self.context)

            assert isinstance(result, NodeResult)
            assert result.is_error() is True
            assert "Pandas library is not installed" in result.error_message

    def test_build_method_deprecation_warning(self):
        """Test that the build method shows deprecation warning."""
        with patch('app.components.processing.data_to_dataframe.logger') as mock_logger:
            with patch('pandas.DataFrame.from_records') as mock_from_records:
                mock_df = Mock()
                mock_df.shape = (1, 2)
                mock_from_records.return_value = mock_df

                test_data = [{"name": "Alice", "age": 30}]

                result = self.component.build(input_data=test_data, orientation="records")

                # Verify deprecation warning was logged
                mock_logger.warning.assert_called_once()
                warning_call = mock_logger.warning.call_args[0][0]
                assert "legacy build method" in warning_call.lower()
                assert "execute method" in warning_call.lower()

                # Verify the result is correct
                assert "output_dataframe" in result
                assert result["output_dataframe"] == mock_df

    def test_build_method_backward_compatibility(self):
        """Test that the build method supports old parameter names."""
        with patch('pandas.DataFrame.from_records') as mock_from_records:
            mock_df = Mock()
            mock_df.shape = (1, 2)
            mock_from_records.return_value = mock_df

            test_data = [{"name": "Alice", "age": 30}]

            # Test with old parameter name
            result = self.component.build(input_data_handle=test_data, orientation="Records (List of Dicts)")

            assert "output_dataframe" in result
            assert result["output_dataframe"] == mock_df
            mock_from_records.assert_called_once_with(test_data)

    def test_build_method_orientation_mapping(self):
        """Test that the build method correctly maps old orientation values."""
        with patch('pandas.DataFrame.from_records') as mock_from_records:
            mock_df = Mock()
            mock_df.shape = (1, 2)
            mock_from_records.return_value = mock_df

            test_data = [{"name": "Alice", "age": 30}]

            # Test orientation mapping
            result = self.component.build(input_data=test_data, orientation="Auto-Detect")

            assert "output_dataframe" in result
            mock_from_records.assert_called_once_with(test_data)


if __name__ == "__main__":
    pytest.main([__file__])
