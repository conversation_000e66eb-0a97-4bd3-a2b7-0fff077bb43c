"""
Simple test to verify is_customizable logic is working correctly.
"""

import sys
import os

# Add the current directory to the path so we can import the app modules
sys.path.append(os.getcwd())

from app.models.workflow import Workflow
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum


def test_workflow_model_defaults():
    """Test that the Workflow model has correct default values when explicitly set"""

    # Create a new workflow instance with explicit defaults (as done in the service)
    workflow = Workflow(
        name="Test Workflow",
        description="Test Description",
        workflow_url="http://example.com/workflow",
        builder_url="http://example.com/builder",
        owner_id="user-123",
        user_ids=["user-123"],
        owner_type="user",
        visibility=WorkflowVisibilityEnum.PRIVATE,
        status=WorkflowStatusEnum.ACTIVE,
        # These should be set explicitly in the service layer
        is_customizable=True,  # Default for new workflows
        is_changes_marketplace=False,  # Default for new workflows
        auto_version_on_update=False  # Default for new workflows
    )

    # Check that is_customizable is True for new workflows
    print(f"is_customizable value: {workflow.is_customizable}")
    assert workflow.is_customizable == True, f"Expected is_customizable=True, got {workflow.is_customizable}"

    # Check that is_changes_marketplace is False for new workflows
    print(f"is_changes_marketplace value: {workflow.is_changes_marketplace}")
    assert workflow.is_changes_marketplace == False, f"Expected is_changes_marketplace=False, got {workflow.is_changes_marketplace}"

    # Check that auto_version_on_update is False for new workflows
    print(f"auto_version_on_update value: {workflow.auto_version_on_update}")
    assert workflow.auto_version_on_update == False, f"Expected auto_version_on_update=False, got {workflow.auto_version_on_update}"

    print("✅ All workflow field values are correct!")


def test_workflow_customizable_assignment():
    """Test that is_customizable can be set to different values"""

    # Test setting is_customizable to False
    workflow = Workflow(
        name="Test Workflow",
        description="Test Description",
        workflow_url="http://example.com/workflow",
        builder_url="http://example.com/builder",
        owner_id="user-123",
        user_ids=["user-123"],
        owner_type="user",
        visibility=WorkflowVisibilityEnum.PRIVATE,
        status=WorkflowStatusEnum.ACTIVE,
        is_customizable=False
    )

    assert workflow.is_customizable == False, f"Expected is_customizable=False, got {workflow.is_customizable}"

    # Test setting is_customizable to True explicitly
    workflow.is_customizable = True
    assert workflow.is_customizable == True, f"Expected is_customizable=True, got {workflow.is_customizable}"

    print("✅ is_customizable field assignment works correctly!")


if __name__ == "__main__":
    print("Testing is_customizable logic...")
    test_workflow_model_defaults()
    test_workflow_customizable_assignment()
    print("🎉 All tests passed!")
