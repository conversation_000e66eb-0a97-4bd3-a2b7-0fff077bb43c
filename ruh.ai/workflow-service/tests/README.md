# Workflow Schema Converter Tests

This directory contains tests for the workflow schema converter, which converts workflow schemas to transition schemas.

## Test Files

- `test_workflow_schema.py`: Comprehensive test suite for the workflow schema converter
- `test_schema_converter.py`: Tests for schema validation and field mappings
- `run_tests.py`: <PERSON><PERSON><PERSON> to run all tests

## Running Tests

### Running All Tests

To run all tests, use the `run_tests.py` script:

```bash
python run_tests.py
```

### Running Specific Tests

To run a specific test file:

```bash
python test_workflow_schema.py
```

To run a specific test case:

```bash
python -m unittest test_workflow_schema.TestWorkflowSchemaConverter.test_field_mapping
```

## Test Coverage

The tests cover the following aspects of the workflow schema converter:

1. **Schema Validation**: Ensures that the converted schema is valid against the transition schema definition.
2. **Node IDs and Tool Names**: Verifies that node IDs and tool names are correctly mapped from the original workflow.
3. **Transition Tool Names**: Checks that tool names in transitions are correctly set based on the execution type.
4. **Field Values**: Ensures that field values are correctly set (null instead of empty string when not provided).
5. **Input Data Structure**: Verifies that input_data does not have target_node_id.
6. **Field Mapping**: Tests that field mapping from sourceHandle to targetHandle is correctly implemented.
7. **No Temporary Fields**: Ensures that there are no temporary fields (_source_handle, _target_handle) in the output.

## Adding New Tests

To add new tests, simply add new test methods to the `TestWorkflowSchemaConverter` class in `test_workflow_schema.py`. Make sure to follow the naming convention of prefixing test methods with `test_`.
