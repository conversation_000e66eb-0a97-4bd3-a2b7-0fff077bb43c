"""
Tests for separate conditional node transitions implementation.

This module tests the new architecture where conditional nodes create their own
dedicated transitions rather than being embedded within other transitions.
"""

import pytest
import os
from unittest.mock import patch
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    should_use_component_based_routing,
    create_conditional_component_routing,
    is_conditional_node
)


class TestSeparateConditionalTransitions:
    """Test suite for separate conditional node transition creation."""

    @pytest.fixture
    def sample_workflow_with_conditional(self):
        """Sample workflow with a conditional node for testing."""
        return {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {
                        "originalType": "StartNode",
                        "definition": {"name": "start"},
                        "type": "start"
                    }
                },
                {
                    "id": "processor-1", 
                    "data": {
                        "definition": {"name": "data_processor"},
                        "type": "component"
                    }
                },
                {
                    "id": "conditional-1",
                    "data": {
                        "originalType": "ConditionalNode",
                        "definition": {
                            "name": "conditional",
                            "num_conditions": 2
                        },
                        "config": {
                            "num_conditions": 2,
                            "condition_1_operator": "equals",
                            "condition_1_expected_value": "success",
                            "condition_1_source": "node_output",
                            "condition_2_operator": "contains", 
                            "condition_2_expected_value": "error",
                            "condition_2_source": "node_output"
                        },
                        "type": "conditional"
                    }
                },
                {
                    "id": "success-node",
                    "data": {
                        "definition": {"name": "success_handler"},
                        "type": "component"
                    }
                },
                {
                    "id": "error-node", 
                    "data": {
                        "definition": {"name": "error_handler"},
                        "type": "component"
                    }
                },
                {
                    "id": "default-node",
                    "data": {
                        "definition": {"name": "default_handler"},
                        "type": "component"
                    }
                }
            ],
            "edges": [
                {"id": "e1", "source": "start-1", "target": "processor-1"},
                {"id": "e2", "source": "processor-1", "target": "conditional-1"},
                {"id": "e3", "source": "conditional-1", "target": "success-node", "sourceHandle": "condition_1_output"},
                {"id": "e4", "source": "conditional-1", "target": "error-node", "sourceHandle": "condition_2_output"},
                {"id": "e5", "source": "conditional-1", "target": "default-node", "sourceHandle": "default_output"}
            ]
        }

    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_conditional_node_creates_separate_transition(self, sample_workflow_with_conditional):
        """
        Test that conditional nodes create their own dedicated transitions.
        
        Expected to FAIL initially until separate transition logic is implemented.
        """
        result = convert_workflow_to_transition_schema(sample_workflow_with_conditional)
        
        # Should have a dedicated transition for the conditional node
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-1" in t["id"]),
            None
        )
        
        assert conditional_transition is not None, "Conditional node should create its own transition"
        assert conditional_transition["id"] == "transition-conditional-1"
        assert conditional_transition["execution_type"] == "Components"

    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_conditional_transition_has_conditional_component(self, sample_workflow_with_conditional):
        """
        Test that conditional transition contains the conditional component.
        
        Expected to FAIL initially until component structure is implemented.
        """
        result = convert_workflow_to_transition_schema(sample_workflow_with_conditional)
        
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-1" in t["id"]),
            None
        )
        
        assert conditional_transition is not None
        
        # Should have conditional component in tools_to_use
        tools = conditional_transition["node_info"]["tools_to_use"]
        conditional_tool = next((tool for tool in tools if tool["tool_name"] == "conditional"), None)

        assert conditional_tool is not None, "Conditional transition should have conditional component"
        assert conditional_tool["server_id"] == "node-executor-service"
        assert "tool_params" in conditional_tool

        # Extract conditions from tool_params format
        tool_params = conditional_tool["tool_params"]
        conditions = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
                break
        assert conditions is not None, "Should have conditions in tool_params"

    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_conditional_transition_receives_input_from_previous(self, sample_workflow_with_conditional):
        """
        Test that conditional transition receives input data from previous transition.
        
        Expected to FAIL initially until input data flow is implemented.
        """
        result = convert_workflow_to_transition_schema(sample_workflow_with_conditional)
        
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-1" in t["id"]),
            None
        )
        
        assert conditional_transition is not None
        
        # Should have input_data from processor transition
        input_data = conditional_transition["node_info"]["input_data"]
        assert len(input_data) > 0, "Conditional transition should receive input data"
        
        processor_input = next(
            (inp for inp in input_data if "processor-1" in inp.get("from_transition_id", "")),
            None
        )
        assert processor_input is not None, "Should receive input from processor transition"

    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_conditional_transition_has_dynamic_output_routing(self, sample_workflow_with_conditional):
        """
        Test that conditional transition uses dynamic routing rather than static output_data.
        
        Expected to FAIL initially until dynamic routing is implemented.
        """
        result = convert_workflow_to_transition_schema(sample_workflow_with_conditional)
        
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-1" in t["id"]),
            None
        )
        
        assert conditional_transition is not None
        
        # Should have minimal or no static output_data (dynamic routing)
        output_data = conditional_transition["node_info"]["output_data"]
        
        # The conditional component should handle routing dynamically
        # So output_data should either be empty or contain routing metadata
        assert isinstance(output_data, list), "output_data should be a list"
        
        # Verify conditional component has routing parameters
        tools = conditional_transition["node_info"]["tools_to_use"]
        conditional_tool = next((tool for tool in tools if tool["tool_name"] == "conditional"), None)

        # Extract conditions from tool_params format
        tool_params = conditional_tool["tool_params"]
        conditions = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
                break
        assert len(conditions) == 2, "Should have 2 conditions configured"
        
        # Each condition should specify next_transition
        for condition in conditions:
            assert "next_transition" in condition, "Each condition should specify next_transition"
            assert condition["next_transition"].startswith("transition-"), "Should reference valid transition"

    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_workflow_flow_through_conditional_transition(self, sample_workflow_with_conditional):
        """
        Test complete workflow flow: Previous → Conditional → Target nodes.
        
        Expected to FAIL initially until complete flow is implemented.
        """
        result = convert_workflow_to_transition_schema(sample_workflow_with_conditional)
        
        # Should have transitions for: processor, conditional, success, error, default
        transition_ids = [t["id"] for t in result["transitions"]]
        
        expected_transitions = [
            "transition-processor-1",
            "transition-conditional-1", 
            "transition-success-node",
            "transition-error-node",
            "transition-default-node"
        ]
        
        for expected_id in expected_transitions:
            assert expected_id in transition_ids, f"Should have transition: {expected_id}"
        
        # Verify flow connections
        processor_transition = next(t for t in result["transitions"] if t["id"] == "transition-processor-1")
        conditional_transition = next(t for t in result["transitions"] if t["id"] == "transition-conditional-1")
        
        # Processor should connect to conditional
        processor_outputs = processor_transition["node_info"]["output_data"]
        conditional_connection = next(
            (out for out in processor_outputs if out["to_transition_id"] == "transition-conditional-1"),
            None
        )
        assert conditional_connection is not None, "Processor should connect to conditional transition"
        
        # Conditional should have routing to target transitions
        conditional_tool = next(
            tool for tool in conditional_transition["node_info"]["tools_to_use"]
            if tool["tool_name"] == "conditional"
        )

        # Extract conditions from tool_params format
        tool_params = conditional_tool["tool_params"]
        conditions = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
                break
        target_transitions = [condition["next_transition"] for condition in conditions]
        
        assert "transition-success-node" in target_transitions, "Should route to success transition"
        assert "transition-error-node" in target_transitions, "Should route to error transition"

    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_previous_transition_no_longer_has_embedded_routing(self, sample_workflow_with_conditional):
        """
        Test that previous transitions no longer contain embedded conditional routing.
        
        Expected to FAIL initially until embedded routing removal is implemented.
        """
        result = convert_workflow_to_transition_schema(sample_workflow_with_conditional)
        
        processor_transition = next(
            (t for t in result["transitions"] if "processor-1" in t["id"]),
            None
        )
        
        assert processor_transition is not None
        
        # Should NOT have conditional_routing field
        assert "conditional_routing" not in processor_transition, \
            "Previous transition should not have embedded conditional routing"
        
        # Should NOT have conditional component in tools_to_use
        tools = processor_transition["node_info"]["tools_to_use"]
        conditional_tools = [tool for tool in tools if tool["tool_name"] == "conditional"]
        assert len(conditional_tools) == 0, \
            "Previous transition should not have conditional component"

    def test_conditional_node_detection_still_works(self):
        """
        Test that conditional node detection function still works correctly.
        
        This should PASS as it doesn't require changes.
        """
        conditional_node = {
            "data": {
                "originalType": "ConditionalNode",
                "type": "conditional"
            }
        }
        
        non_conditional_node = {
            "data": {
                "type": "component"
            }
        }
        
        assert is_conditional_node(conditional_node) is True
        assert is_conditional_node(non_conditional_node) is False


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
