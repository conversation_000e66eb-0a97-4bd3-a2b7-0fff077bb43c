"""
Test cases for is_customizable field behavior in workflow service.

This test file verifies:
1. New workflows created from scratch have is_customizable=True
2. Cloned workflows inherit is_customizable from source workflow
3. Only customizable workflows can be published to marketplace
4. is_customizable field can be updated via PATCH requests
"""

import pytest
import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum


class TestIsCustomizableLogic(unittest.TestCase):
    """Test cases for is_customizable field behavior"""

    def setUp(self):
        """Set up test fixtures"""
        self.workflow_service = WorkflowFunctions()

    def test_new_workflow_is_customizable_true(self):
        """Test that new workflows created from scratch have is_customizable=True"""
        
        with patch.object(self.workflow_service, 'get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value = mock_db
            mock_db.add = MagicMock()
            mock_db.commit = MagicMock()
            mock_db.flush = MagicMock()
            mock_db.refresh = MagicMock()
            
            # Mock GCS upload service
            with patch('app.services.workflow_functions.GCSUploadService') as mock_gcs:
                mock_gcs_instance = MagicMock()
                mock_gcs.return_value = mock_gcs_instance
                mock_gcs_instance.upload_json_as_file.return_value = {
                    "publicUrl": "http://example.com/test-url"
                }
                
                # Mock workflow conversion and validation
                with patch('app.services.workflow_functions.convert_workflow_to_transition_schema') as mock_convert:
                    mock_convert.return_value = {"nodes": [], "edges": []}
                    
                    with patch('app.services.workflow_functions.validate_transition_schema') as mock_validate:
                        mock_validate.return_value = True
                        
                        # Create request for new workflow
                        request = workflow_pb2.CreateWorkflowRequest(
                            name="Test Workflow",
                            description="Test Description",
                            workflow_data='{"nodes": [], "edges": []}',
                            start_nodes=[],
                            owner=workflow_pb2.WorkflowOwner(
                                id="user-id",
                                name="Test User"
                            ),
                            owner_type=workflow_pb2.WorkflowOwnerType.USER
                        )
                        
                        # Call the service method
                        response = self.workflow_service.createWorkflow(request, MagicMock())
                        
                        # Verify workflow creation was successful
                        self.assertTrue(response.success)
                        
                        # Verify that the workflow was created with is_customizable=True
                        # Check the mock_db.add call arguments
                        mock_db.add.assert_called()
                        workflow_arg = mock_db.add.call_args[0][0]
                        self.assertIsInstance(workflow_arg, Workflow)
                        self.assertTrue(workflow_arg.is_customizable)

    def test_cloned_workflow_inherits_customizability(self):
        """Test that cloned workflows inherit is_customizable from source workflow"""
        
        with patch.object(self.workflow_service, 'get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value = mock_db
            
            # Mock marketplace listing
            mock_listing = MagicMock()
            mock_listing.id = "listing-id"
            mock_listing.workflow_id = "source-workflow-id"
            mock_listing.title = "Source Workflow"
            mock_listing.description = "Source Description"
            mock_listing.category = "automation"
            mock_listing.tags = ["test"]
            mock_listing.listed_by_user_id = "source-owner-id"
            mock_listing.use_count = 5
            
            # Mock workflow version
            mock_version = MagicMock()
            mock_version.id = "version-id"
            mock_version.workflow_url = "http://example.com/workflow"
            mock_version.builder_url = "http://example.com/builder"
            mock_version.start_nodes = []
            
            # Mock source workflow with is_customizable=False
            mock_source_workflow = MagicMock()
            mock_source_workflow.id = "source-workflow-id"
            mock_source_workflow.is_customizable = False
            
            # Set up query results
            mock_db.query().filter().first.side_effect = [
                mock_listing,  # First query for marketplace listing
                mock_version,  # Second query for workflow version
                mock_source_workflow  # Third query for source workflow
            ]
            
            mock_db.add = MagicMock()
            mock_db.commit = MagicMock()
            mock_db.flush = MagicMock()
            mock_db.refresh = MagicMock()
            
            # Create request for cloning workflow
            request = workflow_pb2.UseWorkflowRequest(
                workflow_id="listing-id",
                user_id="user-id"
            )
            
            # Call the service method
            response = self.workflow_service.useWorkflow(request, MagicMock())
            
            # Verify that the cloned workflow was created successfully
            self.assertTrue(response.success)
            
            # Verify that the cloned workflow has is_customizable=False (inherited from source)
            # Check the mock_db.add call arguments for the new workflow
            add_calls = mock_db.add.call_args_list
            workflow_calls = [call for call in add_calls if isinstance(call[0][0], Workflow)]
            self.assertTrue(len(workflow_calls) > 0)
            
            cloned_workflow = workflow_calls[0][0][0]
            self.assertFalse(cloned_workflow.is_customizable)

    def test_non_customizable_workflow_cannot_be_published(self):
        """Test that non-customizable workflows cannot be published to marketplace"""
        
        with patch.object(self.workflow_service, 'get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value = mock_db
            
            # Mock workflow with is_customizable=False
            mock_workflow = MagicMock()
            mock_workflow.id = "workflow-id"
            mock_workflow.owner_id = "user-id"
            mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
            mock_workflow.is_customizable = False
            
            mock_db.query().filter().first.return_value = mock_workflow
            
            # Create request to toggle visibility (make public)
            request = workflow_pb2.ToggleWorkflowVisibilityRequest(
                workflow_id="workflow-id",
                owner=workflow_pb2.WorkflowOwner(
                    id="user-id",
                    name="Test User"
                )
            )
            
            # Call the service method
            context = MagicMock()
            response = self.workflow_service.toggleWorkflowVisibility(request, context)
            
            # Verify that the request was rejected
            self.assertFalse(response.success)
            self.assertIn("not customizable", response.message)
            
            # Verify that the appropriate error code was set
            context.set_code.assert_called_with(workflow_pb2.grpc.StatusCode.FAILED_PRECONDITION)

    def test_customizable_workflow_can_be_published(self):
        """Test that customizable workflows can be published to marketplace"""
        
        with patch.object(self.workflow_service, 'get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value = mock_db
            
            # Mock workflow with is_customizable=True
            mock_workflow = MagicMock()
            mock_workflow.id = "workflow-id"
            mock_workflow.name = "Test Workflow"
            mock_workflow.owner_id = "user-id"
            mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
            mock_workflow.is_customizable = True
            mock_workflow.is_imported = False
            
            mock_db.query().filter().first.return_value = mock_workflow
            mock_db.add = MagicMock()
            mock_db.commit = MagicMock()
            mock_db.refresh = MagicMock()
            
            # Mock the marketplace listing creation
            with patch.object(self.workflow_service, '_create_marketplace_listing_from_workflow') as mock_create_listing:
                mock_create_listing.return_value = MagicMock()
                
                # Create request to toggle visibility (make public)
                request = workflow_pb2.ToggleWorkflowVisibilityRequest(
                    workflow_id="workflow-id",
                    owner=workflow_pb2.WorkflowOwner(
                        id="user-id",
                        name="Test User"
                    )
                )
                
                # Call the service method
                context = MagicMock()
                response = self.workflow_service.toggleWorkflowVisibility(request, context)
                
                # Verify that the request was successful
                self.assertTrue(response.success)
                self.assertIn("PUBLIC", response.message)
                
                # Verify that the workflow visibility was changed
                self.assertEqual(mock_workflow.visibility, WorkflowVisibilityEnum.PUBLIC)

    def test_is_customizable_can_be_updated_via_patch(self):
        """Test that is_customizable field can be updated via PATCH requests"""
        
        with patch.object(self.workflow_service, 'get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value = mock_db
            
            # Mock existing workflow
            mock_workflow = MagicMock()
            mock_workflow.id = "workflow-id"
            mock_workflow.owner_id = "user-id"
            mock_workflow.is_customizable = True
            
            mock_db.query().filter().first.return_value = mock_workflow
            mock_db.add = MagicMock()
            mock_db.commit = MagicMock()
            mock_db.refresh = MagicMock()
            
            # Create update request with is_customizable field
            request = workflow_pb2.UpdateWorkflowRequest(
                id="workflow-id",
                is_customizable=False,
                owner=workflow_pb2.WorkflowOwner(
                    id="user-id",
                    name="Test User"
                ),
                update_mask=workflow_pb2.FieldMask(paths=["is_customizable"])
            )
            
            # Call the service method
            response = self.workflow_service.updateWorkflow(request, MagicMock())
            
            # Verify update was successful
            self.assertTrue(response.success)
            
            # Verify that is_customizable was updated
            self.assertFalse(mock_workflow.is_customizable)


if __name__ == "__main__":
    unittest.main()
