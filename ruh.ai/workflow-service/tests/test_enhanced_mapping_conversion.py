"""
Test cases for the enhanced mapping conversion functionality.

These tests verify that the workflow schema converter correctly generates
the simplified mapping structures that align with the orchestration engine's
enhanced parameter resolution system.
"""

import unittest
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    create_enhanced_field_mapping
)


class TestEnhancedMappingConversion(unittest.TestCase):
    """Test the enhanced mapping conversion functionality."""

    def setUp(self):
        """Set up test data."""
        # Sample workflow data with MCP and component nodes
        self.sample_workflow = {
            "nodes": [
                {
                    "id": "start_node",
                    "type": "StartNode",
                    "data": {
                        "type": "start",
                        "definition": {
                            "name": "Start",
                            "originalType": "StartNode"
                        }
                    }
                },
                {
                    "id": "mcp_node",
                    "type": "CustomNode",
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "name": "Text Extractor",
                            "mcp_info": {
                                "server_id": "text_extractor_server",
                                "tool_name": "extract_text"
                            },
                            "outputs": [
                                {"name": "result", "output_type": "string"}
                            ]
                        }
                    }
                },
                {
                    "id": "component_node",
                    "type": "ComponentNode",
                    "data": {
                        "type": "component",
                        "definition": {
                            "name": "Text Processor",
                            "inputs": [
                                {"name": "input_text", "input_type": "string", "required": True}
                            ],
                            "outputs": [
                                {"name": "processed_text", "output_type": "string"}
                            ]
                        }
                    }
                }
            ],
            "edges": [
                {
                    "id": "edge1",
                    "source": "start_node",
                    "target": "mcp_node"
                },
                {
                    "id": "edge2",
                    "source": "mcp_node",
                    "target": "component_node",
                    "sourceHandle": "result",
                    "targetHandle": "input_text"
                }
            ],
            "mcp_configs": [
                {
                    "node_id": "mcp_node",
                    "selected_tool_name": "extract_text",
                    "tool_schema": {
                        "properties": {
                            "document_url": {"type": "string"}
                        }
                    },
                    "tool_args": {
                        "document_url": "https://example.com/doc.pdf"
                    }
                }
            ]
        }

    def test_enhanced_field_mapping_creation(self):
        """Test that enhanced field mappings are created correctly."""
        # Test MCP node mapping
        mcp_node_def = {
            "type": "mcp",
            "mcp_info": {"server_id": "test_server"}
        }
        
        mapping = create_enhanced_field_mapping("result", "input_text", mcp_node_def)
        
        self.assertEqual(mapping["from_field"], "result")
        self.assertEqual(mapping["to_field"], "input_text")

    def test_component_node_mapping(self):
        """Test mapping for component nodes."""
        component_node_def = {
            "type": "component",
            "name": "Test Component"
        }
        
        mapping = create_enhanced_field_mapping("output_data", "input_param", component_node_def)
        
        self.assertEqual(mapping["from_field"], "output_data")
        self.assertEqual(mapping["to_field"], "input_param")

    def test_workflow_conversion_with_mappings(self):
        """Test that workflow conversion includes mapping arrays."""
        converted = convert_workflow_to_transition_schema(self.sample_workflow)
        
        # Verify the conversion succeeded
        self.assertIn("transitions", converted)
        self.assertIn("nodes", converted)
        
        # Find the transition that should have input_data with mapping
        component_transition = None
        for transition in converted["transitions"]:
            if "component" in transition.get("node_info", {}).get("node_id", "").lower():
                component_transition = transition
                break
        
        if component_transition:
            # Verify input_data has mapping array
            input_data = component_transition["node_info"]["input_data"]
            self.assertGreater(len(input_data), 0)
            
            # Check if any input_data entry has mapping
            has_mapping = any("mapping" in entry for entry in input_data)
            self.assertTrue(has_mapping, "Expected at least one input_data entry to have mapping")
            
            # Verify mapping structure
            for entry in input_data:
                if "mapping" in entry:
                    mapping = entry["mapping"]
                    self.assertIsInstance(mapping, list)
                    self.assertGreater(len(mapping), 0)
                    
                    # Verify mapping entry structure
                    mapping_entry = mapping[0]
                    self.assertIn("from_field", mapping_entry)
                    self.assertIn("to_field", mapping_entry)
                    
                    # Verify no deprecated connection metadata
                    self.assertNotIn("priority", mapping_entry)
                    self.assertNotIn("required", mapping_entry)
                    self.assertNotIn("fallback_value", mapping_entry)
                    self.assertNotIn("handle_id", mapping_entry)
                    self.assertNotIn("connection_metadata", mapping_entry)

    def test_backward_compatibility_preserved(self):
        """Test that backward compatibility fields are still present."""
        converted = convert_workflow_to_transition_schema(self.sample_workflow)
        
        # Find a transition with input_data
        for transition in converted["transitions"]:
            input_data = transition["node_info"]["input_data"]
            for entry in input_data:
                # Should still have the basic required fields
                self.assertIn("from_transition_id", entry)
                self.assertIn("source_node_id", entry)
                self.assertIn("data_type", entry)

    def test_no_connection_metadata_generated(self):
        """Test that no deprecated connection metadata is generated."""
        converted = convert_workflow_to_transition_schema(self.sample_workflow)
        
        # Check all transitions for deprecated fields
        for transition in converted["transitions"]:
            input_data = transition["node_info"]["input_data"]
            for entry in input_data:
                if "mapping" in entry:
                    for mapping_entry in entry["mapping"]:
                        # Ensure no deprecated fields are present
                        deprecated_fields = [
                            "priority", "required", "fallback_value", 
                            "handle_id", "connection_metadata"
                        ]
                        for field in deprecated_fields:
                            self.assertNotIn(field, mapping_entry, 
                                f"Deprecated field '{field}' found in mapping")


if __name__ == "__main__":
    unittest.main()
