import pytest
import grpc
from unittest.mock import Mock, patch
from app.services.version_functions import WorkflowVersionFunctions
from app.grpc_ import workflow_pb2
from app.models.workflow import Workflow, WorkflowVersion
from app.utils.constants.constants import WorkflowVisibilityEnum


class TestListWorkflowVersionsPermissions:
    """Test permission logic for listWorkflowVersions function"""

    def setup_method(self):
        """Setup test fixtures"""
        self.service = WorkflowVersionFunctions()
        self.mock_context = Mock(spec=grpc.ServicerContext)

    @patch('app.services.version_functions.SessionLocal')
    def test_owner_can_view_private_workflow_versions(self, mock_session_local):
        """Test that workflow owner can view versions of their private workflow"""
        # Setup mock database session
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        # Create mock private workflow owned by user
        mock_workflow = Mock(spec=Workflow)
        mock_workflow.id = "workflow-123"
        mock_workflow.owner_id = "user-456"
        mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
        mock_workflow.current_version_id = "version-1"
        
        # Create mock versions
        mock_version = Mock(spec=WorkflowVersion)
        mock_version.id = "version-1"
        mock_version.workflow_id = "workflow-123"
        mock_version.version_number = "1.0.0"
        mock_version.created_at = "2023-01-01"
        
        # Setup database query mocks
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow
        mock_db.query.return_value.filter.return_value.count.return_value = 1
        mock_db.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [mock_version]
        
        # Create request from workflow owner
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id="workflow-123",
            user_id="user-456",  # Same as workflow owner_id
            page=1,
            page_size=10
        )
        
        # Mock the protobuf conversion function
        with patch('app.services.version_functions._workflow_version_to_protobuf') as mock_convert:
            mock_convert.return_value = Mock()
            
            # Call the function
            response = self.service.listWorkflowVersions(request, self.mock_context)
            
            # Assert success - owner should be able to view their private workflow versions
            assert response.success is True
            assert "Retrieved 1 versions for workflow" in response.message
            assert len(response.versions) == 1
            assert response.total == 1

    @patch('app.services.version_functions.SessionLocal')
    def test_non_owner_cannot_view_private_workflow_versions(self, mock_session_local):
        """Test that non-owners cannot view versions of private workflows"""
        # Setup mock database session
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        # Create mock private workflow owned by different user
        mock_workflow = Mock(spec=Workflow)
        mock_workflow.id = "workflow-123"
        mock_workflow.owner_id = "user-456"  # Different from requester
        mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
        
        # Setup database query mocks
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow
        
        # Create request from non-owner
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id="workflow-123",
            user_id="user-789",  # Different from workflow owner_id
            page=1,
            page_size=10
        )
        
        # Call the function
        response = self.service.listWorkflowVersions(request, self.mock_context)
        
        # Assert permission denied
        assert response.success is False
        assert "Permission denied: Cannot view versions of private workflow" in response.message
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)

    @patch('app.services.version_functions.SessionLocal')
    def test_anyone_can_view_public_workflow_versions(self, mock_session_local):
        """Test that anyone can view versions of public workflows"""
        # Setup mock database session
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        # Create mock public workflow
        mock_workflow = Mock(spec=Workflow)
        mock_workflow.id = "workflow-123"
        mock_workflow.owner_id = "user-456"
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        mock_workflow.current_version_id = "version-1"
        
        # Create mock versions
        mock_version = Mock(spec=WorkflowVersion)
        mock_version.id = "version-1"
        mock_version.workflow_id = "workflow-123"
        mock_version.version_number = "1.0.0"
        mock_version.created_at = "2023-01-01"
        
        # Setup database query mocks
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow
        mock_db.query.return_value.filter.return_value.count.return_value = 1
        mock_db.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [mock_version]
        
        # Create request from non-owner
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id="workflow-123",
            user_id="user-789",  # Different from workflow owner_id
            page=1,
            page_size=10
        )
        
        # Mock the protobuf conversion function
        with patch('app.services.version_functions._workflow_version_to_protobuf') as mock_convert:
            mock_convert.return_value = Mock()
            
            # Call the function
            response = self.service.listWorkflowVersions(request, self.mock_context)
            
            # Assert success - anyone should be able to view public workflow versions
            assert response.success is True
            assert "Retrieved 1 versions for workflow" in response.message
            assert len(response.versions) == 1
            assert response.total == 1