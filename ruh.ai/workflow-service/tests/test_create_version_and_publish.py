"""
Test suite for the new createVersionAndPublish functionality.

This test validates the new user-controlled versioning system that replaces
the automatic versioning behavior with explicit user action.
"""

import pytest
import grpc
import json
from datetime import datetime, timezone
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.db.session import SessionLocal


class TestCreateVersionAndPublish:
    """Test cases for the createVersionAndPublish method."""

    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.workflow_service = WorkflowFunctions()
        self.mock_context = Mock()
        
        # Sample workflow data
        self.sample_workflow_data = {
            "nodes": [
                {"id": "1", "type": "start", "data": {"label": "Start"}},
                {"id": "2", "type": "end", "data": {"label": "End"}}
            ],
            "edges": [
                {"id": "e1", "source": "1", "target": "2"}
            ]
        }

    def test_create_version_and_publish_success_with_marketplace(self):
        """Test successful version creation with marketplace publishing."""
        db = SessionLocal()
        try:
            # Create a test workflow with pending changes
            workflow = Workflow(
                name="Test Workflow",
                description="Test Description",
                workflow_url="https://example.com/workflow.json",
                builder_url="https://example.com/builder.json",
                start_nodes=[{"id": "start", "type": "start"}],
                available_nodes=[{"id": "node1", "type": "action"}],
                owner_id="user123",
                user_ids=["user123"],
                owner_type="user",
                is_updated=True,  # Has pending changes
                visibility=WorkflowVisibilityEnum.PUBLIC,
                status=WorkflowStatusEnum.ACTIVE
            )
            db.add(workflow)
            db.flush()
            
            # Create initial version
            initial_version = WorkflowVersion(
                workflow_id=workflow.id,
                version_number="1.0.0",
                name=workflow.name,
                description=workflow.description,
                workflow_url=workflow.workflow_url,
                builder_url=workflow.builder_url,
                start_nodes=workflow.start_nodes,
                available_nodes=workflow.available_nodes,
                changelog="Initial version"
            )
            db.add(initial_version)
            db.flush()
            
            workflow.current_version_id = initial_version.id
            db.commit()
            
            # Store workflow ID for later use
            workflow_id = workflow.id
            
            # Create request
            request = workflow_pb2.CreateVersionAndPublishRequest(
                workflow_id=str(workflow_id),
                user_id="user123",
                publish_to_marketplace=True
            )
            
            # Call the method
            response = self.workflow_service.createVersionAndPublish(request, self.mock_context)
            
            # Verify response
            assert response.success is True
            assert response.version_created is True
            assert response.marketplace_updated is True
            assert "1.1.0" in response.version_number
            assert response.version_id is not None
            assert response.marketplace_listing_id is not None
            assert "Successfully created version" in response.message
            
            # Verify workflow state - use fresh session to see committed changes
            db.close()
            db = SessionLocal()
            updated_workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
            assert updated_workflow.is_updated is False  # Should be reset
            
            # Verify new version was created
            new_version = db.query(WorkflowVersion).filter(
                WorkflowVersion.workflow_id == workflow_id,
                WorkflowVersion.version_number == "1.1.0"
            ).first()
            assert new_version is not None
            assert updated_workflow.current_version_id == new_version.id
            
            # Verify marketplace listing was created
            marketplace_listing = db.query(WorkflowMarketplaceListing).filter(
                WorkflowMarketplaceListing.workflow_id == workflow_id
            ).first()
            assert marketplace_listing is not None
            assert marketplace_listing.workflow_version_id == new_version.id
            
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()

    def test_create_version_and_publish_success_without_marketplace(self):
        """Test successful version creation without marketplace publishing."""
        db = SessionLocal()
        try:
            # Create a test workflow with pending changes
            workflow = Workflow(
                name="Test Workflow",
                description="Test Description",
                workflow_url="https://example.com/workflow.json",
                builder_url="https://example.com/builder.json",
                start_nodes=[{"id": "start", "type": "start"}],
                available_nodes=[{"id": "node1", "type": "action"}],
                owner_id="user123",
                user_ids=["user123"],
                owner_type="user",
                is_updated=True,  # Has pending changes
                visibility=WorkflowVisibilityEnum.PRIVATE,  # Private workflow
                status=WorkflowStatusEnum.ACTIVE
            )
            db.add(workflow)
            db.flush()
            
            # Create initial version
            initial_version = WorkflowVersion(
                workflow_id=workflow.id,
                version_number="1.0.0",
                name=workflow.name,
                description=workflow.description,
                workflow_url=workflow.workflow_url,
                builder_url=workflow.builder_url,
                start_nodes=workflow.start_nodes,
                available_nodes=workflow.available_nodes,
                changelog="Initial version"
            )
            db.add(initial_version)
            db.flush()
            
            workflow.current_version_id = initial_version.id
            db.commit()
            
            # Store workflow ID for later use
            workflow_id = workflow.id
            
            # Create request
            request = workflow_pb2.CreateVersionAndPublishRequest(
                workflow_id=str(workflow_id),
                user_id="user123",
                publish_to_marketplace=False
            )
            
            # Call the method
            response = self.workflow_service.createVersionAndPublish(request, self.mock_context)
            
            # Verify response
            assert response.success is True
            assert response.version_created is True
            assert response.marketplace_updated is False
            assert "1.1.0" in response.version_number
            assert response.version_id is not None
            assert response.marketplace_listing_id == ""
            
            # Verify workflow state - use fresh session to see committed changes
            db.close()
            db = SessionLocal()
            updated_workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
            assert updated_workflow.is_updated is False  # Should be reset
            
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()

    def test_create_version_and_publish_no_pending_changes(self):
        """Test when workflow has no pending changes."""
        db = SessionLocal()
        try:
            # Create a test workflow without pending changes
            workflow = Workflow(
                name="Test Workflow",
                description="Test Description",
                workflow_url="https://example.com/workflow.json",
                builder_url="https://example.com/builder.json",
                start_nodes=[{"id": "start", "type": "start"}],
                available_nodes=[{"id": "node1", "type": "action"}],
                owner_id="user123",
                user_ids=["user123"],
                owner_type="user",
                is_updated=False,  # No pending changes
                visibility=WorkflowVisibilityEnum.PUBLIC,
                status=WorkflowStatusEnum.ACTIVE
            )
            db.add(workflow)
            db.commit()
            
            # Store workflow ID for later use
            workflow_id = workflow.id
            
            # Create request
            request = workflow_pb2.CreateVersionAndPublishRequest(
                workflow_id=str(workflow_id),
                user_id="user123",
                publish_to_marketplace=True
            )
            
            # Call the method
            response = self.workflow_service.createVersionAndPublish(request, self.mock_context)
            
            # Verify response
            assert response.success is True
            assert response.version_created is False
            assert response.marketplace_updated is False
            assert "No pending changes" in response.message
            
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()

    def test_create_version_and_publish_workflow_not_found(self):
        """Test when workflow doesn't exist."""
        # Create request with non-existent workflow ID
        request = workflow_pb2.CreateVersionAndPublishRequest(
            workflow_id="non-existent-id",
            user_id="user123",
            publish_to_marketplace=True
        )
        
        # Call the method
        response = self.workflow_service.createVersionAndPublish(request, self.mock_context)
        
        # Verify response
        assert response.success is False
        assert "Workflow not found" in response.message
        
        # Verify context was set correctly
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)

    def test_create_version_and_publish_permission_denied(self):
        """Test when user is not the owner."""
        db = SessionLocal()
        try:
            # Create a test workflow
            workflow = Workflow(
                name="Test Workflow",
                description="Test Description",
                workflow_url="https://example.com/workflow.json",
                builder_url="https://example.com/builder.json",
                start_nodes=[{"id": "start", "type": "start"}],
                available_nodes=[{"id": "node1", "type": "action"}],
                owner_id="owner123",  # Different owner
                user_ids=["owner123"],
                owner_type="user",
                is_updated=True,
                visibility=WorkflowVisibilityEnum.PUBLIC,
                status=WorkflowStatusEnum.ACTIVE
            )
            db.add(workflow)
            db.commit()
            
            # Store workflow ID for later use
            workflow_id = workflow.id
            
            # Create request with different user
            request = workflow_pb2.CreateVersionAndPublishRequest(
                workflow_id=str(workflow_id),
                user_id="different_user",  # Not the owner
                publish_to_marketplace=True
            )
            
            # Call the method
            response = self.workflow_service.createVersionAndPublish(request, self.mock_context)
            
            # Verify response
            assert response.success is False
            assert "Permission denied" in response.message
            
            # Verify context was set correctly
            self.mock_context.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)
            
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()

    def test_create_version_and_publish_private_workflow_marketplace_skip(self):
        """Test marketplace publishing is skipped for private workflows."""
        db = SessionLocal()
        try:
            # Create a private workflow with pending changes
            workflow = Workflow(
                name="Test Workflow",
                description="Test Description",
                workflow_url="https://example.com/workflow.json",
                builder_url="https://example.com/builder.json",
                start_nodes=[{"id": "start", "type": "start"}],
                available_nodes=[{"id": "node1", "type": "action"}],
                owner_id="user123",
                user_ids=["user123"],
                owner_type="user",
                is_updated=True,  # Has pending changes
                visibility=WorkflowVisibilityEnum.PRIVATE,  # Private workflow
                status=WorkflowStatusEnum.ACTIVE
            )
            db.add(workflow)
            db.flush()
            
            # Create initial version
            initial_version = WorkflowVersion(
                workflow_id=workflow.id,
                version_number="1.0.0",
                name=workflow.name,
                description=workflow.description,
                workflow_url=workflow.workflow_url,
                builder_url=workflow.builder_url,
                start_nodes=workflow.start_nodes,
                available_nodes=workflow.available_nodes,
                changelog="Initial version"
            )
            db.add(initial_version)
            db.flush()
            
            workflow.current_version_id = initial_version.id
            db.commit()
            
            # Store workflow ID for later use
            workflow_id = workflow.id
            
            # Create request requesting marketplace publishing
            request = workflow_pb2.CreateVersionAndPublishRequest(
                workflow_id=str(workflow_id),
                user_id="user123",
                publish_to_marketplace=True  # Request marketplace publishing
            )
            
            # Call the method
            response = self.workflow_service.createVersionAndPublish(request, self.mock_context)
            
            # Verify response
            assert response.success is True
            assert response.version_created is True
            assert response.marketplace_updated is False  # Should be False for private workflow
            assert "marketplace update skipped - workflow is not public" in response.message
            
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()

    def test_integration_workflow_update_then_create_version(self):
        """Integration test: Update workflow (sets is_updated=True) then create version (resets is_updated=False)."""
        db = SessionLocal()
        try:
            # Create a test workflow
            workflow = Workflow(
                name="Test Workflow",
                description="Test Description",
                workflow_url="https://example.com/workflow.json",
                builder_url="https://example.com/builder.json",
                start_nodes=[{"id": "start", "type": "start"}],
                available_nodes=[{"id": "node1", "type": "action"}],
                owner_id="user123",
                user_ids=["user123"],
                owner_type="user",
                is_updated=False,  # Initially no pending changes
                visibility=WorkflowVisibilityEnum.PUBLIC,
                status=WorkflowStatusEnum.ACTIVE
            )
            db.add(workflow)
            db.commit()
            
            # Store workflow ID for later use
            workflow_id = workflow.id
            
            # Step 1: Simulate workflow update (this would normally be done via updateWorkflow)
            workflow.name = "Updated Workflow Name"
            workflow.is_updated = True  # Set by updateWorkflow when version-relevant fields change
            db.commit()
            
            # Verify workflow has pending changes
            updated_workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
            assert updated_workflow.is_updated is True
            
            # Step 2: Create version and publish
            request = workflow_pb2.CreateVersionAndPublishRequest(
                workflow_id=str(workflow_id),
                user_id="user123",
                publish_to_marketplace=True
            )
            
            response = self.workflow_service.createVersionAndPublish(request, self.mock_context)
            
            # Verify response
            assert response.success is True
            assert response.version_created is True
            assert response.marketplace_updated is True
            
            # Step 3: Verify is_updated flag was reset - use fresh session to see committed changes
            db.close()
            db = SessionLocal()
            final_workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
            assert final_workflow.is_updated is False  # Should be reset after version creation
            
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()


if __name__ == "__main__":
    pytest.main([__file__])